package com.toika.netwok.navigation

import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import androidx.compose.ui.platform.LocalContext
import com.google.firebase.auth.FirebaseAuth
import kotlinx.coroutines.tasks.await
import com.toika.netwok.auth.AuthenticationManager
import com.toika.netwok.ui.screens.ActiveStreamScreen
import com.toika.netwok.ui.screens.AddFriendScreen
import com.toika.netwok.ui.screens.EmailVerificationScreen
import com.toika.netwok.ui.screens.FirebaseTestScreen
import com.toika.netwok.ui.screens.FriendsScreen
import com.toika.netwok.ui.screens.HomeScreen
import com.toika.netwok.ui.screens.LoginScreen
import com.toika.netwok.ui.screens.ProfileScreen
import com.toika.netwok.ui.screens.RegisterScreen
import com.toika.netwok.ui.screens.ViewStreamScreen

// مسارات التنقل
object Routes {
    const val LOGIN = "login"
    const val REGISTER = "register"
    const val EMAIL_VERIFICATION = "email_verification"
    const val HOME = "home"
    const val FRIENDS = "friends"
    const val ADD_FRIEND = "add_friend"
    const val ACTIVE_STREAM = "active_stream"
    const val VIEW_STREAM = "view_stream"
    const val PROFILE = "profile"
    const val FIREBASE_TEST = "firebase_test"
}

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    val context = LocalContext.current
    val authManager = remember { AuthenticationManager(context) }

    // التحقق من حالة تسجيل الدخول (Firebase أو Google)
    val isUserSignedIn = authManager.isUserSignedIn()
    val startDestination = if (isUserSignedIn) Routes.HOME else Routes.LOGIN
    
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // شاشة تسجيل الدخول
        composable(Routes.LOGIN) {
            LoginScreen(
                onLoginSuccess = {
                    // الانتقال إلى الصفحة الرئيسية وحذف تاريخ التنقل
                    navController.navigate(Routes.HOME) {
                        popUpTo(Routes.LOGIN) { inclusive = true }
                    }
                },
                onNavigateToRegister = {
                    navController.navigate(Routes.REGISTER)
                }
            )
        }
        
        // شاشة التسجيل
        composable(Routes.REGISTER) {
            RegisterScreen(
                onRegisterSuccess = { userEmail ->
                    // الانتقال إلى شاشة التحقق من البريد الإلكتروني
                    navController.navigate("${Routes.EMAIL_VERIFICATION}/$userEmail") {
                        popUpTo(Routes.REGISTER) { inclusive = true }
                    }
                },
                onNavigateToLogin = {
                    navController.popBackStack()
                }
            )
        }

        // شاشة التحقق من البريد الإلكتروني
        composable("${Routes.EMAIL_VERIFICATION}/{userEmail}") { backStackEntry ->
            val userEmail = backStackEntry.arguments?.getString("userEmail") ?: ""
            EmailVerificationScreen(
                userEmail = userEmail,
                onVerificationComplete = {
                    // الانتقال إلى الصفحة الرئيسية بعد التحقق
                    navController.navigate(Routes.HOME) {
                        popUpTo(Routes.LOGIN) { inclusive = true }
                    }
                },
                onBackToLogin = {
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.LOGIN) { inclusive = true }
                    }
                }
            )
        }

        // الصفحة الرئيسية
        composable(Routes.HOME) {
            HomeScreen(
                onSignOut = {
                    // الانتقال إلى شاشة تسجيل الدخول وحذف تاريخ التنقل
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.HOME) { inclusive = true }
                    }
                },
                onNavigateToFriends = {
                    navController.navigate(Routes.FRIENDS)
                },
                onNavigateToProfile = {
                    navController.navigate(Routes.PROFILE)
                },
                onNavigateToActiveStream = { streamId ->
                    navController.navigate("${Routes.ACTIVE_STREAM}/$streamId")
                },
                onNavigateToViewStream = { streamId ->
                    navController.navigate("${Routes.VIEW_STREAM}/$streamId")
                }
            )
        }

        // شاشة قائمة الأصدقاء
        composable(Routes.FRIENDS) {
            FriendsScreen(
                onBackClick = {
                    navController.popBackStack()
                },
                onAddFriendClick = {
                    navController.navigate(Routes.ADD_FRIEND)
                }
            )
        }

        // شاشة إضافة صديق
        composable(Routes.ADD_FRIEND) {
            AddFriendScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }



        // شاشة البث النشط - استخدام البث المُمرر مباشرة
        composable("${Routes.ACTIVE_STREAM}/{streamId}") { backStackEntry ->
            val streamId = backStackEntry.arguments?.getString("streamId") ?: ""

            // استخدام البث من currentStream في LiveStreamManager
            val context = LocalContext.current
            val streamManager = remember { com.toika.netwok.streaming.LiveStreamManager.getInstance(context) }

            // الحصول على البث الحالي من StreamManager
            val currentStream = streamManager.getCurrentStream()

            if (currentStream != null && currentStream.streamId == streamId) {
                // البث موجود، عرض شاشة البث النشط
                ActiveStreamScreen(
                    stream = currentStream,
                    onEndStream = {
                        navController.popBackStack()
                    },
                    onBackClick = {
                        navController.popBackStack()
                    }
                )
            } else {
                // البث غير موجود، عرض رسالة خطأ
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Column(horizontalAlignment = Alignment.CenterHorizontally) {
                        Text(
                            text = "البث غير موجود أو انتهى",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.headlineSmall
                        )
                        Spacer(modifier = Modifier.height(16.dp))
                        Button(onClick = { navController.popBackStack() }) {
                            Text("رجوع")
                        }
                    }
                }
            }
        }

        // شاشة مشاهدة البث مع Firebase الحقيقي
        composable("${Routes.VIEW_STREAM}/{streamId}") { backStackEntry ->
            val streamId = backStackEntry.arguments?.getString("streamId") ?: ""
            val context = LocalContext.current

            // حالة لتخزين بيانات البث
            var streamData by remember { mutableStateOf<com.toika.netwok.data.models.LiveStream?>(null) }
            var isLoading by remember { mutableStateOf(true) }
            var errorMessage by remember { mutableStateOf<String?>(null) }

            // جلب بيانات البث من Firebase
            LaunchedEffect(streamId) {
                val streamManager = com.toika.netwok.streaming.LiveStreamManager.getInstance(context)
                try {
                    val database = com.google.firebase.database.FirebaseDatabase.getInstance()
                    val streamSnapshot = database.getReference("live_streams")
                        .child(streamId)
                        .get()
                        .await()

                    val stream = streamSnapshot.getValue(com.toika.netwok.data.models.LiveStream::class.java)
                    if (stream != null && stream.isActive) {
                        streamData = stream
                    } else {
                        errorMessage = "البث غير موجود أو غير نشط"
                    }
                } catch (e: Exception) {
                    errorMessage = "فشل في تحميل البث: ${e.message}"
                }
                isLoading = false
            }

            when {
                isLoading -> {
                    // شاشة تحميل
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        CircularProgressIndicator()
                    }
                }
                errorMessage != null -> {
                    // شاشة خطأ
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(horizontalAlignment = Alignment.CenterHorizontally) {
                            Text(
                                text = errorMessage!!,
                                color = MaterialTheme.colorScheme.error
                            )
                            Spacer(modifier = Modifier.height(16.dp))
                            Button(onClick = { navController.popBackStack() }) {
                                Text("رجوع")
                            }
                        }
                    }
                }
                streamData != null -> {
                    ViewStreamScreen(
                        stream = streamData!!,
                        onBackClick = {
                            navController.popBackStack()
                        },
                        onLeaveStream = {
                            // مغادرة البث
                        }
                    )
                }
            }
        }

        // شاشة الملف الشخصي (الإعدادات)
        composable(Routes.PROFILE) {
            ProfileScreen(
                onBackClick = {
                    navController.popBackStack()
                },
                onSignOut = {
                    // الانتقال إلى شاشة تسجيل الدخول وحذف تاريخ التنقل
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.HOME) { inclusive = true }
                    }
                }
            )
        }

        // شاشة اختبار Firebase
        composable(Routes.FIREBASE_TEST) {
            FirebaseTestScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }
    }
}
