[{"merged": "com.toika.netwok.app-debug-74:/drawable_ic_launcher_background.xml.flat", "source": "com.toika.netwok.app-main-76:/drawable/ic_launcher_background.xml"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-xxhdpi_ic_launcher.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-xxhdpi/ic_launcher.webp"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-xxxhdpi_ic_launcher.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-xxxhdpi/ic_launcher.webp"}, {"merged": "com.toika.netwok.app-debug-74:/xml_data_extraction_rules.xml.flat", "source": "com.toika.netwok.app-main-76:/xml/data_extraction_rules.xml"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-xxxhdpi_ic_launcher_round.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-xxxhdpi/ic_launcher_round.webp"}, {"merged": "com.toika.netwok.app-debug-74:/drawable_ic_launcher_foreground.xml.flat", "source": "com.toika.netwok.app-main-76:/drawable/ic_launcher_foreground.xml"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-mdpi_ic_launcher.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-mdpi/ic_launcher.webp"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-xxhdpi_ic_launcher_round.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-xxhdpi/ic_launcher_round.webp"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-hdpi_ic_launcher_round.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-hdpi/ic_launcher_round.webp"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-anydpi-v26_ic_launcher_round.xml.flat", "source": "com.toika.netwok.app-main-76:/mipmap-anydpi-v26/ic_launcher_round.xml"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-xhdpi_ic_launcher_round.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-xhdpi/ic_launcher_round.webp"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-anydpi-v26_ic_launcher.xml.flat", "source": "com.toika.netwok.app-main-76:/mipmap-anydpi-v26/ic_launcher.xml"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-xhdpi_ic_launcher.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-xhdpi/ic_launcher.webp"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-mdpi_ic_launcher_round.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-mdpi/ic_launcher_round.webp"}, {"merged": "com.toika.netwok.app-debug-74:/mipmap-hdpi_ic_launcher.webp.flat", "source": "com.toika.netwok.app-main-76:/mipmap-hdpi/ic_launcher.webp"}, {"merged": "com.toika.netwok.app-debug-74:/xml_backup_rules.xml.flat", "source": "com.toika.netwok.app-main-76:/xml/backup_rules.xml"}]