{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-72:/values-is/values-is.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,257,354,454,557,661,772", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "145,252,349,449,552,656,767,868"}, "to": {"startLines": "31,32,33,34,35,36,37,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3013,3108,3215,3312,3412,3515,3619,13815", "endColumns": "94,106,96,99,102,103,110,100", "endOffsets": "3103,3210,3307,3407,3510,3614,3725,13911"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a7d2fcfebb99ca0b7d2351010b4b169\\transformed\\play-services-basement-18.4.0\\res\\values-is\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "4876", "endColumns": "128", "endOffsets": "5000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e099880564deb176147b2205fdc84816\\transformed\\foundation-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,142", "endColumns": "86,86", "endOffsets": "137,224"}, "to": {"startLines": "137,138", "startColumns": "4,4", "startOffsets": "14181,14268", "endColumns": "86,86", "endOffsets": "14263,14350"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e71863d474cbeda869e988a1e7d78c0\\transformed\\credentials-1.2.0-rc01\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,169", "endColumns": "113,119", "endOffsets": "164,284"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2779,2893", "endColumns": "113,119", "endOffsets": "2888,3008"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09ab614e376350ca0889dc2dbf11206f\\transformed\\browser-1.4.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,159,260,366", "endColumns": "103,100,105,100", "endOffsets": "154,255,361,462"}, "to": {"startLines": "58,62,63,64", "startColumns": "4,4,4,4", "startOffsets": "6050,6437,6538,6644", "endColumns": "103,100,105,100", "endOffsets": "6149,6533,6639,6740"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7661eb1439e484e98fea3778baf664e3\\transformed\\play-services-base-18.5.0\\res\\values-is\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,445,566,671,808,929,1034,1135,1285,1387,1540,1662,1800,1950,2010,2069", "endColumns": "101,149,120,104,136,120,104,100,149,101,152,121,137,149,59,58,74", "endOffsets": "294,444,565,670,807,928,1033,1134,1284,1386,1539,1661,1799,1949,2009,2068,2143"}, "to": {"startLines": "40,41,42,43,44,45,46,47,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3902,4008,4162,4287,4396,4537,4662,4771,5005,5159,5265,5422,5548,5690,5844,5908,5971", "endColumns": "105,153,124,108,140,124,108,104,153,105,156,125,141,153,63,62,78", "endOffsets": "4003,4157,4282,4391,4532,4657,4766,4871,5154,5260,5417,5543,5685,5839,5903,5966,6045"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\549dfb7dd035cf2830ebdc27ba100054\\transformed\\appcompat-1.1.0\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,875,966,1058,1151,1245,1352,1445,1540,1635,1726,1820,1900,2010,2117,2214,2323,2423,2526,2681,2779", "endColumns": "99,96,111,84,100,113,80,79,90,91,92,93,106,92,94,94,90,93,79,109,106,96,108,99,102,154,97,79", "endOffsets": "200,297,409,494,595,709,790,870,961,1053,1146,1240,1347,1440,1535,1630,1721,1815,1895,2005,2112,2209,2318,2418,2521,2676,2774,2854"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,302,414,499,600,714,795,875,966,1058,1151,1245,1352,1445,1540,1635,1726,1820,1900,2010,2117,2214,2323,2423,2526,2681,13441", "endColumns": "99,96,111,84,100,113,80,79,90,91,92,93,106,92,94,94,90,93,79,109,106,96,108,99,102,154,97,79", "endOffsets": "200,297,409,494,595,709,790,870,961,1053,1146,1240,1347,1440,1535,1630,1721,1815,1895,2005,2112,2209,2318,2418,2521,2676,2774,13516"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ae8da29e7eccb1a8725377c96f852ddc\\transformed\\material3-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,169,281,388,500,597,696,812,953,1080,1215,1305,1406,1503,1603,1718,1844,1950,2075,2199,2341,2512,2635,2751,2870,2992,3090,3188,3297,3419,3525,3633,3736,3866,4001,4109,4214,4290,4384,4477,4591,4676,4761,4870,4950,5041,5142,5243,5338,5446,5534,5639,5740,5846,5966,6046,6148", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "164,276,383,495,592,691,807,948,1075,1210,1300,1401,1498,1598,1713,1839,1945,2070,2194,2336,2507,2630,2746,2865,2987,3085,3183,3292,3414,3520,3628,3731,3861,3996,4104,4209,4285,4379,4472,4586,4671,4756,4865,4945,5036,5137,5238,5333,5441,5529,5634,5735,5841,5961,6041,6143,6239"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6920,7034,7146,7253,7365,7462,7561,7677,7818,7945,8080,8170,8271,8368,8468,8583,8709,8815,8940,9064,9206,9377,9500,9616,9735,9857,9955,10053,10162,10284,10390,10498,10601,10731,10866,10974,11079,11155,11249,11342,11456,11541,11626,11735,11815,11906,12007,12108,12203,12311,12399,12504,12605,12711,12831,12911,13013", "endColumns": "113,111,106,111,96,98,115,140,126,134,89,100,96,99,114,125,105,124,123,141,170,122,115,118,121,97,97,108,121,105,107,102,129,134,107,104,75,93,92,113,84,84,108,79,90,100,100,94,107,87,104,100,105,119,79,101,95", "endOffsets": "7029,7141,7248,7360,7457,7556,7672,7813,7940,8075,8165,8266,8363,8463,8578,8704,8810,8935,9059,9201,9372,9495,9611,9730,9852,9950,10048,10157,10279,10385,10493,10596,10726,10861,10969,11074,11150,11244,11337,11451,11536,11621,11730,11810,11901,12002,12103,12198,12306,12394,12499,12600,12706,12826,12906,13008,13104"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f43be022de2035b39234c04c60821b4e\\transformed\\ui-release\\res\\values-is\\values-is.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,196,277,376,475,560,640,735,824,906,984,1067,1137,1212,1287,1361,1438,1506", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "191,272,371,470,555,635,730,819,901,979,1062,1132,1207,1282,1356,1433,1501,1621"}, "to": {"startLines": "38,39,59,60,61,65,66,124,125,126,127,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3730,3821,6154,6253,6352,6745,6825,13109,13198,13280,13358,13521,13591,13666,13741,13916,13993,14061", "endColumns": "90,80,98,98,84,79,94,88,81,77,82,69,74,74,73,76,67,119", "endOffsets": "3816,3897,6248,6347,6432,6820,6915,13193,13275,13353,13436,13586,13661,13736,13810,13988,14056,14176"}}]}]}