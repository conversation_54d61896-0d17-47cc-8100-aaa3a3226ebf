package com.toika.netwok

import android.util.Log
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.storage.FirebaseStorage

object FirebaseHelper {
    
    private const val TAG = "FirebaseHelper"
    
    // Firebase Analytics
    fun logEvent(analytics: FirebaseAnalytics, eventName: String, params: Map<String, Any>? = null) {
        try {
            val bundle = android.os.Bundle()
            params?.forEach { (key, value) ->
                when (value) {
                    is String -> bundle.putString(key, value)
                    is Int -> bundle.putInt(key, value)
                    is Long -> bundle.putLong(key, value)
                    is Double -> bundle.putDouble(key, value)
                    is Boolean -> bundle.putBoolean(key, value)
                }
            }
            analytics.logEvent(eventName, bundle)
            Log.d(TAG, "Event logged: $eventName")
        } catch (e: Exception) {
            Log.e(TAG, "Error logging event: ${e.message}")
        }
    }
    
    // Firebase Realtime Database
    fun writeToDatabase(path: String, data: Any) {
        try {
            val database = FirebaseDatabase.getInstance()
            val reference = database.getReference(path)
            reference.setValue(data)
                .addOnSuccessListener {
                    Log.d(TAG, "Data written successfully to: $path")
                }
                .addOnFailureListener { exception ->
                    Log.e(TAG, "Failed to write data: ${exception.message}")
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error writing to database: ${e.message}")
        }
    }
    
    fun readFromDatabase(path: String, callback: (Any?) -> Unit) {
        try {
            val database = FirebaseDatabase.getInstance()
            val reference = database.getReference(path)
            reference.get()
                .addOnSuccessListener { snapshot ->
                    Log.d(TAG, "Data read successfully from: $path")
                    callback(snapshot.value)
                }
                .addOnFailureListener { exception ->
                    Log.e(TAG, "Failed to read data: ${exception.message}")
                    callback(null)
                }
        } catch (e: Exception) {
            Log.e(TAG, "Error reading from database: ${e.message}")
            callback(null)
        }
    }
    
    // Firebase Storage
    fun getStorageReference(path: String) = FirebaseStorage.getInstance().getReference(path)
    
    // Firebase Authentication
    fun getCurrentUser() = FirebaseAuth.getInstance().currentUser

    fun isUserSignedIn() = getCurrentUser() != null

    // Test Firebase connection
    fun testFirebaseConnection() {
        Log.d(TAG, "Testing Firebase connection...")

        // Test Auth
        val currentUser = getCurrentUser()
        Log.d(TAG, "Current user: ${currentUser?.email ?: "Not signed in"}")

        // Test Database
        writeToDatabase("test/connection", mapOf(
            "timestamp" to System.currentTimeMillis(),
            "message" to "Firebase connection test",
            "status" to "connected",
            "user" to (currentUser?.email ?: "anonymous")
        ))

        // Test Storage
        val storageRef = getStorageReference("test/")
        Log.d(TAG, "Storage reference created: ${storageRef.path}")
    }
}
