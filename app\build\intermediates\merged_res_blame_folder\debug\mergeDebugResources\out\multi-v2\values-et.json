{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-63:/values-et/values-et.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,350,453,559,664,784", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "145,247,345,448,554,659,779,880"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "331,426,528,626,729,835,940,11181", "endColumns": "94,101,97,102,105,104,119,100", "endOffsets": "421,523,621,724,830,935,1055,11277"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7661eb1439e484e98fea3778baf664e3\\transformed\\play-services-base-18.5.0\\res\\values-et\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,292,450,573,677,823,948,1060,1159,1315,1419,1579,1707,1858,1999,2058,2119", "endColumns": "98,157,122,103,145,124,111,98,155,103,159,127,150,140,58,60,83", "endOffsets": "291,449,572,676,822,947,1059,1158,1314,1418,1578,1706,1857,1998,2057,2118,2202"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1233,1336,1498,1625,1733,1883,2012,2128,2371,2531,2639,2803,2935,3090,3235,3298,3363", "endColumns": "102,161,126,107,149,128,115,102,159,107,163,131,154,144,62,64,87", "endOffsets": "1331,1493,1620,1728,1878,2007,2123,2226,2526,2634,2798,2930,3085,3230,3293,3358,3446"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f43be022de2035b39234c04c60821b4e\\transformed\\ui-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,278,374,469,551,629,720,811,895,977,1062,1134,1209,1284,1356,1433,1504", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,74,74,71,76,70,121", "endOffsets": "193,273,369,464,546,624,715,806,890,972,1057,1129,1204,1279,1351,1428,1499,1621"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1060,1153,3551,3647,3742,4130,4208,10545,10636,10720,10802,10887,10959,11034,11109,11282,11359,11430", "endColumns": "92,79,95,94,81,77,90,90,83,81,84,71,74,74,71,76,70,121", "endOffsets": "1148,1228,3642,3737,3819,4203,4294,10631,10715,10797,10882,10954,11029,11104,11176,11354,11425,11547"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e71863d474cbeda869e988a1e7d78c0\\transformed\\credentials-1.2.0-rc01\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,115", "endOffsets": "160,276"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,215", "endColumns": "109,115", "endOffsets": "210,326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ae8da29e7eccb1a8725377c96f852ddc\\transformed\\material3-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,175,297,411,530,629,730,848,981,1101,1249,1336,1437,1531,1630,1746,1873,1979,2114,2247,2378,2553,2679,2798,2919,3041,3136,3233,3353,3487,3592,3695,3800,3931,4066,4174,4277,4354,4450,4546,4650,4737,4822,4928,5008,5094,5195,5299,5393,5497,5584,5693,5794,5901,6018,6098,6202", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "170,292,406,525,624,725,843,976,1096,1244,1331,1432,1526,1625,1741,1868,1974,2109,2242,2373,2548,2674,2793,2914,3036,3131,3228,3348,3482,3587,3690,3795,3926,4061,4169,4272,4349,4445,4541,4645,4732,4817,4923,5003,5089,5190,5294,5388,5492,5579,5688,5789,5896,6013,6093,6197,6296"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4299,4419,4541,4655,4774,4873,4974,5092,5225,5345,5493,5580,5681,5775,5874,5990,6117,6223,6358,6491,6622,6797,6923,7042,7163,7285,7380,7477,7597,7731,7836,7939,8044,8175,8310,8418,8521,8598,8694,8790,8894,8981,9066,9172,9252,9338,9439,9543,9637,9741,9828,9937,10038,10145,10262,10342,10446", "endColumns": "119,121,113,118,98,100,117,132,119,147,86,100,93,98,115,126,105,134,132,130,174,125,118,120,121,94,96,119,133,104,102,104,130,134,107,102,76,95,95,103,86,84,105,79,85,100,103,93,103,86,108,100,106,116,79,103,98", "endOffsets": "4414,4536,4650,4769,4868,4969,5087,5220,5340,5488,5575,5676,5770,5869,5985,6112,6218,6353,6486,6617,6792,6918,7037,7158,7280,7375,7472,7592,7726,7831,7934,8039,8170,8305,8413,8516,8593,8689,8785,8889,8976,9061,9167,9247,9333,9434,9538,9632,9736,9823,9932,10033,10140,10257,10337,10441,10540"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e099880564deb176147b2205fdc84816\\transformed\\foundation-release\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,88", "endOffsets": "140,229"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11552,11642", "endColumns": "89,88", "endOffsets": "11637,11726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09ab614e376350ca0889dc2dbf11206f\\transformed\\browser-1.4.0\\res\\values-et\\values-et.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,255,362", "endColumns": "99,99,106,98", "endOffsets": "150,250,357,456"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3451,3824,3924,4031", "endColumns": "99,99,106,98", "endOffsets": "3546,3919,4026,4125"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a7d2fcfebb99ca0b7d2351010b4b169\\transformed\\play-services-basement-18.4.0\\res\\values-et\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "135", "endOffsets": "330"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2231", "endColumns": "139", "endOffsets": "2366"}}]}]}