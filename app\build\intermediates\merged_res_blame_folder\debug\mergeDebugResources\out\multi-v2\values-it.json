{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-72:/values-it/values-it.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a7d2fcfebb99ca0b7d2351010b4b169\\transformed\\play-services-basement-18.4.0\\res\\values-it\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "131", "endOffsets": "326"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "4958", "endColumns": "135", "endOffsets": "5089"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09ab614e376350ca0889dc2dbf11206f\\transformed\\browser-1.4.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,363", "endColumns": "99,97,109,102", "endOffsets": "150,248,358,461"}, "to": {"startLines": "58,62,63,64", "startColumns": "4,4,4,4", "startOffsets": "6181,6566,6664,6774", "endColumns": "99,97,109,102", "endOffsets": "6276,6659,6769,6872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\549dfb7dd035cf2830ebdc27ba100054\\transformed\\appcompat-1.1.0\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,884,976,1069,1162,1256,1358,1452,1549,1644,1736,1828,1908,2014,2121,2219,2323,2429,2536,2699,2799", "endColumns": "104,102,108,83,104,118,77,75,91,92,92,93,101,93,96,94,91,91,79,105,106,97,103,105,106,162,99,80", "endOffsets": "205,308,417,501,606,725,803,879,971,1064,1157,1251,1353,1447,1544,1639,1731,1823,1903,2009,2116,2214,2318,2424,2531,2694,2794,2875"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,210,313,422,506,611,730,808,884,976,1069,1162,1256,1358,1452,1549,1644,1736,1828,1908,2014,2121,2219,2323,2429,2536,2699,13844", "endColumns": "104,102,108,83,104,118,77,75,91,92,92,93,101,93,96,94,91,91,79,105,106,97,103,105,106,162,99,80", "endOffsets": "205,308,417,501,606,725,803,879,971,1064,1157,1251,1353,1447,1544,1639,1731,1823,1903,2009,2116,2214,2318,2424,2531,2694,2794,13920"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7661eb1439e484e98fea3778baf664e3\\transformed\\play-services-base-18.5.0\\res\\values-it\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,294,441,562,666,820,944,1060,1160,1313,1416,1566,1689,1841,2018,2081,2138", "endColumns": "100,146,120,103,153,123,115,99,152,102,149,122,151,176,62,56,72", "endOffsets": "293,440,561,665,819,943,1059,1159,1312,1415,1565,1688,1840,2017,2080,2137,2210"}, "to": {"startLines": "40,41,42,43,44,45,46,47,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3959,4064,4215,4340,4448,4606,4734,4854,5094,5251,5358,5512,5639,5795,5976,6043,6104", "endColumns": "104,150,124,107,157,127,119,103,156,106,153,126,155,180,66,60,76", "endOffsets": "4059,4210,4335,4443,4601,4729,4849,4953,5246,5353,5507,5634,5790,5971,6038,6099,6176"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,354,456,565,672,802", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "148,250,349,451,560,667,797,898"}, "to": {"startLines": "31,32,33,34,35,36,37,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3025,3123,3225,3324,3426,3535,3642,14228", "endColumns": "97,101,98,101,108,106,129,100", "endOffsets": "3118,3220,3319,3421,3530,3637,3767,14324"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ae8da29e7eccb1a8725377c96f852ddc\\transformed\\material3-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,182,306,428,552,657,753,866,1009,1128,1286,1370,1482,1576,1676,1795,1917,2034,2176,2316,2459,2635,2770,2890,3013,3143,3238,3335,3462,3600,3700,3810,3916,4059,4207,4317,4418,4507,4603,4696,4811,4897,4983,5086,5166,5249,5348,5454,5554,5655,5743,5853,5953,6058,6176,6256,6370", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "177,301,423,547,652,748,861,1004,1123,1281,1365,1477,1571,1671,1790,1912,2029,2171,2311,2454,2630,2765,2885,3008,3138,3233,3330,3457,3595,3695,3805,3911,4054,4202,4312,4413,4502,4598,4691,4806,4892,4978,5081,5161,5244,5343,5449,5549,5650,5738,5848,5948,6053,6171,6251,6365,6472"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7062,7189,7313,7435,7559,7664,7760,7873,8016,8135,8293,8377,8489,8583,8683,8802,8924,9041,9183,9323,9466,9642,9777,9897,10020,10150,10245,10342,10469,10607,10707,10817,10923,11066,11214,11324,11425,11514,11610,11703,11818,11904,11990,12093,12173,12256,12355,12461,12561,12662,12750,12860,12960,13065,13183,13263,13377", "endColumns": "126,123,121,123,104,95,112,142,118,157,83,111,93,99,118,121,116,141,139,142,175,134,119,122,129,94,96,126,137,99,109,105,142,147,109,100,88,95,92,114,85,85,102,79,82,98,105,99,100,87,109,99,104,117,79,113,106", "endOffsets": "7184,7308,7430,7554,7659,7755,7868,8011,8130,8288,8372,8484,8578,8678,8797,8919,9036,9178,9318,9461,9637,9772,9892,10015,10145,10240,10337,10464,10602,10702,10812,10918,11061,11209,11319,11420,11509,11605,11698,11813,11899,11985,12088,12168,12251,12350,12456,12556,12657,12745,12855,12955,13060,13178,13258,13372,13479"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f43be022de2035b39234c04c60821b4e\\transformed\\ui-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,205,292,390,490,577,656,762,855,950,1034,1122,1207,1283,1355,1425,1503,1572", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "200,287,385,485,572,651,757,850,945,1029,1117,1202,1278,1350,1420,1498,1567,1688"}, "to": {"startLines": "38,39,59,60,61,65,66,124,125,126,127,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3772,3872,6281,6379,6479,6877,6956,13484,13577,13672,13756,13925,14010,14086,14158,14329,14407,14476", "endColumns": "99,86,97,99,86,78,105,92,94,83,87,84,75,71,69,77,68,120", "endOffsets": "3867,3954,6374,6474,6561,6951,7057,13572,13667,13751,13839,14005,14081,14153,14223,14402,14471,14592"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e71863d474cbeda869e988a1e7d78c0\\transformed\\credentials-1.2.0-rc01\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,167", "endColumns": "111,113", "endOffsets": "162,276"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2799,2911", "endColumns": "111,113", "endOffsets": "2906,3020"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e099880564deb176147b2205fdc84816\\transformed\\foundation-release\\res\\values-it\\values-it.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,153", "endColumns": "97,98", "endOffsets": "148,247"}, "to": {"startLines": "137,138", "startColumns": "4,4", "startOffsets": "14597,14695", "endColumns": "97,98", "endOffsets": "14690,14789"}}]}]}