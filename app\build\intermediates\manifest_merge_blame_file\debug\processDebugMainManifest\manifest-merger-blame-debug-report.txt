1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.toika.netwok"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="35" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\kotln project\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\kotln project\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
12-->D:\kotln project\app\src\main\AndroidManifest.xml:6:5-79
12-->D:\kotln project\app\src\main\AndroidManifest.xml:6:22-76
13
14    <!-- Screen sharing and Audio permissions -->
15    <uses-permission android:name="android.permission.RECORD_AUDIO" />
15-->D:\kotln project\app\src\main\AndroidManifest.xml:9:5-71
15-->D:\kotln project\app\src\main\AndroidManifest.xml:9:22-68
16    <uses-permission android:name="android.permission.MODIFY_AUDIO_SETTINGS" />
16-->D:\kotln project\app\src\main\AndroidManifest.xml:10:5-80
16-->D:\kotln project\app\src\main\AndroidManifest.xml:10:22-77
17    <uses-permission android:name="android.permission.WAKE_LOCK" />
17-->D:\kotln project\app\src\main\AndroidManifest.xml:11:5-68
17-->D:\kotln project\app\src\main\AndroidManifest.xml:11:22-65
18    <uses-permission android:name="android.permission.FOREGROUND_SERVICE" />
18-->D:\kotln project\app\src\main\AndroidManifest.xml:12:5-77
18-->D:\kotln project\app\src\main\AndroidManifest.xml:12:22-74
19    <uses-permission android:name="android.permission.SYSTEM_ALERT_WINDOW" />
19-->D:\kotln project\app\src\main\AndroidManifest.xml:13:5-78
19-->D:\kotln project\app\src\main\AndroidManifest.xml:13:22-75
20
21    <!-- Audio features -->
22    <uses-feature
22-->D:\kotln project\app\src\main\AndroidManifest.xml:16:5-88
23        android:name="android.hardware.microphone"
23-->D:\kotln project\app\src\main\AndroidManifest.xml:16:19-61
24        android:required="true" />
24-->D:\kotln project\app\src\main\AndroidManifest.xml:16:62-85
25
26    <uses-permission android:name="com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE" />
26-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
26-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
27    <uses-permission android:name="com.google.android.gms.permission.AD_ID" />
27-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
27-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
28    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_ATTRIBUTION" />
28-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
28-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
29    <uses-permission android:name="android.permission.ACCESS_ADSERVICES_AD_ID" />
29-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
29-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
30    <uses-permission android:name="com.google.android.providers.gsf.permission.READ_GSERVICES" />
30-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
30-->[com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
31
32    <permission
32-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
33        android:name="com.toika.netwok.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.toika.netwok.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
37
38    <application
38-->D:\kotln project\app\src\main\AndroidManifest.xml:18:5-47:19
39        android:name="com.toika.netwok.MyApplication"
39-->D:\kotln project\app\src\main\AndroidManifest.xml:19:9-38
40        android:allowBackup="true"
40-->D:\kotln project\app\src\main\AndroidManifest.xml:20:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
42        android:dataExtractionRules="@xml/data_extraction_rules"
42-->D:\kotln project\app\src\main\AndroidManifest.xml:21:9-65
43        android:debuggable="true"
44        android:extractNativeLibs="false"
45        android:fullBackupContent="@xml/backup_rules"
45-->D:\kotln project\app\src\main\AndroidManifest.xml:22:9-54
46        android:icon="@mipmap/ic_launcher"
46-->D:\kotln project\app\src\main\AndroidManifest.xml:23:9-43
47        android:label="@string/app_name"
47-->D:\kotln project\app\src\main\AndroidManifest.xml:24:9-41
48        android:roundIcon="@mipmap/ic_launcher_round"
48-->D:\kotln project\app\src\main\AndroidManifest.xml:25:9-54
49        android:supportsRtl="true"
49-->D:\kotln project\app\src\main\AndroidManifest.xml:26:9-35
50        android:theme="@style/Theme.Anime" >
50-->D:\kotln project\app\src\main\AndroidManifest.xml:27:9-43
51        <activity
51-->D:\kotln project\app\src\main\AndroidManifest.xml:29:9-39:20
52            android:name="com.toika.netwok.MainActivity"
52-->D:\kotln project\app\src\main\AndroidManifest.xml:30:13-41
53            android:exported="true"
53-->D:\kotln project\app\src\main\AndroidManifest.xml:31:13-36
54            android:label="@string/app_name"
54-->D:\kotln project\app\src\main\AndroidManifest.xml:32:13-45
55            android:theme="@style/Theme.Anime" >
55-->D:\kotln project\app\src\main\AndroidManifest.xml:33:13-47
56            <intent-filter>
56-->D:\kotln project\app\src\main\AndroidManifest.xml:34:13-38:29
57                <action android:name="android.intent.action.MAIN" />
57-->D:\kotln project\app\src\main\AndroidManifest.xml:35:17-69
57-->D:\kotln project\app\src\main\AndroidManifest.xml:35:25-66
58
59                <category android:name="android.intent.category.LAUNCHER" />
59-->D:\kotln project\app\src\main\AndroidManifest.xml:37:17-77
59-->D:\kotln project\app\src\main\AndroidManifest.xml:37:27-74
60            </intent-filter>
61        </activity>
62
63        <!-- خدمة مشاركة الشاشة -->
64        <service
64-->D:\kotln project\app\src\main\AndroidManifest.xml:42:9-46:63
65            android:name="com.toika.netwok.streaming.ScreenCaptureService"
65-->D:\kotln project\app\src\main\AndroidManifest.xml:43:13-59
66            android:enabled="true"
66-->D:\kotln project\app\src\main\AndroidManifest.xml:44:13-35
67            android:exported="false"
67-->D:\kotln project\app\src\main\AndroidManifest.xml:45:13-37
68            android:foregroundServiceType="mediaProjection" />
68-->D:\kotln project\app\src\main\AndroidManifest.xml:46:13-60
69        <service
69-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
70            android:name="androidx.camera.core.impl.MetadataHolderService"
70-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
71            android:enabled="false"
71-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
72            android:exported="false" >
72-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
73            <meta-data
73-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
74                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
74-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
75                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
75-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
76        </service>
77
78        <activity
78-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
79            android:name="com.google.firebase.auth.internal.GenericIdpActivity"
79-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
80            android:excludeFromRecents="true"
80-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
81            android:exported="true"
81-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
82            android:launchMode="singleTask"
82-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
83            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
83-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
84            <intent-filter>
84-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
85                <action android:name="android.intent.action.VIEW" />
85-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
85-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
86
87                <category android:name="android.intent.category.DEFAULT" />
87-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
87-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
88                <category android:name="android.intent.category.BROWSABLE" />
88-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
88-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
89
90                <data
90-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
91                    android:host="firebase.auth"
91-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
92                    android:path="/"
92-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
93                    android:scheme="genericidp" />
93-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
94            </intent-filter>
95        </activity>
96        <activity
96-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
97            android:name="com.google.firebase.auth.internal.RecaptchaActivity"
97-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
98            android:excludeFromRecents="true"
98-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
99            android:exported="true"
99-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
100            android:launchMode="singleTask"
100-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
101            android:theme="@android:style/Theme.Translucent.NoTitleBar" >
101-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
102            <intent-filter>
102-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
103                <action android:name="android.intent.action.VIEW" />
103-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
103-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
104
105                <category android:name="android.intent.category.DEFAULT" />
105-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
105-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
106                <category android:name="android.intent.category.BROWSABLE" />
106-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
106-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
107
108                <data
108-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
109                    android:host="firebase.auth"
109-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
110                    android:path="/"
110-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
111                    android:scheme="recaptcha" />
111-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
112            </intent-filter>
113        </activity>
114
115        <service
115-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
116            android:name="com.google.firebase.components.ComponentDiscoveryService"
116-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:67:13-84
117            android:directBootAware="true"
117-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
118            android:exported="false" >
118-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:68:13-37
119            <meta-data
119-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
120                android:name="com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar"
120-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
121                android:value="com.google.firebase.components.ComponentRegistrar" />
121-->[com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
122            <meta-data
122-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
123                android:name="com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar"
123-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
124                android:value="com.google.firebase.components.ComponentRegistrar" />
124-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
125            <meta-data
125-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
126                android:name="com.google.firebase.components:com.google.firebase.storage.StorageRegistrar"
126-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
127                android:value="com.google.firebase.components.ComponentRegistrar" />
127-->[com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
128            <meta-data
128-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
129                android:name="com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar"
129-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
130                android:value="com.google.firebase.components.ComponentRegistrar" />
130-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
131            <meta-data
131-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
132                android:name="com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar"
132-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
133                android:value="com.google.firebase.components.ComponentRegistrar" />
133-->[com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
134            <meta-data
134-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
135                android:name="com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar"
135-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
136                android:value="com.google.firebase.components.ComponentRegistrar" />
136-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
137            <meta-data
137-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
138                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar"
138-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
139                android:value="com.google.firebase.components.ComponentRegistrar" />
139-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
140            <meta-data
140-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
141                android:name="com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar"
141-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
142                android:value="com.google.firebase.components.ComponentRegistrar" />
142-->[com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
143            <meta-data
143-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
144                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar"
144-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
145                android:value="com.google.firebase.components.ComponentRegistrar" />
145-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
146            <meta-data
146-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
147                android:name="com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar"
147-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
148                android:value="com.google.firebase.components.ComponentRegistrar" />
148-->[com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
149            <meta-data
149-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
150                android:name="com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar"
150-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
151                android:value="com.google.firebase.components.ComponentRegistrar" />
151-->[com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
152            <meta-data
152-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
153                android:name="com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar"
153-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
154                android:value="com.google.firebase.components.ComponentRegistrar" />
154-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
155        </service>
156        <service
156-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
157            android:name="androidx.credentials.playservices.CredentialProviderMetadataHolder"
157-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
158            android:enabled="true"
158-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
159            android:exported="false" >
159-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
160            <meta-data
160-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
161                android:name="androidx.credentials.CREDENTIAL_PROVIDER_KEY"
161-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
162                android:value="androidx.credentials.playservices.CredentialProviderPlayServicesImpl" />
162-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
163        </service>
164
165        <activity
165-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
166            android:name="androidx.credentials.playservices.HiddenActivity"
166-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
167            android:configChanges="orientation|screenSize|screenLayout|keyboardHidden"
167-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
168            android:enabled="true"
168-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
169            android:exported="false"
169-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
170            android:fitsSystemWindows="true"
170-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
171            android:theme="@style/Theme.Hidden" >
171-->[androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
172        </activity>
173        <activity
173-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
174            android:name="com.google.android.gms.auth.api.signin.internal.SignInHubActivity"
174-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
175            android:excludeFromRecents="true"
175-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
176            android:exported="false"
176-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
177            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
177-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
178        <!--
179            Service handling Google Sign-In user revocation. For apps that do not integrate with
180            Google Sign-In, this service will never be started.
181        -->
182        <service
182-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
183            android:name="com.google.android.gms.auth.api.signin.RevocationBoundService"
183-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
184            android:exported="true"
184-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
185            android:permission="com.google.android.gms.auth.api.signin.permission.REVOCATION_NOTIFICATION"
185-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
186            android:visibleToInstantApps="true" />
186-->[com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
187
188        <receiver
188-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
189            android:name="com.google.android.gms.measurement.AppMeasurementReceiver"
189-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
190            android:enabled="true"
190-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
191            android:exported="false" >
191-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
192        </receiver>
193
194        <service
194-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
195            android:name="com.google.android.gms.measurement.AppMeasurementService"
195-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
196            android:enabled="true"
196-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
197            android:exported="false" />
197-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
198        <service
198-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
199            android:name="com.google.android.gms.measurement.AppMeasurementJobService"
199-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
200            android:enabled="true"
200-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
201            android:exported="false"
201-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
202            android:permission="android.permission.BIND_JOB_SERVICE" />
202-->[com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
203
204        <property
204-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
205            android:name="android.adservices.AD_SERVICES_CONFIG"
205-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
206            android:resource="@xml/ga_ad_services_config" />
206-->[com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
207
208        <provider
208-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
209            android:name="com.google.firebase.provider.FirebaseInitProvider"
209-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
210            android:authorities="com.toika.netwok.firebaseinitprovider"
210-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
211            android:directBootAware="true"
211-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
212            android:exported="false"
212-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
213            android:initOrder="100" />
213-->[com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
214
215        <activity
215-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
216            android:name="androidx.activity.ComponentActivity"
216-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
217            android:exported="true" />
217-->[androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
218        <activity
218-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
219            android:name="androidx.compose.ui.tooling.PreviewActivity"
219-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
220            android:exported="true" />
220-->[androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
221
222        <provider
222-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
223            android:name="androidx.startup.InitializationProvider"
223-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
224            android:authorities="com.toika.netwok.androidx-startup"
224-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
225            android:exported="false" >
225-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
226            <meta-data
226-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
227                android:name="androidx.emoji2.text.EmojiCompatInitializer"
227-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
228                android:value="androidx.startup" />
228-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
229            <meta-data
229-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
230                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
230-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
231                android:value="androidx.startup" />
231-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
232            <meta-data
232-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
233                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
233-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
234                android:value="androidx.startup" />
234-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
235        </provider>
236
237        <activity
237-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
238            android:name="com.google.android.gms.common.api.GoogleApiActivity"
238-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
239            android:exported="false"
239-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
240            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
240-->[com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
241
242        <uses-library
242-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
243            android:name="android.ext.adservices"
243-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
244            android:required="false" />
244-->[androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
245
246        <meta-data
246-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
247            android:name="com.google.android.gms.version"
247-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
248            android:value="@integer/google_play_services_version" />
248-->[com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
249
250        <receiver
250-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
251            android:name="androidx.profileinstaller.ProfileInstallReceiver"
251-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
252            android:directBootAware="false"
252-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
253            android:enabled="true"
253-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
254            android:exported="true"
254-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
255            android:permission="android.permission.DUMP" >
255-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
256            <intent-filter>
256-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
257                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
257-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
258            </intent-filter>
259            <intent-filter>
259-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
260                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
260-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
261            </intent-filter>
262            <intent-filter>
262-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
263                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
263-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
264            </intent-filter>
265            <intent-filter>
265-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
266                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
266-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
267            </intent-filter>
268        </receiver> <!-- The activities will be merged into the manifest of the hosting app. -->
269        <activity
269-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
270            android:name="com.google.android.play.core.common.PlayCoreDialogWrapperActivity"
270-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
271            android:exported="false"
271-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
272            android:stateNotNeeded="true"
272-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
273            android:theme="@style/Theme.PlayCore.Transparent" />
273-->[com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
274    </application>
275
276</manifest>
