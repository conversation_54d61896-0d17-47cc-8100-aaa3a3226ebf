{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-63:/values-ar/values-ar.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7661eb1439e484e98fea3778baf664e3\\transformed\\play-services-base-18.5.0\\res\\values-ar\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,293,433,551,652,786,910,1017,1115,1248,1348,1494,1612,1747,1889,1949,2011", "endColumns": "99,139,117,100,133,123,106,97,132,99,145,117,134,141,59,61,79", "endOffsets": "292,432,550,651,785,909,1016,1114,1247,1347,1493,1611,1746,1888,1948,2010,2090"}, "to": {"startLines": "13,14,15,16,17,18,19,20,22,23,24,25,26,27,28,29,30", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1223,1327,1471,1593,1698,1836,1964,2075,2307,2444,2548,2698,2820,2959,3105,3169,3235", "endColumns": "103,143,121,104,137,127,110,101,136,103,149,121,138,145,63,65,83", "endOffsets": "1322,1466,1588,1693,1831,1959,2070,2172,2439,2543,2693,2815,2954,3100,3164,3230,3314"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09ab614e376350ca0889dc2dbf11206f\\transformed\\browser-1.4.0\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,361", "endColumns": "99,97,107,101", "endOffsets": "150,248,356,458"}, "to": {"startLines": "31,35,36,37", "startColumns": "4,4,4,4", "startOffsets": "3319,3697,3795,3903", "endColumns": "99,97,107,101", "endOffsets": "3414,3790,3898,4000"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e71863d474cbeda869e988a1e7d78c0\\transformed\\credentials-1.2.0-rc01\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,170", "endColumns": "114,118", "endOffsets": "165,284"}, "to": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "105,220", "endColumns": "114,118", "endOffsets": "215,334"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f43be022de2035b39234c04c60821b4e\\transformed\\ui-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,194,277,372,470,555,636,742,826,907,988,1071,1141,1220,1299,1373,1449,1523", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,78,73,75,73,120", "endOffsets": "189,272,367,465,550,631,737,821,902,983,1066,1136,1215,1294,1368,1444,1518,1639"}, "to": {"startLines": "11,12,32,33,34,38,39,97,98,99,100,101,102,103,104,106,107,108", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1051,1140,3419,3514,3612,4005,4086,10454,10538,10619,10700,10783,10853,10932,11011,11186,11262,11336", "endColumns": "88,82,94,97,84,80,105,83,80,80,82,69,78,78,73,75,73,120", "endOffsets": "1135,1218,3509,3607,3692,4081,4187,10533,10614,10695,10778,10848,10927,11006,11080,11257,11331,11452"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e099880564deb176147b2205fdc84816\\transformed\\foundation-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,139", "endColumns": "83,85", "endOffsets": "134,220"}, "to": {"startLines": "109,110", "startColumns": "4,4", "startOffsets": "11457,11541", "endColumns": "83,85", "endOffsets": "11536,11622"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,148,250,345,448,551,653,767", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "143,245,340,443,546,648,762,863"}, "to": {"startLines": "4,5,6,7,8,9,10,105", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "339,432,534,629,732,835,937,11085", "endColumns": "92,101,94,102,102,101,113,100", "endOffsets": "427,529,624,727,830,932,1046,11181"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a7d2fcfebb99ca0b7d2351010b4b169\\transformed\\play-services-basement-18.4.0\\res\\values-ar\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "21", "startColumns": "4", "startOffsets": "2177", "endColumns": "129", "endOffsets": "2302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ae8da29e7eccb1a8725377c96f852ddc\\transformed\\material3-release\\res\\values-ar\\values-ar.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,290,400,516,614,719,842,979,1100,1243,1330,1435,1527,1627,1745,1871,1981,2127,2271,2408,2560,2686,2806,2929,3047,3140,3238,3361,3485,3585,3688,3796,3941,4091,4198,4300,4380,4474,4567,4684,4773,4858,4958,5037,5121,5222,5325,5424,5522,5609,5715,5815,5915,6044,6123,6224", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "168,285,395,511,609,714,837,974,1095,1238,1325,1430,1522,1622,1740,1866,1976,2122,2266,2403,2555,2681,2801,2924,3042,3135,3233,3356,3480,3580,3683,3791,3936,4086,4193,4295,4375,4469,4562,4679,4768,4853,4953,5032,5116,5217,5320,5419,5517,5604,5710,5810,5910,6039,6118,6219,6312"}, "to": {"startLines": "40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4192,4310,4427,4537,4653,4751,4856,4979,5116,5237,5380,5467,5572,5664,5764,5882,6008,6118,6264,6408,6545,6697,6823,6943,7066,7184,7277,7375,7498,7622,7722,7825,7933,8078,8228,8335,8437,8517,8611,8704,8821,8910,8995,9095,9174,9258,9359,9462,9561,9659,9746,9852,9952,10052,10181,10260,10361", "endColumns": "117,116,109,115,97,104,122,136,120,142,86,104,91,99,117,125,109,145,143,136,151,125,119,122,117,92,97,122,123,99,102,107,144,149,106,101,79,93,92,116,88,84,99,78,83,100,102,98,97,86,105,99,99,128,78,100,92", "endOffsets": "4305,4422,4532,4648,4746,4851,4974,5111,5232,5375,5462,5567,5659,5759,5877,6003,6113,6259,6403,6540,6692,6818,6938,7061,7179,7272,7370,7493,7617,7717,7820,7928,8073,8223,8330,8432,8512,8606,8699,8816,8905,8990,9090,9169,9253,9354,9457,9556,9654,9741,9847,9947,10047,10176,10255,10356,10449"}}]}]}