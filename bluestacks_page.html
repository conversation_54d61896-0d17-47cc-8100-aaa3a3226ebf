<!doctype html>
<html lang="" data-country-code="IQ">
<script>
var domain = 'bluestacks.com';
if (
  location.hostname.includes('bluestacks.com')
) {
  domain = 'bluestacks.com';
} else if (location.hostname === 'local.bluestacks.com') {
  domain = 'local.bluestacks.com';
} else if (location.hostname === 'localhost') {
  domain = 'localhost';
} else {
  domain = 'bstkinternal.net';
}

var setCookie = (cookieName, cookieValue, daysToExpire) => {
  var date = new Date();
  date.setTime(date.getTime() + daysToExpire * 24 * 60 * 60 * 1000);
  var expires = 'expires=' + date.toUTCString();
  document.cookie = cookieName + '=' + cookieValue + ';' + expires + ';' + domain + ';path=/';
};


function getCookie(cname) {
  var name = cname + "=";
  var decodedCookie = decodeURIComponent(document.cookie);
  var ca = decodedCookie.split(';');
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
  return "";
}
if (!getCookie('experiment_version') && (location.pathname == '/' || location.pathname.includes('index.html') || location.pathname == '/download.html') && (
    getCookie('preferred_lang') == 'en' || getCookie(
      'preferred_lang') == '')) {
  setCookie('experiment_version', 'experiment_variant', 7);
}

var htmlEle = document.querySelector('html');
htmlEle.classList.add('experiment_variant');
</script>
<style>
.experiment_controlled .bsx-deal-section .hero-upper-text .hero-text .hero-heading {
  display: none !important;
}

@media (min-width:768px) {
  .experiment_variant .bsx-deal-section .bsx-video::after {
    background: transparent;
  }

  .experiment_variant .bsx-deal-section::after {
    background: linear-gradient(359deg, rgb(1 15 39 / 90%) 16.28%, rgba(4, 3, 18, 0.00) 51.9%);
  }
}
</style><script>
window.cloud_unavailable_countries = []
function getCountrySpecificGames() {
  let viewerCountry = "iq" || "US";
  viewerCountry = viewerCountry.toLocaleUpperCase();
  let countrySpecificGames = [];
    const requestOptions = {
      method: "GET"
    };
    let geoAPI = `/wp-content/themes/bluestacks/cloud-apps-jsons/cloud_top_cpi_games_ar.json`;
    fetch(geoAPI, requestOptions)
      .then((response) => response.json())
      .then((result) => {
        // countrySpecificGames = result[viewerCountry] || result["US"] || [];
        countrySpecificGames = result["default"] || [];
        countrySpecificGames =  countrySpecificGames.slice(0, 6);
        countrySpecificGames = countrySpecificGames.map((game)=>{
          return ` <div class="game-card">
          <a class="download-title download-bs5 download-bs download-button pc-visible download-page-other-builds"
                          data-no-retry='true'
                          data-utm-mac='${game?.registered_utm}'
                          data-utm="${game?.registered_utm}"
                          data-app='{"package": "${game?.package_name}"}'
                          data-animation="إعادة التحميل"
                          data-utm="nxt-bs5-n32_button_download_page-ar"
                          href="/download.html?utm_campaign=download-page-en">
            <div class="game-image-container">
              ${game.app_icon? '<img src="' + game.app_icon + '" alt="Game Cover" class="game-image">' : ""}
              <div class="rating">⭐ ${game.app_rating}</div>
             <div class="game-card-hover">
              <div class="card-download-btn"> تحميل </div>
             </div>
            </div>
            <p class="game-title" title="${game.title}">${game.title}</p>
            <p class="developer"title="${game.producer}">${game.producer}</p>
            </a>
          </div>`;
        })
        const gamesGrid = document.querySelector('.games-grid');
       
        gamesGrid.innerHTML = countrySpecificGames.toString();
        if(countrySpecificGames.length){
          document.getElementById("top-games-section").style.display = "block";
        }

      })
      .catch((error) => {
        console.error(error)
        document.getElementById("top-games-section").style.display = "none";
      });
  }
  



</script>

<head>
  <meta name="Yeti" content="noindex" />
  <!--[if IE ]>
<meta http-equiv="X-UA-Compatible" content="IE=edge">
<![endif]-->

<meta charset="UTF-8">
<!--[if IE ]><meta http-equiv="X-UA-Compatible" content="IE=edge"><![endif]-->

<!-- Search Engine Optimization by Rank Math - https://s.rankmath.com/home -->
<title>Download BlueStacks - App Player on PC - Windows and Mac</title>
<meta name="description" content="Download BlueStacks for Windows and Mac. Enjoy over 1 Million Top Android Games with the best app player for PC."/>
<meta name="robots" content="index,follow"/>
<link rel="canonical" href="https://www.bluestacks.com/download.html" />
<meta property="og:locale" content="en_US">
<meta property="og:type" content="article">
<meta property="og:title" content="Download BlueStacks - App Player on PC - Windows and Mac">
<meta property="og:description" content="Download BlueStacks for Windows and Mac. Enjoy over 1 Million Top Android Games with the best app player for PC.">
<meta property="og:url" content="https://www.bluestacks.com/download.html">
<meta property="og:site_name" content="Bluestacks - The Best Android Emulator on PC as Rated by You">
<meta property="article:publisher" content="https://www.facebook.com/BlueStacksInc/">
<meta property="article:published_time" content="2017-01-21T20:41:31+00:00">
<meta property="article:modified_time" content="2022-05-25T04:33:39+00:00">
<meta property="og:updated_time" content="2022-05-25T04:33:39+00:00">
<meta property="fb:app_id" content="236804760387339">
<meta property="og:image" content="https://cdn-www.bluestacks.com/bs-images/OG-Image1.png">
<meta property="og:image:secure_url" content="https://cdn-www.bluestacks.com/bs-images/OG-Image1.png">
<meta property="og:image:width" content="1200">
<meta property="og:image:height" content="630">
<meta property="og:image:alt" content="download">
<meta property="og:image:type" content="image/png">
<meta name="twitter:card" content="summary_large_image">
<meta name="twitter:title" content="Download BlueStacks - App Player on PC - Windows and Mac">
<meta name="twitter:description" content="Download BlueStacks for Windows and Mac. Enjoy over 1 Million Top Android Games with the best app player for PC.">
<meta name="twitter:creator" content="@bluestacksinc">
<meta name="twitter:image" content="https://cdn-www.bluestacks.com/bs-images/OG-Image1.png">
<script type="application/ld+json">[{"@context":"https:\/\/schema.org","@type":"WebSite","@id":"https:\/\/www.bluestacks.com.html#website","url":"https:\/\/www.bluestacks.com.html","name":"BlueStacks","potentialAction":{"@type":"SearchAction","target":"https:\/\/www.bluestacks.com\/search\/{search_term_string}\/ar\/","query-input":"required name=search_term_string"}},{"@context":"https:\/\/schema.org","@type":"Article","headline":"Download BlueStacks - App Player on PC - Windows and Mac","description":"Download BlueStacks for Windows and Mac. Enjoy over 1 Million Top Android Games with the best app player for PC.","datePublished":"2017-01-21T20:41:31+00:00","dateModified":"2022-05-25T04:33:39+00:00","publisher":{"@type":"Organization","name":"BlueStacks","logo":{"@type":"ImageObject","url":"https:\/\/cdn-www.bluestacks.com\/bs-images\/logo-icon.png"}},"mainEntityOfPage":{"@type":"WebPage","@id":"https:\/\/www.bluestacks.com\/download.html"},"author":{"@type":"Person","name":"Diego"},"image":{"@type":"ImageObject","url":"https:\/\/cdn-www.bluestacks.com\/bs-images\/OG-Image1.png","width":1200,"height":630}}]</script>
<!-- /Rank Math WordPress SEO plugin -->

<link rel='stylesheet' id='wp-block-library-css'  href='https://www.bluestacks.com/wp-includes/css/dist/block-library/style.min.css?ver=5.0.3' type='text/css' media='all' />
<script>if (document.location.protocol != "https:") {document.location = document.URL.replace(/^http:/i, "https:");}</script>
<link rel="alternate" href="https://www.bluestacks.com/download.html" hreflang="x-default" />
<link rel="icon" href="https://cdn-www.bluestacks.com/bs-images/favicon.png" sizes="32x32" />
<link rel="icon" href="https://cdn-www.bluestacks.com/bs-images/favicon.png" sizes="192x192" />
<link rel="apple-touch-icon-precomposed" href="https://cdn-www.bluestacks.com/bs-images/favicon.png" />
<meta name="msapplication-TileImage" content="https://cdn-www.bluestacks.com/bs-images/favicon.png" />
<meta name="viewport" content="width=device-width,initial-scale=1,shrink-to-fit=no" />
<meta name="google-site-verification" content="tbz8pRvfI7jRiJR8spPefljM-dF2Vi3MIxitsQ2oX_Y" />
<link rel="preconnect" href="https://webapi-cloud.bluestacks.com">
<link rel="dns-prefetch" href="https://www.google-analytics.com">
<script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/js/polyfill.min.js"></script>
<!-- <script async src="https://cdn.jsdelivr.net/npm/promise-polyfill@8/dist/polyfill.min.js"></script> -->
<svg xmlns="http://www.w3.org/2000/svg" style="display:none">
  <symbol viewBox="0 0 19 18" id="search"> 
    <path fill-rule="evenodd" clip-rule="evenodd" d="M13.0401 13.253C11.71 14.4174 9.96816 15.1231 8.06152 15.1231C3.88541 15.1231 0.5 11.7376 0.5 7.56153C0.5 3.38541 3.88541 0 8.06152 0C12.2376 0 15.623 3.38541 15.623 7.56153C15.623 9.46816 14.9174 11.21 13.753 12.54L18.5 17.2871L17.7871 18L13.0401 13.253ZM14.6148 7.56153C14.6148 11.1808 11.6808 14.1149 8.06152 14.1149C4.44222 14.1149 1.5082 11.1808 1.5082 7.56153C1.5082 3.94223 4.44222 1.0082 8.06152 1.0082C11.6808 1.0082 14.6148 3.94223 14.6148 7.56153Z"/>
  </symbol>
  <symbol viewBox="0 0 33 34" id="play-icon">
    <g id="Desktop-Final" stroke="none" stroke-width="1" fill="#fff" fill-rule="evenodd" opacity="0.702938988">
      <g id="Home-Page" transform="translate(-303.000000, -3162.000000)" fill="#fff">
        <g id="Testimonials" transform="translate(0.000000, 2804.000000)">
          <g id="v1" transform="translate(145.000000, 199.000000)">
            <path d="M175.894427,160.788854 L191.276393,191.552786 C191.523382,192.046765 191.323158,192.647438 190.82918,192.894427 C190.690324,192.963855 190.537211,193 190.381966,193 L159.618034,193 C159.065749,193 158.618034,192.552285 158.618034,192 C158.618034,191.844755 158.654179,191.691642 158.723607,191.552786 L174.105573,160.788854 C174.352562,160.294876 174.953235,160.094652 175.447214,160.341641 C175.640741,160.438404 175.797663,160.595327 175.894427,160.788854 Z" id="Triangle" transform="translate(175.000000, 176.000000) rotate(-270.000000) translate(-175.000000, -176.000000) " />
          </g>
        </g>
      </g>
    </g>
  </symbol>
  <symbol width="64" height="64" viewBox="0 0 64 64" fill="none" id="play-icon-full">
    <path d="M32.2788 8.97754C36.8873 8.97754 41.3924 10.3441 45.2242 12.9045C49.0561 15.4649 52.0427 19.104 53.8063 23.3618C55.5699 27.6195 56.0314 32.3046 55.1323 36.8246C54.2332 41.3446 52.014 45.4965 48.7552 48.7552C45.4965 52.014 41.3446 54.2332 36.8246 55.1323C32.3046 56.0314 27.6195 55.5699 23.3618 53.8063C19.104 52.0427 15.4649 49.0561 12.9045 45.2242C10.3441 41.3924 8.97754 36.8873 8.97754 32.2788C8.98688 26.1018 11.4448 20.1804 15.8126 15.8126C20.1804 11.4448 26.1018 8.98687 32.2788 8.97754ZM32.2788 4.03485C26.6927 4.03485 21.232 5.69133 16.5873 8.79481C11.9426 11.8983 8.32251 16.3094 6.1848 21.4703C4.04708 26.6312 3.48776 32.3101 4.57756 37.7889C5.66735 43.2677 8.35733 48.3003 12.3073 52.2502C16.2573 56.2002 21.2899 58.8902 26.7687 59.98C32.2474 61.0698 37.9264 60.5105 43.0873 58.3727C48.2482 56.235 52.6593 52.6149 55.7627 47.9703C58.8662 43.3256 60.5227 37.8649 60.5227 32.2788C60.5227 24.788 57.547 17.6041 52.2502 12.3073C46.9535 7.01054 39.7695 4.03485 32.2788 4.03485V4.03485Z" fill="white"/>
    <path d="M41.2814 33.0555L27.6184 40.9285C27.4838 41.0022 27.3323 41.0394 27.1789 41.0367C27.0255 41.034 26.8755 40.9913 26.7436 40.9129C26.6118 40.8345 26.5026 40.723 26.4269 40.5896C26.3513 40.4561 26.3117 40.3052 26.3121 40.1518V24.3705C26.308 24.2119 26.3467 24.0551 26.4241 23.9167C26.5016 23.7782 26.6149 23.6632 26.7522 23.5837C26.8895 23.5042 27.0457 23.4632 27.2043 23.465C27.363 23.4667 27.5182 23.5112 27.6537 23.5938L41.3167 31.5021C41.4551 31.5809 41.5697 31.6956 41.6482 31.8342C41.7267 31.9728 41.7663 32.1301 41.7627 32.2893C41.7591 32.4486 41.7124 32.6039 41.6277 32.7388C41.5429 32.8737 41.4233 32.9831 41.2814 33.0555Z" fill="white"/>
  </symbol>
  <symbol viewBox="0 0 124.07 100.96" id="loader">
    <rect fill="#ddd" class="cls-1" x="12.05" y="15.85" width="82.72" height="60.34" transform="translate(-11.19 17.78) rotate(-17.13)" />
    <path fill="#fff" class="cls-2" d="M81.4,10,96.82,60,25.42,82,10,32,81.4,10M86.69,0,79,2.36l-71.4,22L0,26.73l2.36,7.64,15.42,50L20.13,92l7.65-2.36,71.4-22,7.64-2.36-2.35-7.64-15.42-50L86.69,0Z" />
    <rect fill="#ddd" class="cls-1" x="37.36" y="36.61" width="82.71" height="60.34" />
    <path fill="#fff" class="cls-2" d="M116.07,40.61V93H41.36V40.61h74.71m8-8H33.36V101h90.71V32.61Z" />
    <circle fill="#fff" class="cls-2" cx="60.25" cy="55.16" r="7.29" />
    <polygon fill="#fff" class="cls-2" points="53.41 84.92 70.57 65.05 76.76 71.24 88.49 55.6 107.82 84.92 53.41 84.92" />
  </symbol>
</svg>
<script>
// https://developers.google.com/tag-platform/security/guides/implement-TCF-strings
window['gtag_enable_tcf_support'] = true;
// window['ga-disable-G-3PED3R14PQ'] = true;

// GA init function
window.dataLayer = window.dataLayer || [];

function gtag() {
  dataLayer.push(arguments);
}

window.searchSuggetionString = {
  'games': "ألعاب",
  'see_all': "اظهار الكلاظهار الكل",
  'blogs': "المدونات",
};

function getCookie(cname) {
  var name = cname + "=";
  var decodedCookie = decodeURIComponent(document.cookie);
  var ca = decodedCookie.split(';');
  for (var i = 0; i < ca.length; i++) {
    var c = ca[i];
    while (c.charAt(0) == ' ') {
      c = c.substring(1);
    }
    if (c.indexOf(name) == 0) {
      return c.substring(name.length, c.length);
    }
  }
  return "";
}
</script>
<style>
.version-info {
  display: none !important;
}

.search-icon {
  height: 18px;
  width: 18px;
}

.so-button {
  padding-top: 7px;
  padding-bottom: 7px;
}

.download-started .so-button {
  padding-top: 0;
  padding-bottom: 0;
}

.upcoming {
  margin-left: -4%;
  margin-right: 1%;
  text-align: center;
  display: flex;
  align-items: center;
}

[lang="ar"] .upcoming {
  margin-left: 30px;
}

.upcoming a {
  color: #fff;
  display: inline-block;
  margin-right: 10px;
  max-width: 100px;
  position: relative;
  font-size: 14px;
}

.upcoming a span {
  position: absolute;
  top: -12px;
  right: -12px;
  display: block;
  background: red;
  overflow: hidden;
  border-radius: 0.8em;
  line-height: 1.2em;
  text-align: center;
  width: 1.2em;
  font-size: 13px;
}

@media(max-width: 1160px) {
  .upcoming {
    margin-left: 1%;
  }
}

@media(max-width: 1024px) {
  .upcoming {
    margin-left: 8px;
    margin-right: 67px;
    min-width: 195px;
  }

  .upcoming a {
    max-width: 95px;
  }
}

@media(max-width: 480px) {
  .upcoming {
    display: none;
  }
}
</style>

<script type="text/javascript" async=true>
(function() {
  var host = 'bluestacks.com';
  var element = document.createElement('script');
  var firstScript = document.getElementsByTagName('script')[0];
  var url = 'https://cmp.inmobi.com'
    .concat('/choice/', 'mw9xJtqPQGFbC', '/', host, '/choice.js?tag_version=V3');
  var uspTries = 0;
  var uspTriesLimit = 3;
  element.async = true;
  element.type = 'text/javascript';
  element.src = url;

  firstScript.parentNode.insertBefore(element, firstScript);

  function makeStub() {
    var TCF_LOCATOR_NAME = '__tcfapiLocator';
    var queue = [];
    var win = window;
    var cmpFrame;

    function addFrame() {
      var doc = win.document;
      var otherCMP = !!(win.frames[TCF_LOCATOR_NAME]);

      if (!otherCMP) {
        if (doc.body) {
          var iframe = doc.createElement('iframe');

          iframe.style.cssText = 'display:none';
          iframe.name = TCF_LOCATOR_NAME;
          doc.body.appendChild(iframe);
        } else {
          setTimeout(addFrame, 5);
        }
      }
      return !otherCMP;
    }

    function tcfAPIHandler() {
      var gdprApplies;
      var args = arguments;

      if (!args.length) {
        return queue;
      } else if (args[0] === 'setGdprApplies') {
        if (
          args.length > 3 &&
          args[2] === 2 &&
          typeof args[3] === 'boolean'
        ) {
          gdprApplies = args[3];
          if (typeof args[2] === 'function') {
            args[2]('set', true);
          }
        }
      } else if (args[0] === 'ping') {
        var retr = {
          gdprApplies: gdprApplies,
          cmpLoaded: false,
          cmpStatus: 'stub'
        };

        if (typeof args[2] === 'function') {
          args[2](retr);
        }
      } else {
        if(args[0] === 'init' && typeof args[3] === 'object') {
          args[3] = Object.assign(args[3], { tag_version: 'V3' });
        }
        queue.push(args);
      }
    }

    function postMessageEventHandler(event) {
      var msgIsString = typeof event.data === 'string';
      var json = {};

      try {
        if (msgIsString) {
          json = JSON.parse(event.data);
        } else {
          json = event.data;
        }
      } catch (ignore) {}

      var payload = json.__tcfapiCall;

      if (payload) {
        window.__tcfapi(
          payload.command,
          payload.version,
          function(retValue, success) {
            var returnMsg = {
              __tcfapiReturn: {
                returnValue: retValue,
                success: success,
                callId: payload.callId
              }
            };
            if (msgIsString) {
              returnMsg = JSON.stringify(returnMsg);
            }
            if (event && event.source && event.source.postMessage) {
              event.source.postMessage(returnMsg, '*');
            }
          },
          payload.parameter
        );
      }
    }

    while (win) {
      try {
        if (win.frames[TCF_LOCATOR_NAME]) {
          cmpFrame = win;
          break;
        }
      } catch (ignore) {}

      if (win === window.top) {
        break;
      }
      win = win.parent;
    }
    if (!cmpFrame) {
      addFrame();
      win.__tcfapi = tcfAPIHandler;
      win.addEventListener('message', postMessageEventHandler, false);
    }
  };

  makeStub();

  function makeGppStub() {
    const CMP_ID = 10;
    const SUPPORTED_APIS = [
      '2:tcfeuv2',
      '6:uspv1',
      '7:usnatv1',
      '8:usca',
      '9:usvav1',
      '10:uscov1',
      '11:usutv1',
      '12:usctv1'
    ];

    window.__gpp_addFrame = function (n) {
      if (!window.frames[n]) {
        if (document.body) {
          var i = document.createElement("iframe");
          i.style.cssText = "display:none";
          i.name = n;
          document.body.appendChild(i);
        } else {
          window.setTimeout(window.__gpp_addFrame, 10, n);
        }
      }
    };
    window.__gpp_stub = function () {
      var b = arguments;
      __gpp.queue = __gpp.queue || [];
      __gpp.events = __gpp.events || [];

      if (!b.length || (b.length == 1 && b[0] == "queue")) {
        return __gpp.queue;
      }

      if (b.length == 1 && b[0] == "events") {
        return __gpp.events;
      }

      var cmd = b[0];
      var clb = b.length > 1 ? b[1] : null;
      var par = b.length > 2 ? b[2] : null;
      if (cmd === "ping") {
        clb(
          {
            gppVersion: "1.1", // must be “Version.Subversion”, current: “1.1”
            cmpStatus: "stub", // possible values: stub, loading, loaded, error
            cmpDisplayStatus: "hidden", // possible values: hidden, visible, disabled
            signalStatus: "not ready", // possible values: not ready, ready
            supportedAPIs: SUPPORTED_APIS, // list of supported APIs
            cmpId: CMP_ID, // IAB assigned CMP ID, may be 0 during stub/loading
            sectionList: [],
            applicableSections: [-1],
            gppString: "",
            parsedSections: {},
          },
          true
        );
      } else if (cmd === "addEventListener") {
        if (!("lastId" in __gpp)) {
          __gpp.lastId = 0;
        }
        __gpp.lastId++;
        var lnr = __gpp.lastId;
        __gpp.events.push({
          id: lnr,
          callback: clb,
          parameter: par,
        });
        clb(
          {
            eventName: "listenerRegistered",
            listenerId: lnr, // Registered ID of the listener
            data: true, // positive signal
            pingData: {
              gppVersion: "1.1", // must be “Version.Subversion”, current: “1.1”
              cmpStatus: "stub", // possible values: stub, loading, loaded, error
              cmpDisplayStatus: "hidden", // possible values: hidden, visible, disabled
              signalStatus: "not ready", // possible values: not ready, ready
              supportedAPIs: SUPPORTED_APIS, // list of supported APIs
              cmpId: CMP_ID, // list of supported APIs
              sectionList: [],
              applicableSections: [-1],
              gppString: "",
              parsedSections: {},
            },
          },
          true
        );
      } else if (cmd === "removeEventListener") {
        var success = false;
        for (var i = 0; i < __gpp.events.length; i++) {
          if (__gpp.events[i].id == par) {
            __gpp.events.splice(i, 1);
            success = true;
            break;
          }
        }
        clb(
          {
            eventName: "listenerRemoved",
            listenerId: par, // Registered ID of the listener
            data: success, // status info
            pingData: {
              gppVersion: "1.1", // must be “Version.Subversion”, current: “1.1”
              cmpStatus: "stub", // possible values: stub, loading, loaded, error
              cmpDisplayStatus: "hidden", // possible values: hidden, visible, disabled
              signalStatus: "not ready", // possible values: not ready, ready
              supportedAPIs: SUPPORTED_APIS, // list of supported APIs
              cmpId: CMP_ID, // CMP ID
              sectionList: [],
              applicableSections: [-1],
              gppString: "",
              parsedSections: {},
            },
          },
          true
        );
      } else if (cmd === "hasSection") {
        clb(false, true);
      } else if (cmd === "getSection" || cmd === "getField") {
        clb(null, true);
      }
      //queue all other commands
      else {
        __gpp.queue.push([].slice.apply(b));
      }
    };
    window.__gpp_msghandler = function (event) {
      var msgIsString = typeof event.data === "string";
      try {
        var json = msgIsString ? JSON.parse(event.data) : event.data;
      } catch (e) {
        var json = null;
      }
      if (typeof json === "object" && json !== null && "__gppCall" in json) {
        var i = json.__gppCall;
        window.__gpp(
          i.command,
          function (retValue, success) {
            var returnMsg = {
              __gppReturn: {
                returnValue: retValue,
                success: success,
                callId: i.callId,
              },
            };
            event.source.postMessage(msgIsString ? JSON.stringify(returnMsg) : returnMsg, "*");
          },
          "parameter" in i ? i.parameter : null,
          "version" in i ? i.version : "1.1"
        );
      }
    };
    if (!("__gpp" in window) || typeof window.__gpp !== "function") {
      window.__gpp = window.__gpp_stub;
      window.addEventListener("message", window.__gpp_msghandler, false);
      window.__gpp_addFrame("__gppLocator");
    }
  };

  makeGppStub();

  var uspStubFunction = function() {
    var arg = arguments;
    if (typeof window.__uspapi !== uspStubFunction) {
      setTimeout(function() {
        if (typeof window.__uspapi !== 'undefined') {
          window.__uspapi.apply(window.__uspapi, arg);
        }
      }, 500);
    }
  };

  var checkIfUspIsReady = function() {
    uspTries++;
    if (window.__uspapi === uspStubFunction && uspTries < uspTriesLimit) {
      console.warn('USP is not accessible');
    } else {
      clearInterval(uspInterval);
    }
  };

  if (typeof window.__uspapi === 'undefined') {
    window.__uspapi = uspStubFunction;
    var uspInterval = setInterval(checkIfUspIsReady, 6000);
  }
})();
</script>


<script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/dist/js/new-components/getGeoData.js?v=3e692981">
</script>
<script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/js/gtm.js"></script>

<script async src="https://www.googletagmanager.com/gtag/js?id=G-3PED3R14PQ"></script>
<script>
gtag('js', new Date());

gtag('config', 'G-3PED3R14PQ');
</script>
  <link rel="icon" href="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" sizes="32x32" />
  <link rel="icon" href="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" sizes="192x192" />
  <link rel="apple-touch-icon-precomposed" href="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" />
  <meta name="msapplication-TileImage" content="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" />
  <style name="feature-components-styles">
  *,::after,::before{box-sizing:border-box;padding:0;margin:0}.flex{display:flex}body{margin:0;font-family:Poppins,sans-serif}.after-element::after{content:'';position:absolute;left:0;top:0;height:100%;width:100%}.hfull{height:100%}.wfull{width:100%}.relative{position:relative}.container{max-width:1300px;width:100%;margin:0 auto;position:relative}.text-center{text-align:center}figure{margin:0}.img-responsive{max-width:100%;max-height:100%;height:auto;width:auto}.p80{padding:80px 0}.flex-col{flex-direction:column}.absolute{position:absolute}.absolute-top-transform{top:50%;-webkit-transform:translateY(-50%);transform:translateY(-50%);z-index:1}.absolute-100{left:0;width:100%;height:100%;top:0}.object-fit{height:100%;width:100%;-o-object-fit:cover;object-fit:cover}.cta-layout{border:1px solid rgba(255,255,255,.2);height:70px;border-radius:6px;font-weight:600;font-size:20px;line-height:28px;display:flex;align-items:center;color:#fff;padding:0 16px;justify-content:space-between;min-width:260px}.cta-layout.hide{display:none}.cta-layout.download-bs10{background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);border:1px solid rgba(255,255,255,.2)}.cta-layout.download-bs5{background:linear-gradient(180deg,#5bce66 0,#9bce5b 100%);border:1px solid rgba(255,255,255,.2)}.cta-layout.download-bs10 .so-button,.cta-layout.download-bs5 .so-button{padding:0;flex-direction:column-reverse;font-weight:600;font-size:20px;line-height:28px}.cta-layout.download-bs10 .so-button .small-txt,.cta-layout.download-bs5 .so-button .small-txt{font-weight:500;font-size:14px;line-height:20px}.cta-layout .button-copy{justify-content:flex-start}.cta-layout .svg-icon{margin-right:12px;padding-right:12px}.cta-layout .svg-icon .new-logo-icon{max-width:54px;margin-top:4px}.cta-layout .svg-icon::after{content:'';border-right:1px solid rgba(255,255,255,.5);right:0;top:0;position:absolute;height:100%}a{text-decoration:none}.align-center{justify-content:center;align-items:center}.heading{font-weight:700;font-size:60px;line-height:72px;color:#fff}ul{list-style:none;margin:0;padding:0}li{list-style:none;padding:0}.explore-all{margin-top:18px;text-align:center}.explore-all a{max-width:100%}.explore-all button{border:1.2126px solid #fff;border-radius:9.70079px;font-weight:700;font-size:18px;line-height:28px;padding:18px 52px;color:#fff;background:0 0;transition:all .3s ease-in;cursor:pointer}.explore-all button:hover{background:linear-gradient(272.09deg,#5bce66 11.81%,#9bce5b 89.06%);box-shadow:0 4.85039px 4.85039px rgba(0,113,199,.3);border:1.2126px solid transparent}svg{vertical-align:middle;overflow:hidden}@media (max-width:767px){.bsx-link{display:none}.container{padding:0 16px}.heading{font-weight:600;font-size:24px;line-height:38px;text-align:center}.cta-layout{min-width:250px;font-size:18px}.cta-layout.download-bsx{display:none}.p80{padding:40px 0}.explore-all button{min-width:220px;font-size:16px;line-height:28px;padding:8px 50px}.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .download-bsx{display:none}.bsx-link{display:flex;height:60px}.bsx-home-page .cta-layout .button-copy{justify-content:center}}@media (min-width:768px) and (max-width:1024px){.heading{font-size:48px;line-height:60px}.container{padding:0 24px}}@media (min-width:768px){.pc-visible{display:block!important}}.cta-elements{cursor:default}.browser-cta{background:0 0;border:none;height:auto;justify-content:flex-start;width:auto;min-width:auto}.browser-cta.coming-soon{background:linear-gradient(272.09deg,#323a3e 11.81%,#798b93 89.06%);pointer-events:none;cursor:none}.browser-cta .browser-icon{margin-right:4px;padding-right:0}.browser-cta .browser-icon::after{content:none}.browser-cta *{pointer-events:none}.browser-cta .play-so-button{padding:0;flex-direction:column-reverse}.browser-cta .play-so-button .light-str{display:none;font-weight:400;font-size:14px;line-height:20px;align-items:center}.browser-cta .play-so-button .light-str .light-str-svg{margin-top:4px}.browser-cta .play-so-button .main-str{font-weight:500;font-size:12px;line-height:18px;display:flex;align-items:center;color:rgba(255,255,255,.9)}.terms-n-condition{font-weight:500;font-size:12px;line-height:120%;color:#fff;margin-top:12px;position:relative;z-index:1}@media (max-width:767px){.terms-n-condition{text-align:center}}.terms-n-condition.unchecked label::after{content:'';position:absolute;width:0;height:0;border-left:6px solid transparent;border-right:6px solid transparent;border-bottom:8px solid #f33;top:16px;left:-17px}.terms-n-condition.unchecked::after{content:attr(data-tooltip);position:absolute;background:#260303;border:1px solid #f33;color:#fff;padding:8px 16px;border-radius:6px;left:-10px;top:24px;max-width:235px;font-size:12px;line-height:18px;text-align:left;box-shadow:0 4px 16px 1px rgba(0,0,0,.5)}@media (max-width:767px){.terms-n-condition.unchecked::after{left:0}}.terms-n-condition.unchecked label:not(.no-checkbox)::before{border-color:#f33}.terms-n-condition input{opacity:0;display:none}.terms-n-condition label{position:relative;padding-left:6px;cursor:pointer}.terms-n-condition label:not(.no-checkbox)::before{content:'';position:absolute;left:-17px;border:1px solid #fff;border-radius:4px;height:16px;width:16px;top:0}.terms-n-condition input:checked+label:not(.no-checkbox):after{content:'';position:absolute;left:-11px;height:10px;width:5px;border-bottom:1px solid #fff;border-right:1px solid #fff;-webkit-transform:rotate(45deg);transform:rotate(45deg);top:1px}.terms-n-condition .no-checkbox{pointer-events:none}.terms-n-condition a{color:#fff;text-decoration:underline;pointer-events:visible}.download-other-versions{font-weight:500;font-size:12px;line-height:120%;color:#fff;text-decoration:underline}.download-other-versions[href="#all-versions"]{text-decoration:none}.download-bsx{background:linear-gradient(#159aff 0,#15b9ff 100%);border:1px solid rgba(255,255,255,.2);border-radius:6px;position:relative;min-width:260px}.download-bsx:hover{background:#52cbff}.download-bsx:not(.download-bs10){margin-left:14px}.download-bsx *{pointer-events:none}.download-bsx .so-button{padding:0;flex-direction:column-reverse}.download-bsx .so-button .small-txt{font-weight:400;font-size:14px;line-height:14px}.download-bsx.download-started{padding:0 20px;flex-direction:row-reverse;justify-content:flex-end}.download-bsx.download-started .button-copy .so-button{flex-direction:column-reverse;display:flex}.download-bsx.download-started .button-copy .so-button .light-str{font-weight:500;font-size:14px;line-height:20px}.download-bsx.download-started .retry-button{position:relative;margin-right:12px;padding-right:12px}.download-bsx.download-started .retry-button::after{content:'';border-right:1px solid rgba(255,255,255,.5);right:0;top:0;position:absolute;height:100%}.download-bsx.download-started .download-loader{position:absolute;left:0;bottom:0;height:4px;width:100%;overflow:hidden}.download-bsx.download-started .download-loader *{pointer-events:none}.download-bsx.download-started .download-loader::after{position:absolute;left:-100%;content:'';display:block;background-color:#fff;height:4px;width:100%;-webkit-animation:getWidth .5s ease-in infinite;animation:getWidth .5s ease-in infinite}.download-bsx:hover{-webkit-filter:brightness(110%);filter:brightness(110%);color:#fff}.download-bs10{margin-bottom:8px}.download-bs10:hover{background:#52cbff;border:1px solid rgba(255,255,255,.6);box-shadow:4px 8px 16px -8px rgba(21,154,255,.6)}@-webkit-keyframes getWidth{0%{left:-100%}100%{left:100%}}@keyframes getWidth{0%{left:-100%}100%{left:100%}}.browser-cta{display:none}.cloud-available .browser-cta{display:flex!important}.cloud-available .info-popup .title.not-cloud{display:none!important}.cloud-available .info-popup .desc.not-cloud{display:none}.cloud-available .info-popup .desc:not(.not-cloud),.cloud-available .info-popup .title:not(.not-cloud),.cloud-available .nowgg-href{display:flex!important}@media (max-width:767px){.pc-visible{display:none!important}.mobile-artefact-image{background:url(https://cdn-www.bluestacks.com/bs-images/artefacts_bg_mobile.png);background-size:contain;-webkit-transform:scale(1.3);transform:scale(1.3)}.mobile-artefact-image img{display:none}[lang=ar] .cta-layout.download-bsx{margin-right:0}}@media (max-width:1024px){.header-wrapper .container .left-section .logo-block{width:auto}.cta-know-more{padding:10px 20px;font-weight:600;font-size:16px;line-height:24px;display:flex;align-items:center;justify-content:center;width:auto;background:#676a72;border:1px solid #4d515a;border-radius:6px;color:#cccdd0;font-family:Poppins}.cta-know-more svg{margin-right:4px}.info-popup{background:linear-gradient(180deg,#0e2a3e 0,#011133 100%);border-radius:8px 8px 0 0;position:fixed;bottom:0;padding:30px 19px;border:1px solid rgba(255,255,255,.2);box-shadow:0 14px 28px #011133,0 10px 10px rgba(34,37,42,.4);z-index:1;height:auto;transition:.3s ease-in;transition:opacity .3s ease-out,bottom .3s ease-out;opacity:1;left:1px;right:1px;display:flex;flex-direction:row-reverse;align-items:flex-start;justify-content:space-between}.info-popup.hide{opacity:0;bottom:-250px;display:flex!important}.info-popup .desc{font-size:16px;line-height:28px;display:flex;align-items:center;color:#fff;opacity:.8;margin:0;width:268px}.info-popup .nowgg-href{border:1px solid #fff;border-radius:6px;width:100%;display:flex;justify-content:center;padding:12px 16px;font-weight:600;font-size:20px;line-height:28px;color:#fff}.info-popup .nowgg-href .next-arrow{margin-left:16px}.info-popup .heading-section{display:flex;justify-content:space-between;align-items:center}.info-popup .heading-section .cross{height:32px;width:32px;display:flex;align-items:center;justify-content:center}.info-popup .heading-section .title{font-weight:600;font-size:20px;line-height:30px;display:flex;align-items:center;text-align:center;color:#e6e6e7}body:not(.cloud-available) .info-popup .desc:not(.not-cloud),body:not(.cloud-available) .info-popup .title:not(.not-cloud),body:not(.cloud-available) .nowgg-href{display:none!important}body:not(.cloud-available) .title.not-cloud{display:flex!important}body:not(.cloud-available) .desc.not-cloud{display:block!important}[lang=ar] .info-popup .nowgg-href .next-arrow{margin-left:0;margin-right:16px;-webkit-transform:rotate(180deg);transform:rotate(180deg)}}[lang=ar] .download-bsx{margin-left:0;margin-right:14px}[lang=ar] .cta-layout .svg-icon{margin-right:0;padding-right:0;margin-left:12px;padding-left:12px}[lang=ar] .cta-layout .svg-icon::after{left:0;right:unset}[lang=ar] .download-bsx.download-started .retry-button{margin-left:12px;padding-left:12px;margin-right:0;padding-right:0}[lang=ar] .download-bsx.download-started .retry-button::after{left:0;right:unset}@media (min-width:768px){[lang=ar] .terms-n-condition.unchecked::before{left:unset;right:7px}[lang=ar] .terms-n-condition.unchecked::after{left:unset;right:0}}@media (max-width:767px){[lang=ar] .terms-n-condition.unchecked::after{left:unset;right:0}}[lang=ar] .terms-n-condition input:checked+label:not(.no-checkbox):after{right:-11px;left:unset}[lang=ar] .terms-n-condition label{padding-right:6px;padding-left:0}[lang=ar] .terms-n-condition label:not(.no-checkbox)::after,[lang=ar] .terms-n-condition label:not(.no-checkbox)::before{right:-17px;left:unset}@media (min-width:768px){body.mac-os:not(.is-iPhone) .cta-layout.download-bs10{border:1px solid #fff;background:0 0;pointer-events:none}body.mac-os:not(.is-iPhone) .non-mac{display:none}body.mac-os:not(.is-iPhone) .mac-visible.hide{display:inline-block}body:not(.cloud-available) .lower-level{justify-content:flex-start!important}}body,html{position:relative;margin:0;padding:0}html{-moz-text-size-adjust:100%;-ms-text-size-adjust:100%;text-size-adjust:100%;font-size:14px;-webkit-text-size-adjust:100%;font-variant-ligatures:none;-webkit-font-variant-ligatures:none;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;font-smoothing:antialiased;-webkit-font-smoothing:antialiased;text-shadow:rgba(0,0,0,.01) 0 0 1px}@media (min-width:480px){html{font-size:16px}}*{box-sizing:border-box;outline-width:thin}ul{margin:0;padding:0;list-style:none}a{text-decoration:none}h3{font-weight:400}abbr{text-decoration:none}figure{padding:0;margin:0}.hidden{display:none;opacity:0}button{border:none}button[type=button]{cursor:pointer}.container{margin-left:auto;margin-right:auto;padding:0 10px;max-width:1260px}.flex{display:flex}.flex.end{justify-content:flex-end}.flex.start{justify-content:flex-start}.flex.between{justify-content:space-between}.flex.center{justify-content:center}.flex.wrap{flex-wrap:wrap}.flex.y{align-items:center}.flex.top{align-items:flex-start}.flex.bottom{align-items:flex-end}[lang=vi],[lang=vi] body{font-family:Roboto,sans-serif}body:not(.mac-os) .hide-on-windows{display:none!important}body{transition:top .2s cubic-bezier(.5,0,.5,1)}.annoucement-bar{position:absolute;left:0;top:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0);z-index:999;padding:6px 0;background-color:#009ddf;width:100%}.annoucement-bar .wrp{margin-left:auto;margin-right:auto;max-width:95vw;align-items:center}.annoucement-bar .flex{display:flex}.annoucement-bar .flex.between{justify-content:space-between}.annoucement-bar .flex.center{justify-content:center}.annoucement-bar .flex.y{align-items:center}.annoucement-bar div.content{width:calc(100% - 50px)}.annoucement-bar p.content{flex:1 0 0;margin:0;color:#fff}.annoucement-bar figure{padding:0;margin:0 20px 0 0;font-size:0}.annoucement-bar .download-btn{display:inline-block;margin-left:20px;border-radius:5px;background:#283566;padding:10px 25px;color:#fff;font-size:20px;align-self:center;transition:-webkit-transform .1s cubic-bezier(.5,0,.5,1);transition:transform .1s cubic-bezier(.5,0,.5,1);transition:transform .1s cubic-bezier(.5,0,.5,1),-webkit-transform .1s cubic-bezier(.5,0,.5,1)}.annoucement-bar .download-btn:hover{background:#1a2241;-webkit-transform:translate3d(0,-1px,0);transform:translate3d(0,-1px,0)}.annoucement-bar .download-btn:active{-webkit-transform:translate3d(0,1px,0);transform:translate3d(0,1px,0)}.annoucement-bar #close-notification{position:relative;margin-left:3%;display:block;height:40px;width:40px;border:2px solid #fff;border-radius:6px;background:0 0;text-indent:-999em;cursor:pointer}.annoucement-bar #close-notification:after,.annoucement-bar #close-notification:before{position:absolute;left:50%;top:50%;margin-top:-10px;margin-left:-1px;height:20px;width:2px;content:'';background:#fff;border-radius:2px;-webkit-transform-origin:center;transform-origin:center}.annoucement-bar #close-notification:before{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.annoucement-bar #close-notification:after{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.annoucement-bar #close-notification:hover{color:#3c4455;background:currentColor;border-color:currentColor}@media only screen and (max-width:1030px){.annoucement-bar .wrp{width:98vw}}@media only screen and (max-width:980px){.annoucement-bar figure{margin:0 10px 0 0}.annoucement-bar div.content{width:calc(100% - 50px)}.annoucement-bar div.content.flex.y{align-items:flex-start}.annoucement-bar div.content.flex .flex{display:block}.annoucement-bar .download-btn{margin:10px 0 0}}@media (max-width:480px){.annoucement-bar #close-notification{position:absolute;top:20px;right:20px}.annoucement-bar>.flex>.content{max-width:calc(100% - 80px)}.annoucement-bar .flex{align-items:flex-start}.annoucement-bar .download-btn{font-size:13px}}.string-if-on-pc{display:none;font-style:normal}@media (min-width:1025px){.string-if-on-pc{display:inline}}.bs-logo{display:inline-block}@media (max-width:640px){.bs-logo{display:block;width:14vw;overflow:hidden}.bs-logo img{width:40vw;height:auto}}@media (max-width:640px){[lang=ar] .bs-logo img{position:relative;left:186%}}.language-selector .language-selector-btn>svg{height:8px;width:14px;fill:#fff;transition:-webkit-transform .3s cubic-bezier(.77,0,.3,.99);transition:transform .3s cubic-bezier(.77,0,.3,.99);transition:transform .3s cubic-bezier(.77,0,.3,.99),-webkit-transform .3s cubic-bezier(.77,0,.3,.99)}.language-selector-btn{transition:.7s background cubic-bezier(0,.5,.5,1)}.language-selector{position:relative;right:-10px;font-size:14px;min-width:115px;transition:.5s opacity cubic-bezier(0,.5,.5,1),.3s cubic-bezier(.5,0,.5,1) right}.language-selector:not(.ready){opacity:0}.language-selector .language-selector-btn{position:relative;z-index:2;display:flex;justify-content:space-between;align-items:center;width:100%;padding-top:6.4px;padding-bottom:6.4px;padding-left:9.6px;padding-right:9.6px;background:0 0;border:2px solid #fff;border-radius:5px;cursor:pointer}@media (min-width:768px){.language-selector .language-selector-btn{padding-top:8px;padding-bottom:8px}}@media (min-width:768px){.language-selector .language-selector-btn{padding-left:12px;padding-right:12px}}.language-selector .language-selector-btn svg{position:absolute;right:10px;top:50%;margin-top:-4px;display:inline-block}.language-selector .selected-lang{position:relative;margin-right:15px;padding:3px 0;display:inline-block;font-size:15px;font-size:.9375rem;font-family:Lato,sans-serif;color:#fff;text-align:left;font-weight:900}.language-selector ul{position:absolute;z-index:9;top:calc(100% - 3px);padding-left:0;padding-right:0;padding-top:9.6px;padding-bottom:9.6px;width:100%;opacity:0;-webkit-transform-origin:top;transform-origin:top;-webkit-transform:scale3d(1,0,1);transform:scale3d(1,0,1);transition:.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.5,0,.5,1);transition:.3s transform cubic-bezier(.5,0,.5,1),.25s opacity cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.5,0,.5,1),.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.5,0,.5,1);border-radius:0 0 4px 4px;border:2px solid #fff;border-top:0;background:rgba(28,29,35,.7)}@media (min-width:768px){.language-selector ul{padding-top:12px;padding-bottom:12px}}.language-selector ul li{display:block;opacity:0;transition:.1s opacity cubic-bezier(.77,0,.3,.99)}.language-selector ul a{display:block;padding:5px 12px;color:#fff}.language-selector ul a:hover{background:rgba(28,29,35,.7);opacity:.5}.language-selector ul:hover{transition:.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.77,0,.3,.99),.25s opacity cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.77,0,.3,.99),.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.77,0,.3,.99);-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1);opacity:1}@media (max-width:1024px){.language-selector{right:60px}}@media (max-width:1024px){[lang=ar] .language-selector{right:-60px}}[lang=ar] .language-selector .language-selector-btn svg{right:initial;left:10px}.language-selector.active .language-selector-btn,.language-selector:hover .language-selector-btn{background:rgba(28,29,35,.7);transition:.1s background cubic-bezier(.77,0,.3,.99)}.language-selector.active ul,.language-selector:hover ul{opacity:1;-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1);transition:.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.77,0,.3,.99),.25s opacity cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.77,0,.3,.99),.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.77,0,.3,.99)}.language-selector.active svg,.language-selector:hover svg{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.language-selector ul:hover li,.language-selector.active ul li,.language-selector:hover ul li{opacity:1;transition:.1s .35s opacity cubic-bezier(.77,0,.3,.99);-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.container-autocomplete{margin:auto;max-width:550px}.container-autocomplete form{margin:auto;border-radius:5px;background:#fff;overflow:hidden}@media (max-width:768px){.container-autocomplete{display:none}}.container-autocomplete input{position:relative;padding:10px 15px;font-size:18px;font-size:1.125rem;font-weight:300;font-family:Lato,sans-serif;font-style:italic;background:#fff;vertical-align:middle;white-space:nowrap;border:none;border-radius:4px;border:1px solid transparent;-webkit-appearance:textfield;outline:0}.container-autocomplete input:focus{border-color:rgba(117,207,229,.3)}@media (min-width:769px){.container-autocomplete input{width:35vw;min-width:250px;height:46px;left:4px}}.container-autocomplete button{background:0 0;border:none;fill:#a9adb8;outline:0;padding:13px}.container-autocomplete button svg{pointer-events:none}@media (min-width:540px){#mobile-search-opner{display:none}}.autocomplete-suggestions{position:absolute;z-index:9999;display:none;max-height:250px;border-radius:5px;background:#fff;overflow:hidden;overflow-y:auto}.autocomplete-suggestions .search-wrapper .heading{display:flex;justify-content:space-between;margin:14px 0 5px 0;padding:0 14px}.autocomplete-suggestions .search-wrapper .heading .heading-title{font-weight:600;font-size:16px;line-height:24px;color:#28304c;margin:0}.autocomplete-suggestions .search-wrapper .heading .view-all{font-weight:400;font-size:14px;line-height:24px;color:#0082e5}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion{padding:10px 14px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i{display:flex;align-items:center}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure{height:40px;width:40px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure .object-fit{width:100%;display:inline-block;height:100%;-o-object-fit:cover;object-fit:cover}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{margin-left:18px;font-size:18px;line-height:28px;color:#394566;width:calc(100% - 58px);-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:1;overflow:hidden;text-overflow:ellipsis}.autocomplete-suggestion{padding:10px;color:#3d4664;cursor:pointer}.autocomplete-suggestion:hover{background:#b0cfe5;color:#fff}.autocomplete-suggestion:not(:last-child){border-bottom:1px solid rgba(169,173,184,.3)}.autocomplete-suggestion b{font-weight:400;color:#1f8dd6}input.placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input:-moz-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input::-moz-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input::-webkit-input-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input:focus.placeholder{opacity:0;-webkit-transform:translateX(12px);transform:translateX(12px)}input:focus:-moz-placeholder{opacity:0;transform:translateX(12px)}input:focus::-moz-placeholder{opacity:0;transform:translateX(12px)}input:focus::-webkit-input-placeholder{opacity:0;-webkit-transform:translateX(12px);transform:translateX(12px)}[lang=ar] .autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{margin-left:0;margin-right:18px}@media (max-width:767px){.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion{padding:6px 14px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure{height:32px;width:32px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{font-size:14px;line-height:150%}}.main-header{width:100%;z-index:999;padding-top:9.6px;padding-bottom:9.6px}@media (min-width:768px){.main-header{padding-top:12px;padding-bottom:12px}}.main-header:not(.absolute){position:relative}.main-header.absolute{position:absolute;width:100%}.main-header.blue{background:rgba(20,26,47,.8)}.main-header.blue-opaque{background:#222d48}.main-header.gray{background:rgba(0,0,0,.3)}.menu-container{position:relative;margin:0 auto;width:calc(100% - 48px);align-items:center}@media (max-width:540px){.menu-container{width:calc(100% - 30px)}}.language-selector:not(.ready),.menu-opner{opacity:0}.menu-opner{position:absolute}.container-autocomplete+.ko-txt{display:none}.hide{display:none}.wrapper-links{display:flex;align-items:center;height:30px;margin-right:7%}.wrapper-links img{max-width:100%;max-height:100%}.wrapper-links .ko-lnk{color:#fff;display:flex;align-items:center;order:2}.wrapper-links .ko-lnk .img-section{height:30px;width:30px;margin-right:10px}.wrapper-links .samsung-store-link .img-section{height:30px;width:100%;margin-right:20px}.wrapper-links .samsung-store-link .img-section .pc-visible{display:block}.wrapper-links .samsung-store-link .img-section .mob-visible{display:none}@media (max-width:1285px){.wrapper-links .ko-lnk p{display:none}}@media (max-width:1150px){.wrapper-links{margin:0}}@media (max-width:1024px){[lang=ko-KR] .upcoming{margin-right:0;min-width:125px}.wrapper-links .samsung-store-link .img-section .pc-visible{display:none}.wrapper-links .samsung-store-link .img-section .mob-visible{display:block}}@media (max-width:767px){.wrapper-links{margin-right:45px}.wrapper-links .samsung-store-link .img-section{width:30px;margin-right:10px}}.pop-up-open{overflow:hidden}.pop-up{position:fixed;top:0;left:0;width:100%;height:100%;z-index:99999;background:rgba(35,51,76,.9);opacity:0}.pop-up:not(.active){display:none}.pop-up.active{-webkit-animation:modal-showing-up .2s cubic-bezier(.5,0,.5,1) forwards;animation:modal-showing-up .2s cubic-bezier(.5,0,.5,1) forwards}.pop-up .close-modal{position:absolute;top:2vw;right:2vw;border:0;padding:20px;background:0 0}.pop-up .close-modal:after,.pop-up .close-modal:before{position:absolute;left:50%;top:50%;content:'';background-color:#fff;-webkit-transform:translate(-50%,-50%) rotate(45deg);transform:translate(-50%,-50%) rotate(45deg);-webkit-transform-origin:center center;transform-origin:center center}.pop-up .close-modal:before{height:2px;width:50%}.pop-up .close-modal:after{height:50%;width:2px}.show-content .pop-up-content{opacity:1;-webkit-transform:translate3d(-50%,0,0);transform:translate3d(-50%,0,0)}.pop-up-content{position:absolute;top:90px;left:50%;-webkit-transform:translate3d(-50%,10px,0);transform:translate3d(-50%,10px,0);width:90%;max-width:700px;padding:20px 30px 40px;text-align:center;border-radius:2px;box-sizing:border-box;background:#e9f0f7;opacity:0;transition:opacity .15s cubic-bezier(0,.5,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:transform .2s cubic-bezier(.5,0,.5,1),opacity .15s cubic-bezier(0,.5,.5,1);transition:transform .2s cubic-bezier(.5,0,.5,1),opacity .15s cubic-bezier(0,.5,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);box-shadow:0 14px 28px rgba(19,28,41,.8),0 10px 10px rgba(19,28,41,.4)}.pop-up-content h5{color:#32406f;font-size:33px;margin:5px auto 20px}.pop-up-content p{color:gray;font-size:20px;margin:0 auto 8px;font-weight:300}.pop-up-content .small{font-size:14px}@-webkit-keyframes modal-showing-up{from{opacity:0}to{opacity:1}}@keyframes modal-showing-up{from{opacity:0}to{opacity:1}}#current-language-popup{position:fixed;right:-222px;top:15vh;width:220px;z-index:999;font-family:Lato,sans-serif;-webkit-animation:show-current-language-modal .5s cubic-bezier(0,.5,.5,1) forwards;animation:show-current-language-modal .5s cubic-bezier(0,.5,.5,1) forwards}#current-language-popup #language-popup-closer{position:absolute;opacity:0;height:0}#current-language-popup #language-popup-closer:checked+.l-p-content{opacity:0;pointer-events:none;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0);transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .5s cubic-bezier(.5,0,0,1);transition:transform .5s cubic-bezier(.5,0,0,1),opacity .2s cubic-bezier(.5,0,.5,1);transition:transform .5s cubic-bezier(.5,0,0,1),opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .5s cubic-bezier(.5,0,0,1)}#current-language-popup .l-p-content{position:relative;width:220px;padding:60px 20px 30px;box-sizing:border-box;background:rgba(29,45,83,.7);text-align:center;-webkit-transform:translate3d(-222px,0,0);transform:translate3d(-222px,0,0)}#current-language-popup .l-p-phrase{font-weight:700;color:#fff}#current-language-popup .l-p-flag img{width:80px}#current-language-popup .lp-close{position:absolute;right:20px;top:20px;width:35px;height:35px;cursor:pointer}#current-language-popup .lp-close::after,#current-language-popup .lp-close::before{content:'';position:absolute;top:50%;left:0;height:1px;width:100%;background:#fff}#current-language-popup .lp-close:before{-webkit-transform:rotate(45deg);transform:rotate(45deg)}#current-language-popup .lp-close::after{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}#current-language-popup .lp-close:hover::after,#current-language-popup .lp-close:hover::before{box-shadow:0 0 0 1px #fff}#current-language-popup #l-p-button{position:relative;display:inline-block;margin:20px;padding:8px 25px;font-size:22px;color:#fff;text-decoration:none;background-color:#f1c40f;border-radius:5px;box-shadow:0 5px 0 0 #d8ab00;font-weight:700}#current-language-popup #l-p-button:active{-webkit-transform:translate(0,5px);transform:translate(0,5px);box-shadow:0 1px 0 0 #d8ab00}@-webkit-keyframes show-current-language-modal{0%{opacity:0;-webkit-transform:translate3d(110%,0,0);transform:translate3d(110%,0,0)}100%{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}@keyframes show-current-language-modal{0%{opacity:0;-webkit-transform:translate3d(110%,0,0);transform:translate3d(110%,0,0)}100%{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}.hyper-link .download-bs5-link .retry-button{display:none}.download-bs5-link *{pointer-events:none}.download-app-on-mobile .text{font-family:poppins,sans-serif!important;font-weight:600}.hero-text .download-bs5-wrapper{display:flex;align-items:center;justify-content:center;max-width:540px;position:relative;z-index:2}.download-app-on-mobile{display:flex;-webkit-box-align:center;color:rgba(255,255,255,.9);gap:4px;font-size:14px;font-style:normal;font-weight:600;line-height:15.556px;align-items:flex-end;cursor:pointer;font-family:Roboto,sans-serif;box-sizing:border-box;margin:0;padding:0}.download-app-on-mobile::before{content:'';display:inline-block;border-left:2px solid #ffffff30;margin-left:8px;padding-left:8px;height:20px}.download-app-on-mobile .text{margin:0;font-weight:600;font-family:Roboto,sans-serif;box-sizing:border-box;padding:0}.download-app-on-mobile:hover svg path{fill:#159aff}.download-app-on-mobile:hover .text{color:#159aff}.bsx-deal-section .hero-text .download-bs5-wrapper{justify-content:flex-start}.bsx-deal-section .hero-text .download-bsx .svg-icon::after{height:40px;top:7px}.bsx-deal-section .hero-text .download-bs10 .svg-icon::after{height:40px;top:13px}.bsx-deal-section .hero-text.without-app-cta .download-bs10 .so-button{display:flex;flex-flow:row;flex-direction:row-reverse}.bsx-deal-section .hero-text.without-app-cta .download-bs10 .so-button .light-str,.bsx-deal-section .hero-text.without-app-cta .download-bs10 .so-button .small-txt{font-weight:600;font-size:20px;line-height:28px;margin-left:4px}.bsx-deal-section .hero-text.without-app-cta .download-bsx{width:auto}.bsx-deal-section .hero-text.without-app-cta .download-bs5-wrapper{justify-content:flex-start}.windows-os .bsx-deal-section .hero-text .download-bs5-wrapper .download-bs5-link,.windows-os .hero-text .download-bs5-wrapper .download-bs5-link{background:transparent url(https://cdn-www.bluestacks.com/bs-images/download-generic-img.png) no-repeat;border:none;width:auto;height:auto;padding:0;margin:0;min-width:auto;position:relative;background-size:16px;background-position:left;padding-left:16px}.bsx-deal-section .hero-text .download-bs5-wrapper .download-bs5-link .btn-img,.hero-text .download-bs5-wrapper .download-bs5-link .btn-img{max-width:16px}.bsx-deal-section .hero-text .download-bs5-wrapper .download-bs5-link .so-button,.bsx-deal-section .hero-text .download-bs5-wrapper .download-bs5-link:hover .so-button,.hero-text .download-bs5-wrapper .download-bs5-link .so-button,.hero-text .download-bs5-wrapper .download-bs5-link:hover .so-button{font-size:14px;margin-left:8px;font-weight:600;color:#fff}.windows-os .bsx-deal-section .hero-text .download-bs5-wrapper .download-other-versions,.windows-os .hero-text .download-bs5-wrapper .download-other-versions{font-size:14px;font-weight:600;text-decoration:none;margin-left:8px;position:relative;padding-right:16px}.windows-os .bsx-deal-section .hero-text .download-bs5-wrapper .download-other-versions .divider,.windows-os .hero-text .download-bs5-wrapper .download-other-versions .divider{margin-right:8px;color:#fff;opacity:.4}.windows-os .bsx-deal-section .hero-text .download-bs5-wrapper .download-other-versions::after,.windows-os .hero-text .download-bs5-wrapper .download-other-versions::after{right:0;content:'';border:solid rgba(255,255,255,.9);border-width:0 2px 2px 0;display:inline-block;padding:3px;-webkit-transform:rotate(315deg) translateY(-50%);transform:rotate(315deg) translateY(-50%);position:absolute;top:calc(50% - 1px);transition:.3s ease-in-out}[lang=ar] .hero-text .download-bs5-wrapper{flex-direction:row-reverse}[lang=ar] .windows-os .bsx-deal-section .hero-text .download-bs5-wrapper .download-other-versions,[lang=ar] .windows-os .hero-text .download-bs5-wrapper .download-other-versions{display:flex}[lang=ar] .windows-os .bsx-deal-section .hero-text .download-bs5-wrapper .download-other-versions .divider,[lang=ar] .windows-os .hero-text .download-bs5-wrapper .download-other-versions .divider{order:2}.bsx-hero-section .download-deal-bs5-btn-width-auto{width:auto!important}.bsx-hero-section .download-deal-bs5-btn-width-auto::before{content:'';position:absolute;display:none;top:0;left:0;width:95px;height:200%;z-index:2;-webkit-transform-origin:right center;transform-origin:right center;-webkit-transform:translate(-100%,-50%) rotate(45deg);transform:translate(-100%,-50%) rotate(45deg);background:#fff;opacity:.5;-webkit-filter:blur(25px);filter:blur(25px);-webkit-animation:5s ease 0s infinite normal none running fadeInOut;animation:5s ease 0s infinite normal none running fadeInOut}.bsx-hero-section .download-deal-bs5-btn-width-auto:hover::before{display:block}@-webkit-keyframes fadeInOut{0%{left:0;-webkit-transform-origin:right center;transform-origin:right center;-webkit-transform:translate(-100%,-50%) rotate(45deg);transform:translate(-100%,-50%) rotate(45deg)}10%{left:100%;-webkit-transform-origin:center left;transform-origin:center left;-webkit-transform:translate(0,0) rotate(45deg);transform:translate(0,0) rotate(45deg)}100%{left:100%;-webkit-transform-origin:center left;transform-origin:center left;-webkit-transform:translate(0,0) rotate(45deg);transform:translate(0,0) rotate(45deg)}}@keyframes fadeInOut{0%{left:0;-webkit-transform-origin:right center;transform-origin:right center;-webkit-transform:translate(-100%,-50%) rotate(45deg);transform:translate(-100%,-50%) rotate(45deg)}10%{left:100%;-webkit-transform-origin:center left;transform-origin:center left;-webkit-transform:translate(0,0) rotate(45deg);transform:translate(0,0) rotate(45deg)}100%{left:100%;-webkit-transform-origin:center left;transform-origin:center left;-webkit-transform:translate(0,0) rotate(45deg);transform:translate(0,0) rotate(45deg)}}.bsx-hero-section .bsx-video{height:820px;background-color:#000}.bsx-hero-section .bsx-video .hero-banner{max-width:1990px;margin:0 auto}.bsx-hero-section .bsx-video::after{background:linear-gradient(270.33deg,rgba(14,43,62,0) 2.51%,rgba(1,17,51,.95) 90.45%)}.bsx-hero-section .bsx-video .hero-video{max-height:820px;width:100%;-o-object-fit:cover;object-fit:cover;-o-object-position:center center;object-position:center center}.bsx-hero-section .hero-upper-text{left:0}.bsx-hero-section .hero-upper-text .hero-text{width:700px;max-width:100%}.bsx-hero-section .hero-upper-text .hero-text .nowgg-logo{height:80px}.bsx-hero-section .hero-upper-text .hero-text .hero-heading{margin:30px 0;text-shadow:0 4px 4px rgba(0,0,0,.25);line-height:80px;max-width:100%}.bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word{width:100%;display:flex;max-width:100%;font-size:24px;line-height:36px;font-weight:400}.alert-modal{position:fixed;bottom:-100px;font-weight:600;font-size:20px;line-height:28px;background:#f33;border:1px solid rgba(255,255,255,.2);border-radius:6px;padding:21px 24px;display:block;color:#fff;transition:.25s all;-webkit-transform:translate(-50%,0);transform:translate(-50%,0);left:50%;z-index:9}@media (max-width:767px){.alert-modal{bottom:-95px;font-weight:600;font-size:16px;line-height:20px;padding:12px 16px;width:100%}}.alert-modal.slid-up{-webkit-transform:translate(-50%,-100px);transform:translate(-50%,-100px)}@media (max-width:767px){.alert-modal.slid-up{-webkit-transform:translate(-50%,-90px);transform:translate(-50%,-90px)}}.vid-fullscreen-mode{position:fixed;inset:0;width:100%;height:100%;z-index:999;background:#0b0223}.vid-fullscreen-mode .close-vid{display:block;position:absolute;right:24px;top:24px;z-index:1000;width:24px;height:24px;cursor:pointer}.vid-fullscreen-mode .close-vid::after,.vid-fullscreen-mode .close-vid::before{content:'';position:absolute;width:2px;height:16px;top:4px;left:11px;-webkit-transform:rotate(45deg);transform:rotate(45deg);background:#fff}.vid-fullscreen-mode .close-vid::after{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}[lang=ar] .bsx-hero-section .bsx-video::after{background:linear-gradient(270.33deg,rgba(1,17,51,.95) 2.51%,rgba(14,43,62,0) 90.45%)}@media (max-width:767px){.bsx-hero-section{min-height:auto;background:linear-gradient(348.42deg,#0e2a3e 31.62%,#011133 90.19%)}.bsx-hero-section .bsx-video{height:320px}.bsx-hero-section .bsx-video::after{background:linear-gradient(0deg,rgba(0,0,0,.9) 0,rgba(0,0,0,.6) 30%,rgba(0,0,0,0) 60%)!important}.bsx-hero-section .hero-upper-text{min-height:auto;display:flex;align-items:center;top:unset;-webkit-transform:none;transform:none;left:unset;padding:30px 0;position:absolute;bottom:0;left:0;right:0}.bsx-hero-section .hero-upper-text .hero-text .nowgg-logo{height:40px;text-align:center}.bsx-hero-section .hero-upper-text .hero-text .hero-heading{font-size:24px;line-height:36px;max-width:100%;margin:0 auto 24px}.bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word{justify-content:center;font-weight:600;font-size:20px;line-height:30px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements{justify-content:center}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .play-browser-wrapper{align-items:center;display:flex}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .mobile-cta{display:flex;flex-direction:column;align-items:center}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-bsx{display:none}.bsx-hero-section .hero-upper-text .hero-text .download-other-versions{display:none}.bsx-hero-section .investors-section{padding-bottom:20px;background:0 0}}@media (min-width:768px){.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-bsx:not(.download-bs10):not(.download-bs5){margin-left:40px;position:relative;padding:0 24px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-bsx:not(.download-bs10):not(.download-bs5) .download-icon{margin-right:18px;padding-right:0}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-bsx:not(.download-bs10):not(.download-bs5) .download-icon .download-icon{margin-right:0}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-bsx:not(.download-bs10):not(.download-bs5) .download-icon::after{content:none}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level{display:flex;justify-content:center;align-items:center}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .download-other-versions{position:relative;color:rgba(255,255,255,.9);text-decoration:none;padding-right:8px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .download-other-versions::after{right:-1px;content:'';border:solid rgba(255,255,255,.9);border-width:0 1px 1px 0;display:inline-block;padding:2px;-webkit-transform:rotate(315deg) translate(0,-50%);transform:rotate(315deg) translate(0,-50%);position:absolute;top:calc(50% - 1px);transition:.3s ease-in-out}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link{height:auto;min-width:auto;background:0 0;border:none;padding:0;padding-right:8px;margin-right:8px;position:relative}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link::after{content:'';border-left:1px solid #fff;opacity:.4;position:absolute;right:0;width:1px;height:16px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link .browser-icon{margin-right:4px;padding-right:0;display:flex}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link .browser-icon::after{content:none}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link .play-so-button .light-str{display:none}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link .play-so-button .main-str{font-weight:500;font-size:12px;line-height:18px;display:flex;align-items:center;color:rgba(255,255,255,.9)}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .terms-n-condition{max-width:300px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .terms-n-condition label{padding-left:0}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .no-checkbox-wrapper.terms-n-condition input{position:absolute;left:4px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .no-checkbox-wrapper.terms-n-condition input:checked+label:not(.no-checkbox):after{left:-16px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .no-checkbox-wrapper.terms-n-condition label{margin-left:24px;display:inline-block}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .no-checkbox-wrapper.terms-n-condition label:not(.no-checkbox)::before{left:-22px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer{margin-left:20px}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-bsx{margin-bottom:4px;margin-left:0}.bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-other-versions{font-weight:500;font-size:12px;line-height:120%;color:#fff;text-decoration:underline}.mac-os .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer{margin-left:0}.mac-os .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-bsx:not(.download-bs10):not(.download-bs5){margin-left:0;margin-bottom:8px}[lang=ar] .bsx-hero-section .hero-upper-text .hero-text .cta-elements .no-checkbox-wrapper.terms-n-condition label{margin-right:22px;margin-left:0}[lang=ar] .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer{margin-left:0;margin-right:20px}[lang=ar] .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-bsx{margin-right:0;margin-left:unset}[lang=ar] .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-bsx::before{right:-22px;left:unset}[lang=ar] body.mac-os .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-bsx:not(.download-bs10):not(.download-bs5) .download-icon{margin-right:0;padding-right:0;padding-left:4px}[lang=ar] body.mac-os .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac::before{right:unset;left:-13px}[lang=ar] body.mac-os .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link{padding-left:8px;margin-left:8px;margin-right:0;padding-right:0}[lang=ar] body.mac-os .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link::after{left:0;right:unset}[lang=ar] body.mac-os .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .browser-cta.cta-layout.bsx-link .browser-icon{padding-left:0;margin-left:4px;margin-right:0}[lang=ar] .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .download-other-versions{padding-left:8px;padding-right:0}[lang=ar] .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .lower-level .download-other-versions::after{left:-1px;right:unset;-webkit-transform:rotate(145deg) translate(0,-50%);transform:rotate(145deg) translate(0,-50%);top:calc(50% - 4px)}}@media (min-width:768px){[lang=de-DE] .bsx-hero-section .hero-upper-text .hero-text .hero-heading,[lang=es-ES] .bsx-hero-section .hero-upper-text .hero-text .hero-heading,[lang=fr-FR] .bsx-hero-section .hero-upper-text .hero-text .hero-heading,[lang=ja] .bsx-hero-section .hero-upper-text .hero-text .hero-heading,[lang=ko-KR] .bsx-hero-section .hero-upper-text .hero-text .hero-heading,[lang=vi] .bsx-hero-section .hero-upper-text .hero-text .hero-heading,[lang=zh-hant] .bsx-hero-section .hero-upper-text .hero-text .hero-heading{font-size:48px}[lang=de-DE] .bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word,[lang=es-ES] .bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word,[lang=fr-FR] .bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word,[lang=ja] .bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word,[lang=ko-KR] .bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word,[lang=vi] .bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word,[lang=zh-hant] .bsx-hero-section .hero-upper-text .hero-text .hero-heading .break-word{font-size:24px;line-height:36px}}.bsx-deal-section::after{background:linear-gradient(258.36deg,rgba(0,0,0,.0001) 21.36%,rgba(0,0,0,.8) 97.18%);background-blend-mode:darken;content:'';position:absolute;left:0;top:0;height:100%;width:100%}.bsx-deal-section .browser-cta{width:auto}.bsx-deal-section .download-bsx{width:260px}.bsx-deal-section .hero-upper-text{z-index:3}.bsx-deal-section .hero-upper-text .cta-elements{z-index:9;position:relative}.bsx-deal-section .bsx-redirect-lnk{top:0;z-index:1}.bsx-deal-section .bsx-video::after{background:linear-gradient(180deg,rgba(14,43,64,.0001) 73.44%,#0e2b40 100%);background-blend-mode:darken}.bsx-deal-section .game-logo{height:120px}.bsx-deal-section .game-logo .lazy:not(.loaded)+.img-loading{width:240px}@media (max-width:767px){.bsx-deal-section .game-logo .lazy:not(.loaded)+.img-loading{-webkit-transform:translate(-50%,0);transform:translate(-50%,0);left:50%}}.bsx-deal-section .hero-upper-text .hero-text .hero-heading{margin:28px 0 31px;font-size:38px;line-height:56px;width:556px}.bsx-deal-section .hero-upper-text .hero-text .hero-heading .break-word{font-size:38px;line-height:56px}.bsx-deal-section .hero-upper-text .hero-text .is-cloud-enabled{align-items:center;margin-bottom:15px;background:#010713;border-radius:12px;max-width:-webkit-fit-content;max-width:-moz-fit-content;max-width:fit-content;padding-right:12px}.bsx-deal-section .hero-upper-text .hero-text .is-cloud-enabled .cloud-txt{font-size:12px;line-height:18px;color:#fff;margin-left:6px;font-weight:700}.bsx-deal-section .hero-upper-text .hero-text .not-cloud-enabled+.cta-elements .browser-cta{border:1px solid #fff;background:0 0}.bsx-deal-section .cta-layout.download-bs10.custom,.bsx-deal-section .cta-layout.download-bsx.custom:not(.download-deal-bs5-btn-width-auto){background:linear-gradient(180deg,#15a1ff 6.89%,#15a3ff 92.13%,#15b4ff 180.91%);width:auto;min-width:240px;margin-right:4px;padding:0 10px}.bsx-deal-section .cta-layout.download-bs10.custom::before,.bsx-deal-section .cta-layout.download-bsx.custom:not(.download-deal-bs5-btn-width-auto)::before{content:'';position:absolute;width:0;height:0;border-top:48px solid transparent;border-right:15px solid #15a1ff;border-bottom:22px solid transparent;left:-14px;border-radius:4px;top:-2px;display:block!important}.bsx-deal-section .cta-layout.download-bs10.custom::after,.bsx-deal-section .cta-layout.download-bsx.custom:not(.download-deal-bs5-btn-width-auto)::after{content:'';position:absolute;width:0;height:0;border-top:48px solid transparent;border-right:15px solid #15a1ff;border-bottom:22px solid transparent;right:-14px;border-radius:4px;-webkit-transform:rotate(180deg);transform:rotate(180deg);top:0;display:block!important}.bsx-deal-section .cta-layout.download-bs10.custom:hover,.bsx-deal-section .cta-layout.download-bsx.custom:not(.download-deal-bs5-btn-width-auto):hover{border:1px solid rgba(255,255,255,.2);background:#52cbff}.bsx-deal-section .cta-layout.download-bs10.custom:hover::before,.bsx-deal-section .cta-layout.download-bsx.custom:not(.download-deal-bs5-btn-width-auto):hover::before{border-top:48px solid transparent;border-right:15px solid #52cbff;border-bottom:22px solid transparent}.bsx-deal-section .cta-layout.download-bs10.custom:hover::after,.bsx-deal-section .cta-layout.download-bsx.custom:not(.download-deal-bs5-btn-width-auto):hover::after{border-top:48px solid transparent;border-right:15px solid #52cbff;border-bottom:22px solid transparent}.bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom{overflow:inherit;background-size:contain!important;width:auto!important;min-width:260px!important;border:none!important;background-repeat:no-repeat!important}.bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom.download-started .button-copy .so-button{display:none}.bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom:hover{opacity:.9}.bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom:hover::before{content:none}.bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom div{background:0 0!important}.windows-os .bsx-deal-section .download-bsx.cta-with-bg-img.retry-link-bs5 .button-copy{display:none}.windows-os .bsx-deal-section .download-bsx.cta-with-bg-img .button-copy{display:none}@media (min-width:768px){[lang=ar] .bsx-deal-section .game-logo .lazy:not(.loaded)+.img-loading{right:0;left:unset}}[lang=ar] .bsx-deal-section .hero-upper-text .hero-text .is-cloud-enabled{padding-left:12px;padding-right:0}[lang=ar] .bsx-deal-section .hero-upper-text .hero-text .is-cloud-enabled .cloud-txt{margin-right:6px;margin-left:0}@media (min-width:768px){.bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text .cta-elements .terms-n-condition{max-width:270px}.play-browser-wrapper.hide+.download-wrapper-layer.show .download-other-versions{display:block}.mac-os .bsx-deal-section .hero-upper-text .hero-text .is-cloud-enabled[data-mac-cloud-enabled=cloud-enabled]+.cta-elements .browser-cta{background:linear-gradient(272.09deg,#159aff 11.81%,#15b9ff 89.06%);border:1px solid rgba(255,255,255,.2)}.mac-os .bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom.download-started .button-copy .so-button{display:flex}.mac-os .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text div[data-mac-cloud-cta-present=hide]{display:none}.mac-os .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text div[data-mac-cloud-cta-present=hide]+.download-wrapper-layer{margin-left:0;margin-right:0}.mac-os .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text div[data-mac-cloud-cta-present=hide]+.download-wrapper-layer .download-other-versions{display:block}.mac-os .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text div[data-mac-cloud-cta-present=hide]+.download-wrapper-layer .download-bsx::before{content:none}.mac-os .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text div[data-mac-cta-present=hide]{display:none}.mac-os .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text div[data-mac-cta-present=show]{display:block}.mac-os .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text div[data-mac-cloud-cta-present=show]{display:flex}.mac-os .bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom-mac{overflow:inherit;background-size:contain!important;width:auto!important;min-width:260px!important;border:none!important;background-repeat:no-repeat!important}.mac-os .bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom-mac.download-started .button-copy .so-button{display:none}.mac-os .bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom-mac:hover{opacity:.9}.mac-os .bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom-mac:hover::before{content:none}.mac-os .bsx-deal-section .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac{width:auto;min-width:240px;margin-right:4px;padding:0 10px;overflow:inherit!important;border:none!important}.mac-os .bsx-deal-section .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac .so-button{display:flex!important}.mac-os .bsx-deal-section .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac.download-started .button-copy .so-button{display:flex}.mac-os .bsx-deal-section .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac::before{content:'';position:absolute;width:0;height:0;border-top:48px solid transparent;border-right:15px solid #5bce66;border-bottom:22px solid transparent;left:-13px;border-radius:4px;top:-1px;display:block!important;-webkit-transform:none!important;transform:none!important;background:0 0!important;opacity:1;-webkit-filter:none;filter:none;-webkit-animation:none;animation:none}.mac-os .bsx-deal-section .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac::after{content:'';position:absolute;width:0;height:0;border-top:48px solid transparent;border-right:15px solid #5bce66;border-bottom:22px solid transparent;right:-13px;border-radius:4px;-webkit-transform:rotate(180deg);transform:rotate(180deg);top:1px;display:block!important}.mac-os .bsx-deal-section .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac:hover{-webkit-filter:brightness(110%);filter:brightness(110%)}.mac-os .bsx-deal-section .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac:hover::before{border-top:48px solid transparent;border-right:15px solid #a4db80;border-bottom:22px solid transparent}.mac-os .bsx-deal-section .cta-layout.download-bsx.download-deal-bs5-btn-width-auto.custom-mac:hover::after{border-top:48px solid transparent;border-right:15px solid #a4db80;border-bottom:22px solid transparent}.mac-os .bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto{background:linear-gradient(#159aff 0,#15b9ff 100%)!important;border:1px solid rgba(255,255,255,.2);border-radius:6px}body:not(.mac-os) .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text div.hide{display:none}body:not(.mac-os) .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text .play-browser-wrapper.hide+.download-wrapper-layer.show{display:block;margin-left:0;margin-right:0}body:not(.mac-os) .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text .play-browser-wrapper.hide+.download-wrapper-layer.show .download-bsx::before{content:none}body:not(.mac-os) .bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text .download-wrapper-layer.show{display:block}body:not(.mac-os) .show{display:flex}}.bsx-deal-section .not-cloud-enabled-cta{border:1px solid #fff;background:0 0}.bsx-deal-section .not-cloud-enabled-cta:hover{background:0 0}@media (max-width:767px){.bsx-deal-section .hero-upper-text{padding:20px 0}.bsx-deal-section .bsx-redirect-lnk{height:320px}.bsx-deal-section .browser-cta .play-so-button{flex:1}.bsx-deal-section .browser-cta .play-so-button .main-str{height:30px;overflow:hidden;word-break:break-all;position:relative}.bsx-deal-section .browser-cta .play-so-button .main-str::after{content:'';bottom:0;height:25px;max-height:100%;pointer-events:none;position:absolute;right:0;width:60px;background:linear-gradient(to right,rgba(21,154,255,0),#159aff)}.bsx-deal-section .browser-cta:active .play-so-button .main-str::after,.bsx-deal-section .browser-cta:focus .play-so-button .main-str::after,.bsx-deal-section .browser-cta:hover .play-so-button .main-str::after,.bsx-deal-section .browser-cta:visited .play-so-button .main-str::after{content:none}.bsx-deal-section .hero-upper-text .hero-text .not-cloud-enabled+.cta-elements .browser-cta .play-so-button .main-str::after{background:linear-gradient(to right,rgba(14,42,62,0),#0e2a3e)}.bsx-deal-section .hero-upper-text .hero-text .not-cloud-enabled+.cta-elements .browser-cta:active .play-so-button .main-str::after,.bsx-deal-section .hero-upper-text .hero-text .not-cloud-enabled+.cta-elements .browser-cta:focus .play-so-button .main-str::after,.bsx-deal-section .hero-upper-text .hero-text .not-cloud-enabled+.cta-elements .browser-cta:hover .play-so-button .main-str::after,.bsx-deal-section .hero-upper-text .hero-text .not-cloud-enabled+.cta-elements .browser-cta:visited .play-so-button .main-str::after{content:none}.bsx-deal-section::after{content:none}.bsx-deal-section .investors-section{z-index:1}.bsx-deal-section .game-logo{height:70px;text-align:center}.bsx-deal-section .hero-upper-text .hero-text .is-cloud-enabled{justify-content:center;margin:0 auto 10px}.bsx-deal-section .hero-upper-text .hero-text .hero-heading{font-size:20px;line-height:30px;margin:12px 30px 12px 30px;width:auto}.bsx-deal-section .hero-upper-text .hero-text .hero-heading .break-word{font-size:20px;line-height:30px}.bsx-hero-section.bsx-deal-section .hero-upper-text .hero-text .hide{display:none}}.mac-os .bsx-deal-section.hide-on-mac-deal{display:none}.mac-os .bsx-deal-section.hide-on-mac-deal+script+.bsx-hero-section{display:block}.bsx-deal-section+script+.bsx-hero-section{display:none}body:not(.mac-os) .bsx-deal-section.hide-on-win-deal{display:none}body:not(.mac-os) .bsx-deal-section.hide-on-win-deal+script+.bsx-hero-section{display:block}@media (max-width:767px){.bsx-deal-section.hide-on-win-deal{display:none}.bsx-deal-section.hide-on-win-deal+script+.bsx-hero-section{display:block}.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements{flex-direction:column}.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .playstore-mobile-btn{display:block;background:url(https://cdn-www.bluestacks.com/bs-images/deal-play-store-btn.png) no-repeat;background-size:contain;width:auto;margin:0 auto;border:none;background-color:transparent;border-radius:0;font-size:0;height:38px;margin:0 auto;min-width:170px}.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .playstore-mobile-btn .button-copy{display:none}.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .playstore-mobile-btn:hover{box-shadow:none}.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .cta-layout.download-bs10::after,.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .cta-layout.download-bs10::before,.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .cta-layout.download-bsx::after,.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .cta-layout.download-bsx::before{content:none}.bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .mobile-cta{display:none}.bsx-home-page .bsx-hero-section.relative:not(.bsx-deal-section) .hero-upper-text .hero-text .cta-elements .mobile-cta{display:flex}.is-iPhone .bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .playstore-mobile-btn,.mobile-ios .bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .playstore-mobile-btn,.safari .bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .playstore-mobile-btn{display:none}.is-iPhone .bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .mobile-cta,.mobile-ios .bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .mobile-cta,.safari .bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements .mobile-cta{display:flex}}.mac-os .bsx-hero-section:not(.bsx-deal-section) .play-browser-wrapper{display:none}.mac-os .bsx-hero-section:not(.bsx-deal-section) .play-browser-wrapper .download-other-versions{display:none}.mac-os .bsx-hero-section:not(.bsx-deal-section) .download-wrapper-layer .download-other-versions{display:block}html[lang=ru-RU] .experiment_variant{font-family:Roboto,Arial,sans-serif}html[lang=ru-RU] .hero-upper-text .hero-text .hero-heading{font-family:Roboto,Arial,sans-serif!important}html[lang=ru-RU] .play-instantly-features .play-instant-item .play-heading{font-family:Roboto,Arial,sans-serif!important}html[lang=ru-RU] .play-instantly-features .play-instant-item .desc{font-family:Roboto,Arial,sans-serif!important}html[lang=ru-RU] .explore-all button{font-family:Roboto,Arial,sans-serif!important}html[lang=ru-RU] .language-selector button .selected-lang{font-family:Roboto,Arial,sans-serif!important}.experiment_variant .bsx-hero-section .container-autocomplete{z-index:2;position:relative}.experiment_variant .bsx-hero-section .container-autocomplete form{height:50px;border-radius:5.019px;background:#fff}.experiment_variant .bsx-hero-section .container-autocomplete form input{left:0;width:calc(100% - 40px);font-style:normal;font-family:inherit}.experiment_variant .bsx-hero-section .container-autocomplete form button{background:var(--primary-cta-colors-color-states-browser-default,linear-gradient(180deg,#159aff 0,#15b9ff 100%));height:100%;padding:16px}.experiment_variant .bsx-hero-section .container-autocomplete form button .search-icon use{fill:#fff}.experiment_variant .header-wrapper .container .right-section .container-autocomplete.hide{display:none}.experiment_variant .header-wrapper .container .right-section .container-autocomplete.hide+.search-wrapper.pc-visible{display:none!important}.experiment_variant .game-logo{display:none}.experiment_variant .hero-upper-text{top:200px;-webkit-transform:none;transform:none;bottom:0}.experiment_variant .hero-upper-text .hero-text{width:100%}.experiment_variant .hero-upper-text .hero-text .hero-heading{color:var(--primary-browser-50,#e8f5ff);-webkit-font-feature-settings:'clig' off,'liga' off;font-feature-settings:'clig' off,'liga' off;font-family:Montserrat;font-size:48px;font-weight:800;line-height:62.858px;padding:60px 0 25px;width:996px;margin:0 auto;text-align:center}.experiment_variant .hero-upper-text .hero-text.no-deal-heading .hero-heading{text-align:left;width:auto}.experiment_variant .hero-upper-text .hero-text.no-deal-heading .heading.break-word{display:flex}@media (min-width:768px) and (max-width:1024px){.experiment_variant .hero-upper-text .hero-text.no-deal-heading .heading.break-word{justify-content:flex-start}}.experiment_variant .hero-upper-text .hero-text.no-deal-heading .cta-elements{justify-content:flex-start}.experiment_variant .hero-upper-text .cta-elements{justify-content:center}.experiment_variant .hero-upper-text .download-bs5-wrapper{justify-content:center!important;max-width:100%}.experiment_variant .play-instantly-features{gap:15px;width:100%;margin-top:85px;z-index:1}@media (max-width:767px){.experiment_variant .play-instantly-features{display:none}}@media (min-width:768px) and (max-width:996px){.experiment_variant .play-instantly-features{display:none}.experiment_variant .play-instantly-features .play-instant-item{height:125px!important}}.experiment_variant .play-instantly-features .play-instant-item{border-radius:20px;border:1px solid rgba(201,222,255,.1);background:linear-gradient(92deg,rgba(201,222,255,.15) -16.05%,rgba(140,186,255,.02) 98.62%);-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);width:25%;padding:10px;gap:6px;height:102px;justify-content:center;position:relative}.experiment_variant .play-instantly-features .play-instant-item .new-tag-span{position:absolute;top:-18px;left:-8px}.experiment_variant .play-instantly-features .play-instant-item .new-tag-span figure{width:38px;height:38px}.experiment_variant .play-instantly-features .play-instant-item .play-heading{color:#fff;text-align:center;-webkit-font-feature-settings:'clig' off,'liga' off;font-feature-settings:'clig' off,'liga' off;font-family:Poppins;font-size:14px;font-weight:700;line-height:24px;gap:6px;justify-content:center;align-items:center}.experiment_variant .play-instantly-features .play-instant-item .title{overflow:hidden;max-width:80%;overflow:hidden;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:1}.experiment_variant .play-instantly-features .play-instant-item .desc{color:rgba(255,255,255,.8);text-align:center;font-feature-settings:'clig' off,'liga' off;font-family:Poppins;font-size:12px;font-weight:400;line-height:18px;padding-top:6px;box-orient:vertical;overflow:hidden;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:2}.experiment_variant .play-instantly-features .play-instant-item figure{width:25px;height:25px}.experiment_variant .play-instantly-features .play-instant-item-link:hover{box-shadow:0 0 4px 1px #fff}.experiment_variant .insta_play_cta{gap:10px;width:-webkit-max-content;width:-moz-max-content;width:max-content;min-width:212px;margin:0 auto;max-width:100%}@media (min-width:768px) and (max-width:1024px){.experiment_variant .insta_play_cta{display:none}}.experiment_variant .insta_play_cta .download-button-mobile,.experiment_variant .insta_play_cta .try-instantly-cta{width:100%;height:50px;border-radius:7.133px;border:1.136px solid rgba(255,255,255,.4);gap:10px;justify-content:flex-start;padding:14px;height:-webkit-max-content;height:-moz-max-content;height:max-content}.experiment_variant .insta_play_cta .download-button-mobile img,.experiment_variant .insta_play_cta .try-instantly-cta img{width:32px}.experiment_variant .insta_play_cta .download-button-mobile .so-btn,.experiment_variant .insta_play_cta .try-instantly-cta .so-btn{color:var(--gray-0,#fff);font-size:16px;font-weight:600}@media (max-width:767px){.experiment_variant .insta_play_cta .download-button-mobile .so-btn,.experiment_variant .insta_play_cta .try-instantly-cta .so-btn{align-items:flex-start}}.experiment_variant .insta_play_cta .download-button-mobile .so-btn .light-str,.experiment_variant .insta_play_cta .try-instantly-cta .so-btn .light-str{color:var(--white-white,#fff);font-size:12px;font-weight:400}.experiment_variant .insta_play_cta .download-button-mobile{background:var(--primary-cta-colors-color-states-browser-default,linear-gradient(180deg,#159aff 0,#15b9ff 100%));z-index:9;display:none}.experiment_variant .insta_play_cta #download-bstk-btn-android *{pointer-events:none}.experiment_variant .insta_play_cta .try-instantly-cta{background:var(--primary-cta-colors-color-states-product-default,linear-gradient(279deg,#5bce66 0,#9bce5b 100%))}.experiment_variant .insta_play_cta .insta-play-explore{border-radius:3.933px;border:1.311px solid #fff;background:rgba(255,255,255,.1);height:48px;display:flex;justify-content:center;align-items:center;color:#fff;text-align:center;text-shadow:0 6.28982px 6.28982px rgba(0,0,0,.25);font-size:16.644px;font-weight:600;line-height:14.267px}.experiment_variant .insta_play_cta .insta-info{position:fixed;top:0;left:0;width:100%;z-index:99999;height:100%;background:var(--Gradients-Primary-BG,linear-gradient(180deg,#011133 0,#0e2a3e 100%));-webkit-backdrop-filter:blur(5.1089px);backdrop-filter:blur(5.1089px);justify-content:flex-start}.experiment_variant .insta_play_cta .insta-info:not(.show){display:none}.experiment_variant .insta_play_cta .insta-info .cross-insta-icon{position:absolute;right:9px;top:9px;z-index:999}.experiment_variant .insta_play_cta .insta-info .gradient-bg{height:700px;width:100%;z-index:1;background:url(https://cdn-www.bluestacks.com/bs-images/gradient_bg.png) no-repeat;display:flex;flex-direction:column;background-size:contain;align-items:center;justify-content:center}.experiment_variant .insta_play_cta .insta-info .gradient-bg img{max-width:100%;height:180px}.experiment_variant .insta_play_cta .insta-info .heading{color:#fff;text-align:center;font-size:16px;font-weight:500;line-height:24px;margin-bottom:20px;padding-bottom:12px;position:relative;padding:0 20px}.experiment_variant .insta_play_cta .insta-info .explore-cloud-games,.experiment_variant .insta_play_cta .insta-info .try-top-games{border-radius:7.133px;border:1.136px solid rgba(255,255,255,.4);width:270px;padding:0 14px;color:var(--gray-0,#fff);font-size:16px;font-weight:600;text-align:center;margin:0 20px;height:50px}.experiment_variant .insta_play_cta .insta-info .try-top-games{background:var(--primary-cta-colors-color-states-product-default,linear-gradient(279deg,#5bce66 0,#9bce5b 100%));align-items:center;display:flex;line-height:normal;gap:10px}.experiment_variant .insta_play_cta .insta-info .try-top-games .so-btn{font-size:16px;font-weight:600;line-height:18px;justify-content:center;align-items:flex-start;height:100%}.experiment_variant .insta_play_cta .insta-info .try-top-games .so-btn .light-str{font-size:12px;font-weight:400;line-height:normal}.experiment_variant .insta_play_cta .insta-info .explore-cloud-games{background:var(--white-white-20,rgba(255,255,255,.2));margin-top:7px}.experiment_variant .insta_play_cta .insta-info p{flex:1}html[lang=pt-br] .play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-heading{font-size:22px}html:not(.experiment_variant) .hero-search-wrapper,html:not(.experiment_variant) .insta_play_cta{display:none}html:not(.experiment_variant) .play-instantly-features,html:not(.experiment_variant) .play-instantly-section,html:not(.experiment_variant) .section-backed-by{display:none}.play-instantly-hp .header-wrapper{background:linear-gradient(180deg,rgba(2,19,52,.56) 0,rgba(2,18,52,.4) 50.52%,rgba(2,19,52,.11) 100%);-webkit-backdrop-filter:blur(20px);backdrop-filter:blur(20px);height:60px}.play-instantly-hp .header-wrapper .container .left-section .menu-list>li:hover .sub-menu{top:60px}@media (max-width:1024px){.play-instantly-hp .header-wrapper .container .left-section .menu-list>li:hover .sub-menu{top:auto}}.play-instantly-hp .play-instantly-section{background:linear-gradient(348deg,#0e2a3e 31.62%,#011133 90.19%);padding:52px 0}.play-instantly-hp .play-instantly-section .container{border-radius:20px;border:1px solid rgba(255,255,255,.1);background:linear-gradient(180deg,rgba(201,222,255,.05) 0,rgba(140,186,255,0) 100%);-webkit-backdrop-filter:blur(5px);backdrop-filter:blur(5px);max-width:1272px;padding:25px 0}@media (max-width:1272px){.play-instantly-hp .play-instantly-section .container{width:98%}}.play-instantly-hp .play-instantly-section .play-heading{color:#fff;-webkit-font-feature-settings:'clig' off,'liga' off;font-feature-settings:'clig' off,'liga' off;font-family:Poppins;font-size:36px;text-align:center;font-weight:700;line-height:72px}.play-instantly-hp .play-instantly-section .apps-list{width:1080px;margin:24px auto 0;justify-content:space-between;gap:34px;max-width:100%}.play-instantly-hp .play-instantly-section .apps-list .app-layer{width:460px;padding:20px;gap:20px}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-heading{gap:10px;color:#fff;-webkit-font-feature-settings:'clig' off,'liga' off;font-feature-settings:'clig' off,'liga' off;font-family:Poppins;font-size:24px;font-weight:600;line-height:40px}.play-instantly-hp .play-instantly-section .apps-list .app-layer .list{align-items:center;gap:20px;position:relative}.play-instantly-hp .play-instantly-section .apps-list .app-layer .list .svg-bg-icon{position:absolute;width:322.362px;height:322.362px;-webkit-filter:blur(20px);filter:blur(20px);top:-20px}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list{flex-wrap:wrap;height:260px;justify-content:flex-start;align-items:center;row-gap:18px;z-index:1}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list li{padding:0 4px}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list li:hover{border-radius:8px;background:rgba(255,255,255,.05);box-shadow:0 5.01996px 10.03992px 0 rgba(0,0,0,.05);padding:0 4px}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list .google-play-item{width:222px;height:74px;align-items:center}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list .google-play-item .step-name{-webkit-font-feature-settings:'clig' off,'liga' off;font-feature-settings:'clig' off,'liga' off;font-family:Poppins;font-size:60px;font-style:normal;font-weight:600;line-height:177.186px;background:linear-gradient(180deg,#fff -17.43%,rgba(255,255,255,0) 82.37%);background-clip:text;-webkit-background-clip:text;-webkit-text-fill-color:transparent;display:flex;width:26px;height:70.1px;flex-direction:column;justify-content:center;flex-shrink:0}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list .google-play-item .step-image{height:64px;width:64px;border-radius:16px;overflow:hidden;background:#d3d3d3 50%/cover no-repeat}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list .google-play-item .step-image img{height:100%;width:100%}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list .google-play-item .content{margin-left:10px;gap:4px;justify-content:center;max-width:120px}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list .google-play-item .content .title{color:var(--gray-100,#cccdd0);font-size:14px;font-weight:700;line-height:20px;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:2;overflow:hidden;text-overflow:ellipsis}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list .google-play-item .content .developer{color:var(--gray-50,#e6e6e7);font-size:12px;font-weight:400;line-height:1.5;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:1;overflow:hidden;text-overflow:ellipsis}.play-instantly-hp .play-instantly-section .apps-list .app-layer .try-instantly-btn{border-radius:6px;border:1px solid rgba(255,255,255,.6);display:flex;width:195px;height:50px;padding:13px 18px;justify-content:center;align-items:center;gap:8px;color:#fff;font-size:16px;font-weight:600;line-height:28px;z-index:1}.play-instantly-hp .play-instantly-section .apps-list .app-layer .try-instantly-btn svg{width:24px}.play-instantly-hp .play-instantly-section .apps-list .app-layer .try-instantly-btn:hover{background:linear-gradient(279deg,#5bce66 0,#9bce5b 100%)}.play-instantly-hp .bg-section{background:linear-gradient(348deg,#0e2a3e 31.62%,#011133 90.19%);padding:40px 0}.section-backed-by{background:#000d25}.section-backed-by .container{display:flex;justify-content:center;align-items:center;gap:31.06px;min-height:34px}.section-backed-by .container h3{color:#fff;text-align:center;-webkit-font-feature-settings:'clig' off,'liga' off;font-feature-settings:'clig' off,'liga' off;font-family:Poppins;font-size:14px;font-weight:700;line-height:24px}.section-backed-by .container ul{display:flex;justify-content:center;align-items:center;gap:31.06px}@media (max-width:1080px) and (min-width:768px){.play-instantly-hp .play-instantly-section .apps-list .app-layer{width:48%;padding:20px 4px}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list li{width:calc(50% - 9px)}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list li .google-play-item{width:100%}.play-instantly-hp .play-instantly-section .apps-list .app-layer .google-play-list li .google-play-item .content{flex:1}.app-section .tab-section .tab-content .top-games-content .tab-inner-content .app-list{display:flex}}@media (max-width:767px){.experiment_variant .bsx-home-page .bsx-hero-section.relative .hero-upper-text .hero-text .cta-elements{display:none!important}.experiment_variant .bsx-hero-section .bsx-video::after{background:linear-gradient(180deg,rgba(0,0,0,0) 65.7%,#000d25 97.56%),linear-gradient(0deg,var(--translucent-black-40,rgba(0,0,0,.4)) 0,var(--translucent-black-40,rgba(0,0,0,.4)) 100%) -113.141px 0/253.333% 100% no-repeat!important}.experiment_variant .bsx-deal-section .bsx-redirect-lnk{height:430px}.experiment_variant .bsx-hero-section .bsx-video{height:430px}.experiment_variant .bsx-hero-section .hero-upper-text{top:unset;bottom:0;-webkit-transform:none;transform:none;padding:120px 0 50px}.experiment_variant .bsx-hero-section .hero-upper-text .container-autocomplete{display:block}.experiment_variant .bsx-hero-section .hero-upper-text .container-autocomplete #mobile-search-opner{display:none}.experiment_variant .bsx-hero-section .hero-upper-text .container-autocomplete form{height:30px}.experiment_variant .bsx-hero-section .hero-upper-text .container-autocomplete form input{height:30px;width:calc(100% - 34px);font-size:12px;font-style:normal;line-height:15.057px}.experiment_variant .bsx-hero-section .hero-upper-text .container-autocomplete form button{width:34px;padding:8px}.experiment_variant .bsx-hero-section .hero-upper-text .container-autocomplete form button svg{height:12px;width:12px}.experiment_variant .bsx-hero-section .hero-upper-text .hero-text .hero-heading{font-size:20px;font-weight:800;line-height:28px;padding:14px 0 30px 0}.play-instantly-hp .play-instantly-section{padding:16px 10px}.play-instantly-hp .play-instantly-section .container{border:1px solid rgba(255,255,255,.4);box-shadow:none;width:100%;padding:20px 0}.play-instantly-hp .play-instantly-section .container .play-heading{font-size:16px;font-style:normal;font-weight:700;line-height:22.939px}.play-instantly-hp .play-instantly-section .container .apps-list{width:100%;flex-direction:column-reverse}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer{padding:0 16px;width:100%}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-heading{font-size:12px;line-height:20px;align-items:center}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-heading svg{height:16px;width:16px}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list{-webkit-column-gap:unset;-moz-column-gap:unset;column-gap:unset;row-gap:12px;width:100%;height:190px}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list li{width:48%;justify-content:center}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list .google-play-item{border-radius:5.272px;border:.659px solid #fff;background:rgba(255,255,255,.05);height:54px;width:100%;justify-content:center}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list .google-play-item .step-name{font-size:39px;width:20px}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list .google-play-item .step-image{height:42px;width:42px}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list .google-play-item .content{gap:0;margin-left:4px;flex:1}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list .google-play-item .content .title{font-size:9.226px;line-height:12px}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list .google-play-item .content .developer{font-size:7.908px;line-height:11.861px}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .try-instantly-btn{border-radius:4.808px;border:.801px solid rgba(255,255,255,.6);background:rgba(255,255,255,.1);width:100%;height:40px;font-size:12px}.section-backed-by .container{flex-direction:row;gap:14px;padding:14px;align-items:flex-start}}@media (max-width:767px) and (max-width:767px){.section-backed-by .container{gap:8px}}@media (max-width:767px){.section-backed-by .container .investor-list{flex-wrap:wrap;row-gap:4px;-webkit-column-gap:14px;-moz-column-gap:14px;column-gap:14px;flex:1;justify-content:flex-start}}@media (max-width:767px) and (max-width:767px){.section-backed-by .container .investor-list{-webkit-column-gap:4px;-moz-column-gap:4px;column-gap:4px}.section-backed-by .container .investor-list svg{height:24px;width:42px}.section-backed-by .container .investor-list li:nth-child(4) svg{width:50px}.section-backed-by .container .investor-list li:nth-child(5) svg{width:56px}.section-backed-by .container .investor-list li:nth-child(6) svg{width:92px}.section-backed-by .container .investor-list li:nth-child(7) svg{width:86px}}@media (min-width:700px) and (max-width:767px){.experiment_variant .bsx-hero-section .hero-upper-text{width:60%;margin:0 auto}.play-instantly-hp .play-instantly-section .container{width:80%}.play-instantly-hp .play-instantly-section .container .apps-list .app-layer .google-play-list li{width:45%}}@media (min-width:1025px) and (max-width:1200px){.chrome-mobile.mobile-android .mobile-cta,.is-android .mobile-cta{display:none!important}}@media (min-width:768px){html[lang=en-US].experiment_variant .mac-os .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-bsx:not(.download-bs10):not(.download-bs5){background:linear-gradient(180deg,#15a1ff 6.89%,#15a3ff 92.13%,#15b4ff 180.91%)!important}html[lang=en-US].experiment_variant .mac-os .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-bsx:not(.download-bs10):not(.download-bs5):hover{-webkit-filter:brightness(110%);filter:brightness(110%)}html[lang=en-US].experiment_variant .mac-os .bsx-deal-section .cta-layout.download-deal-bs5-btn-width-auto.custom.download-started .button-copy .so-button{display:flex}html[lang=en-US].experiment_variant .mac-os .header-wrapper .container .right-section .download-buttons .download-button,html[lang=en-US].experiment_variant .mac-os .unlock-content .cta-elements .download-bsx:not(.download-bs10){background:linear-gradient(180deg,#15a1ff 6.89%,#15a3ff 92.13%,#15b4ff 180.91%)!important}html[lang=en-US].experiment_variant .mac-os .header-wrapper .container .right-section .download-buttons .download-button:hover,html[lang=en-US].experiment_variant .mac-os .unlock-content .cta-elements .download-bsx:not(.download-bs10):hover{-webkit-filter:brightness(110%);filter:brightness(110%)}}.is-android #download-bstk-btn-android{display:flex}.is-iPhone #download-bstk-btn-mobile{display:flex}[lang=ar] .download-page .main-container .cloud-games-section .container .download-banner{flex-direction:row-reverse}.download-page .main-container{position:relative;z-index:2;background:linear-gradient(180deg,#020d24 0,#112530 100%)}.download-page .main-container .cloud-games-section{padding-top:80px;justify-content:center}.download-page .main-container .cloud-games-section .container{display:flex;padding:20px 10px 60px;align-items:center}.download-page .main-container .cloud-games-section .container .download-banner{background-image:url(https://cdn-www.bluestacks.com/bs-images/download-page-banner.jpeg);width:100%;height:385px;background-size:contain;display:flex;align-items:center;justify-content:flex-end;border-radius:20px;overflow:hidden;background-color:#212241;background-repeat:no-repeat;background-size:auto 100%}@media (max-width:767px){.download-page .main-container .cloud-games-section .container .download-banner{display:flex;align-items:flex-end;background-size:736px;background-repeat:no-repeat;align-items:flex-end;height:448px}}@media (max-width:767px){.download-page .main-container .cloud-games-section .container-full{margin:0 auto;max-width:calc(100% - 40px)}.download-page .main-container .cloud-games-section .container-full .install-mobile-app{display:none;width:100%;height:50px;border-radius:7.133px;border:1.136px solid rgba(255,255,255,.4);background:var(--primary-cta-colors-color-states-browser-default,linear-gradient(180deg,#159aff 0,#15b9ff 100%));z-index:9;gap:10px;justify-content:center;padding:14px;height:-webkit-max-content;height:-moz-max-content;height:max-content;color:#fff;font-size:16px;font-weight:600}.download-page .main-container .cloud-games-section .container-full .know-more-btn{display:flex!important;cursor:pointer;background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);color:#fff;display:flex!important;cursor:pointer;background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);color:#fff;padding:10px 20px;font-weight:600;font-size:16px;line-height:24px;display:flex;align-items:center;justify-content:center;width:auto;border-radius:6px;font-family:Poppins;margin-bottom:20px}.download-page .main-container .cloud-games-section .container-full .download-button-mobile{display:none}}.download-page .main-container .cloud-games-section .cloud-video{width:520px;height:400px;position:relative}.download-page .main-container .cloud-games-section .heading-layer{margin:40px;align-items:center;padding-bottom:0;flex-direction:column;text-align:center;display:block;flex:none;height:100%;margin:0;display:flex;justify-content:center;padding:64px;background:linear-gradient(270deg,#2e1338 -2.1%,rgba(46,19,56,0) 99.82%)}.download-page .main-container .cloud-games-section .heading-layer .heading{font-size:38px;line-height:56px;text-align:center}.download-page .main-container .cloud-games-section .heading-layer .sub-heading{font-size:20px;margin-top:10px;color:#fff;font-weight:400;text-align:center;line-height:32px;font-family:Roboto}.download-page .main-container .cloud-games-section .heading-layer .cta-wrapper{display:inline-block;text-align:left}.download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements{margin-top:0;justify-content:center}@media (max-width:767px){.download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements{margin-top:24px}}.download-page .main-container .common-bg{z-index:0!important;position:relative}.download-other-versions{display:flex;align-items:center}[lang=ar] .browser-cta .play-so-button{align-items:flex-start}[lang=ar] .download-other-versions{flex-direction:row-reverse}@media (max-width:767px){.download-other-versions{display:none}.download-page .main-container .cloud-games-section{padding-bottom:0}.download-page .main-container .cloud-games-section .container{padding:0 10px;flex-direction:column}}@media (max-width:767px) and (max-width:767px){.download-page .main-container .cloud-games-section .container{background:linear-gradient(359deg,#2e1338 1.22%,#1e1d41 57.07%,rgba(46,19,56,0) 98.86%);width:96%;border-radius:20px;padding:0}}@media (max-width:767px){.download-page .main-container .cloud-games-section .container .cloud-video{max-width:100%;height:240px}.download-page .main-container .cloud-games-section .container .heading-layer{margin-left:0;margin-top:32px;width:100%;padding-bottom:40px;background:linear-gradient(359deg,#2e1338 1.22%,#1e1d41 57.07%,rgba(46,19,56,0) 98.86%);height:86%;padding:0;padding-top:136px;margin:0;padding:16px}.download-page .main-container .cloud-games-section .container .heading-layer .heading{font-size:30px;line-height:46px}.download-page .main-container .cloud-games-section video{max-height:100%;max-width:100%}.download-page .main-container .cloud-games-section .download-bsx{margin-left:0;margin-bottom:12px}.download-page .main-container .cloud-games-section .cta-layout.download-bsx{display:none}.download-page .main-container .cloud-games-section .cta-wrapper{max-width:90%}.download-page .main-container .cloud-games-section .cta-wrapper .cta-elements{flex-direction:column-reverse;margin-top:24px;align-items:center}}@media (min-width:768px){.mob-visible{display:none!important}}@media (min-width:768px) and (max-width:991px){.download-page .main-container .cloud-games-section .container{flex-direction:column}.download-page .main-container .cloud-games-section .container .heading-layer{margin-left:0}.download-page .main-container .cloud-games-section .cloud-video{display:flex;justify-content:center;align-items:center}.download-page .main-container .cloud-games-section .cloud-video img{height:auto;width:auto;max-width:100%}}.top-games-section{padding:40px;margin:0 auto;display:none;z-index:0!important}.top-games-section .header{display:flex;justify-content:space-between;align-items:center}.top-games-section .title{color:#fff;font-size:24px;font-weight:700}.top-games-section .view-all{background-color:#3b82f6;color:#fff;padding:8px 16px;border-radius:6px;text-decoration:none;font-size:14px;cursor:pointer}.top-games-section .view-all:hover{background:linear-gradient(#159aff 0,#15b9ff 100%)}.top-games-section .games-grid{display:flex;gap:12px;flex-wrap:wrap}.top-games-section .game-card{width:180px;margin-top:30px}.top-games-section .game-card-hover{position:absolute;width:100%;height:100%;top:0;left:0;max-width:100%;max-height:100%;background:#000000d6;opacity:0;transition:opacity .5s ease;display:flex;justify-content:center;align-items:center;padding-bottom:40px}.top-games-section .game-card-hover img{width:100%;height:auto}.top-games-section .game-card-hover:hover{opacity:1}.top-games-section .game-image-container{position:relative;margin-bottom:10px;border-radius:12px;overflow:hidden;aspect-ratio:1/1}.top-games-section .game-image{width:180px;height:180px;border-radius:12px;-o-object-fit:cover;object-fit:cover;max-width:100%;max-height:100%}.top-games-section .rating{position:absolute;top:8px;left:8px;background-color:rgba(0,0,0,.6);color:gold;padding:4px 8px;border-radius:4px;font-size:12px;display:flex;align-items:center;gap:4px;z-index:1}.top-games-section .game-title{color:#fff;font-size:16px;font-weight:700;margin:0;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:1;overflow:hidden;text-overflow:ellipsis}.top-games-section .developer{color:#64748b;font-size:14px;margin:4px 0 0 0;-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:1;overflow:hidden;text-overflow:ellipsis}@media (max-width:767px){.top-games-section .games-grid{gap:0;display:flex;flex-wrap:wrap;justify-content:center}.top-games-section .game-card{width:100px;margin:0 4px;margin-top:30px}.top-games-section .header{margin-top:40px}.top-games-section .all-versions{padding-top:40px!important}.top-games-section .game-card .pc-visible{display:inline!important}.top-games-section .game-card .game-card-hover{pointer-events:none}}.top-games-section .download-banner{max-width:calc(100%);width:calc(100% - 60px);margin:0 auto}.top-games-section .card-download-btn{position:absolute;bottom:0;left:0;border-radius:6px;border:1px solid rgba(255,255,255,.2);background:var(--primary-cta-colors-color-states-browser-default,linear-gradient(180deg,#159aff 0,#15b9ff 100%));z-index:2;width:100%;height:44px;display:flex;font-size:16px;color:#fff;font-weight:600;justify-content:center;align-items:center}.all-versions{padding:96px 0}.all-versions .heading{font-size:36px;line-height:48px;text-align:center;color:#fff;margin-bottom:0}.all-versions .download-version-list{margin-top:32px}.all-versions .download-version-list .version-li{width:1000px;margin:0 auto;margin-bottom:50px;display:flex;padding:40px 40px 0;align-items:center;max-width:100%;border-radius:8px}.all-versions .download-version-list .version-li:last-child{margin-bottom:0}.all-versions .download-version-list .version-li .l-section{flex:1}.all-versions .download-version-list .version-li .l-section figure{text-align:center}.all-versions .download-version-list .version-li .v-heading{font-weight:700;font-size:24px;line-height:32px;color:#fff;text-align:center;margin-top:15px;margin-bottom:0}.all-versions .download-version-list .version-li .r-section{width:555px;position:relative;margin-left:102px}.all-versions .download-version-list .version-li .r-section .section-block{background-color:#003155;border-radius:8px;padding:16px 32px}.all-versions .download-version-list .version-li .r-section .section-block:first-child{margin-bottom:32px;padding:32px}.all-versions .download-version-list .version-li .r-section .section-block .heading-group{display:flex;justify-content:space-between;align-items:center}@media (max-width:767px){.all-versions .download-version-list .version-li .r-section .section-block .heading-group{justify-content:center}}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs{border:2px solid #159aff;font-weight:500;font-size:14px;line-height:20px;display:flex!important;align-items:center;text-align:center;color:#159aff;width:150px;justify-content:center;height:40px;transition:.3s ease-in;position:relative;border-radius:6px;letter-spacing:.5px}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs *{pointer-events:none}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs .download-loader{position:absolute;left:0;bottom:-2px;height:2px;background:#52cbff;width:100%;overflow:hidden}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs .download-loader::after{position:absolute;left:-100%;content:'';display:block;background-color:#fff;height:2px;width:100%;-webkit-animation:getWidth .5s ease-in infinite;animation:getWidth .5s ease-in infinite}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs.download-started{background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);color:#fff}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs.download-started.retry-link{padding:0 6px}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs.download-started.retry-link .button-copy{width:90%}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs.download-started.retry-link .button-copy .so-button{width:100%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;display:block;font-size:14px;line-height:20px;color:#fff}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs.download-started.retry-link .button-copy .so-button *{font-size:14px;line-height:20px;color:#fff}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs.download-started.retry-link .retry-button{position:absolute;right:0;width:51px;height:100%;display:flex;align-items:center;justify-content:center;display:none}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs:hover{background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);color:#fff}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-not-available{border:2px solid #7788a3;font-weight:500;font-size:14px;line-height:20px;display:flex!important;align-items:center;color:#7788a3;width:150px;text-align:center;height:40px;justify-content:center;border-radius:6px}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-not-available .btn-txt{width:90%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap}@media (max-width:768px){.all-versions .download-version-list .version-li .r-section .l-side{text-align:center}}.all-versions .download-version-list .version-li .r-section .l-side .title{font-size:18px;line-height:24px;color:#f5f5f5;margin-left:8px;font-weight:600}.all-versions .download-version-list .version-li .r-section .l-side .title .subtitle{font-size:14px;color:rgba(255,255,255,.6)}.all-versions .download-version-list .version-li .r-section .l-side .desc{font-size:12px;line-height:24px;color:#9ca2b7;margin:0}.all-versions .download-version-list .version-li .r-section .versions-list{border-top:2px solid #556789;margin-top:12px;padding-top:16px}.all-versions .download-version-list .version-li .r-section .versions-list .version-item{display:flex;justify-content:space-between;margin-bottom:32px;position:relative;align-items:center}.all-versions .download-version-list .version-li .r-section .versions-list .version-item span{color:#f1f1f4;font-size:16px;line-height:24px}.all-versions .download-version-list .version-li .r-section .versions-list .version-item span .subtext{font-size:14px;display:block;color:rgba(255,255,255,.6)}.all-versions .download-version-list .version-li .r-section .versions-list .version-item:last-child{margin-top:32px;margin-bottom:0}.all-versions .download-version-list .version-li .r-section .versions-list .version-item:last-child::after{content:none}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs{border:2px solid #159aff;font-weight:500;font-size:14px;line-height:20px;display:flex!important;align-items:center;text-align:center;color:#159aff;width:150px;justify-content:center;height:40px;transition:.3s ease-in;position:relative;border-radius:6px;letter-spacing:.5px}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs *{pointer-events:none}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs .download-loader{position:absolute;left:0;bottom:-2px;height:2px;background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);width:100%;overflow:hidden}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs .download-loader::after{position:absolute;left:-100%;content:'';display:block;background-color:#fff;height:2px;width:100%;-webkit-animation:getWidth .5s ease-in infinite;animation:getWidth .5s ease-in infinite}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started{background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);color:#fff}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link,.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link-bs5{padding:0 6px}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link .button-copy,.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link-bs5 .button-copy{width:90%}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link .button-copy .so-button,.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link-bs5 .button-copy .so-button{width:100%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;display:block;font-size:14px;line-height:20px;color:#fff}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link .button-copy .so-button *,.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link-bs5 .button-copy .so-button *{font-size:14px;line-height:20px;color:#fff}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link .retry-button,.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link-bs5 .retry-button{position:absolute;right:0;width:51px;height:100%;display:flex;align-items:center;justify-content:center;display:none}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs:hover{background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);color:#fff}@media (min-width:768px) and (max-width:1000px){.all-versions .container{max-width:1000px}}@media (min-width:768px) and (max-width:1024px){.all-versions .download-version-list .version-li:last-child{padding:68px 40px}.all-versions .download-version-list .version-li .l-section{width:30%}.all-versions .download-version-list .version-li .r-section{width:65%;margin-left:60px}.all-versions .download-version-list .version-li .r-section .versions-list .version-item span:not(.subtext){width:45%}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs{width:50%}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs,.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-not-available{width:50%}}@media (max-width:767px){.all-versions{padding-top:30px}.all-versions .container{z-index:unset}.all-versions .heading{font-size:24px;margin-bottom:28px;line-height:32px}.all-versions .download-version-list{overflow:hidden;margin-top:0}.all-versions .download-version-list .version-li{max-width:100%;width:auto;height:auto;padding:23px 45px;flex-direction:column;justify-content:center;margin-bottom:28px;border-radius:8px}.all-versions .download-version-list .version-li .l-section .bs-version-logo{height:140px}.all-versions .download-version-list .version-li .l-section .bs-version-logo img{max-width:100%;max-height:100%;width:auto}.all-versions .download-version-list .version-li .r-section{width:90%;padding:20px 0 0;margin:0}.all-versions .download-version-list .version-li .r-section .l-side .title{font-weight:500}.all-versions .download-version-list .version-li .r-section::after{content:none}.all-versions .download-version-list .version-li .r-section .section-block{padding:8px}.all-versions .download-version-list .version-li .r-section .section-block:first-child{padding:26px 19px 20px}.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs,.all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-not-available{display:none!important}.all-versions .download-version-list .version-li .r-section .versions-list{margin-top:18px;padding-top:18px}.all-versions .download-version-list .version-li .r-section .versions-list .version-item{flex-direction:column;justify-content:center;align-items:center;padding-bottom:0;margin-bottom:16px}.all-versions .download-version-list .version-li .r-section .versions-list .version-item span{text-align:center}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.pc-visible{margin-top:18px;display:none!important}.all-versions .download-version-list .version-li .r-section .versions-list .version-item:last-child{margin-top:18px}.all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-not-available.pc-visible{display:none!important}.all-versions .download-version-list .version-li .r-section .mob-note{font-size:12px;line-height:20px;text-align:center;letter-spacing:2px;text-transform:uppercase;color:#fff;margin:20px 0 0}.all-versions .download-version-list .version-li .r-section .title .subtitle{display:block;padding-left:10px;margin-top:10px}.download-page .faq-section .container .faq-list .faq-item .faq-q{padding-right:24px}}body[data-lang=ar] .all-versions .download-version-list .version-li .r-section{margin-left:0}@media (min-width:768px){body[data-lang=ar] .all-versions .download-version-list .version-li .r-section{margin-right:102px}}body[data-lang=ar] .all-versions .download-version-list .version-li .r-section .l-side .title{margin-left:0;margin-right:8px}body[data-lang=ar] .all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link,body[data-lang=ar] .all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link-bs5{padding:0 6px}body[data-lang=ar] .all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link .retry-button,body[data-lang=ar] .all-versions .download-version-list .version-li .r-section .versions-list .version-item .download-bs.download-started.retry-link-bs5 .retry-button{right:unset;left:0;display:none}body[data-lang=ar] .all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs.download-started.retry-link{padding:0 6px}body[data-lang=ar] .all-versions .download-version-list .version-li .r-section .section-block .heading-group .download-bs.download-started.retry-link .retry-button{right:unset;left:0;display:none}.bsx-hero-section .bsx-video{height:640px}.experiment_variant .hero-upper-text .cta-elements.two-buttons+.download-bs5-wrapper{max-width:900px}.experiment_variant .hero-upper-text .download-bs5-wrapper .download-bs5-link::after{right:-15px;content:'';border:solid rgba(255,255,255,.9);border-width:0 2px 2px 0;display:inline-block;padding:3px;-webkit-transform:rotate(315deg) translateY(-50%);transform:rotate(315deg) translateY(-50%);position:absolute;top:calc(50% - 1px);transition:.3s ease-in-out}.download-page .header-wrapper .container .right-section .show{padding:12px 0}.header-wrapper .container .right-section .download-buttons .cta-layout.us-download-btn{display:none}.container{z-index:1;position:relative}body[data-lang=ar]{text-align:unset}.bsx-hero-section .hero-upper-text .container{position:static}.bsx-hero-section .hero-upper-text .container .hero-text{text-align:center}.bsx-hero-section .hero-upper-text .container .hero-text.no-deal-heading{text-align:left}.bsx-hero-section .hero-upper-text .container .cta-elements{display:inline-flex;text-align:left}.terms-n-condition{min-width:270px;max-width:300px}.terms-n-condition label{padding-left:22px;line-height:20px}.terms-n-condition label:not(.no-checkbox)::after,.terms-n-condition label:not(.no-checkbox)::before{left:0}.terms-n-condition label:not(.no-checkbox)::after{top:18px}.terms-n-condition input:checked+label:not(.no-checkbox):after{left:6px}.terms-n-condition .no-checkbox{padding-left:0}body.mac-os .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements{flex-direction:column-reverse;align-items:center}body.mac-os .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements .cta-layout .download-icon{margin-right:18px;padding-right:0}body.mac-os .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements .cta-layout .download-icon .download-icon{margin-right:0}body.mac-os .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements .cta-layout .download-icon::after{content:none}body.mac-os .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements .browser-cta{margin-top:12px}body.mac-os .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements .browser-cta .browser-icon{display:flex}body.mac-os .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements .download-bsx:not(.download-bs10){margin-left:0;padding:0 24px;margin-top:20px;margin-bottom:8px}@media (max-width:1024px){.info-popup{z-index:99}}:not(.experiment_variant) .insta_play_cta{display:none}.download-bsx.download-started{padding:0 16px}.experiment_variant .cta-know-more{display:none}.experiment_variant .insta_play_cta{gap:10px;display:flex}.experiment_variant .insta_play_cta .download-button-mobile,.experiment_variant .insta_play_cta .try-instantly-cta{width:242px;height:50px;border-radius:7.133px;border:1.136px solid rgba(255,255,255,.4);gap:10px;justify-content:flex-start;padding:14px}.experiment_variant .insta_play_cta .download-button-mobile img,.experiment_variant .insta_play_cta .try-instantly-cta img{width:32px}.experiment_variant .insta_play_cta .download-button-mobile .so-btn,.experiment_variant .insta_play_cta .try-instantly-cta .so-btn{color:var(--gray-0,#fff);font-size:16px;font-weight:600}.experiment_variant .insta_play_cta .download-button-mobile .so-btn .light-str,.experiment_variant .insta_play_cta .try-instantly-cta .so-btn .light-str{color:var(--white-white,#fff);font-size:12px;font-weight:400}.experiment_variant .insta_play_cta .download-button-mobile{background:var(--primary-cta-colors-color-states-browser-default,linear-gradient(180deg,#159aff 0,#15b9ff 100%))}.experiment_variant .insta_play_cta .try-instantly-cta{background:var(--primary-cta-colors-color-states-product-default,linear-gradient(279deg,#5bce66 0,#9bce5b 100%))}.experiment_variant .insta_play_cta .insta-play-explore{border-radius:3.933px;border:1.311px solid #fff;background:rgba(255,255,255,.1);height:48px;display:flex;justify-content:center;align-items:center;color:#fff;text-align:center;text-shadow:0 6.28982px 6.28982px rgba(0,0,0,.25);font-size:16.644px;font-weight:600;line-height:14.267px}.experiment_variant .insta_play_cta .insta-info{position:fixed;top:0;left:0;width:100%;z-index:99999;height:100%;background:var(--Gradients-Primary-BG,linear-gradient(180deg,#011133 0,#0e2a3e 100%));-webkit-backdrop-filter:blur(5.1089px);backdrop-filter:blur(5.1089px);justify-content:flex-start}.experiment_variant .insta_play_cta .insta-info:not(.show){display:none}.experiment_variant .insta_play_cta .insta-info .cross-insta-icon{position:absolute;right:9px;top:9px;z-index:999}.experiment_variant .insta_play_cta .insta-info .gradient-bg{height:700px;width:100%;z-index:1;background:url(https://cdn-www.bluestacks.com/bs-images/gradient_bg.png) no-repeat;display:flex;flex-direction:column;background-size:contain;align-items:center;justify-content:center}.experiment_variant .insta_play_cta .insta-info .gradient-bg img{max-width:100%;height:180px}.experiment_variant .insta_play_cta .insta-info .heading{color:#fff;text-align:center;font-size:16px!important;font-weight:500;line-height:24px!important;margin-bottom:20px;padding-bottom:12px;position:relative;padding:0 20px}.experiment_variant .insta_play_cta .insta-info .explore-cloud-games,.experiment_variant .insta_play_cta .insta-info .try-top-games{border-radius:7.133px;border:1.136px solid rgba(255,255,255,.4);width:270px;padding:0 14px;color:var(--gray-0,#fff);font-size:16px;font-weight:600;text-align:center;margin:0 20px;height:50px}.experiment_variant .insta_play_cta .insta-info .try-top-games{background:var(--primary-cta-colors-color-states-product-default,linear-gradient(279deg,#5bce66 0,#9bce5b 100%));align-items:center;display:flex;line-height:normal;gap:10px}.experiment_variant .insta_play_cta .insta-info .try-top-games .so-btn{font-size:16px;font-weight:600;line-height:18px;justify-content:center;align-items:flex-start;height:100%}.experiment_variant .insta_play_cta .insta-info .try-top-games .so-btn .light-str{font-size:12px;font-weight:400;line-height:normal}.experiment_variant .insta_play_cta .insta-info .explore-cloud-games{background:var(--white-white-20,rgba(255,255,255,.2));margin-top:7px}.experiment_variant .insta_play_cta .insta-info p{flex:1}@media (max-width:767px){.experiment_variant .hero-upper-text .hero-text.no-deal-heading .hero-heading{text-align:center}.experiment_variant .hero-upper-text .hero-text.no-deal-heading .cta-elements{justify-content:center;text-align:center;align-items:center;width:100%}}.hero-section{background:linear-gradient(180deg,#0e2a3e 0,#011133 100%);height:658px}.hero-section .container{padding-top:145px;justify-content:space-between}.hero-section .container .left-section{width:auto;height:390px;position:relative}.hero-section .container .left-section .background-layer{position:relative}.hero-section .container .left-section .background-layer .content-layer{position:absolute;bottom:5px;left:50%;-webkit-transform:translate(-50%,0);transform:translate(-50%,0);gap:10px;align-items:center}@media (max-width:767px){.hero-section .container .left-section .background-layer .content-layer{bottom:0}}.hero-section .container .left-section .background-layer .content-layer .bg-title{color:#04224e;font-family:Roboto Slab;font-size:22.12px;font-weight:900;line-height:45.08px;letter-spacing:.9507042169570923px}.hero-section .container .left-section .swiper-wrapper-layer{position:absolute;left:11px;top:11px;width:calc(100% - 22px);height:calc(100% - 65px)}.hero-section .container .left-section .swiper-wrapper-layer .redirect-link{height:100%;width:100%;position:relative}.hero-section .container .left-section .swiper-wrapper-layer .redirect-link .swiper-slide-image{position:relative;height:100%;width:100%}.hero-section .container .left-section .swiper-wrapper-layer .redirect-link .swiper-slide-image::after{content:"";position:absolute;top:0;bottom:0;left:0;right:0;-webkit-transform:rotate(180deg);transform:rotate(180deg);background:linear-gradient(181.52deg,#181422 1.34%,rgba(24,20,34,0) 99.93%)}.hero-section .container .left-section .swiper-wrapper-layer .redirect-link .swiper-logo{position:absolute;top:10px;left:10px;max-height:100px;max-width:150px}.hero-section .container .left-section .swiper-wrapper-layer .download-cta-non-customized{background:linear-gradient(279.38deg,#5bce66 0,#9bce5b 100%);border:1px solid rgba(255,255,255,.2);border-radius:6px;padding:8px 12px;color:#fff;font-weight:600;margin-left:0}@media (max-width:767px){.hero-section .container .left-section .swiper-wrapper-layer .download-cta-non-customized{display:none}}.hero-section .container .left-section .swiper-wrapper-layer .download-cta-non-customized.download-started.retry-link{display:flex;flex-direction:row-reverse;gap:8px;margin-left:0;align-items:center}.hero-section .container .left-section .swiper-wrapper-layer .download-cta-non-customized.download-started.retry-link .button-copy .so-button{font-size:16px;align-items:flex-start}.hero-section .container .left-section .swiper-wrapper-layer .download-cta{position:absolute;bottom:40px;left:50%;-webkit-transform:translate(-50%,0);transform:translate(-50%,0)}.hero-section .container .left-section .swiper-pagination-bullet{background:rgba(217,217,217,.2);opacity:1}.hero-section .container .left-section .swiper-pagination-bullet-active{background:#d9d9d9}.hero-section .container .right-section{width:627px;flex-direction:column;justify-content:center;align-items:center;padding-bottom:55px}@media (max-width:767px){.hero-section .container .right-section{padding-bottom:0}}.hero-section .container .right-section .heading{font-family:Poppins;font-size:38px;font-weight:700;line-height:56px;text-align:center;max-width:500px}.hero-section .container .right-section .sub-heading{font-family:Roboto;font-size:24px;font-weight:400;line-height:32px;text-align:center;margin-top:14px;color:#fff}.download-wrapper-layer{display:none!important}.cta-layout.download-bs10{margin-top:20px}.cta-layout.download-bs10 .svg-icon::after{content:none}.cta-layout.download-bs10 .so-button{flex-direction:row-reverse;gap:6px}.cta-layout.download-bs10 .so-button .small-txt{font-size:inherit;line-height:inherit;font-weight:inherit}.cta-layout.download-bs10.download-started:not(.retry-link) .so-button{flex-direction:row-reverse;gap:6px}.cta-layout.download-bs10.download-started:not(.retry-link) .so-button .small-txt{font-size:inherit;line-height:inherit;font-weight:inherit}.download-bs5-wrapper.hyper-link{width:100%;justify-content:center}.download-bs5-wrapper.hyper-link a{font-family:Poppins;font-size:12px;font-weight:500;line-height:18px;color:rgba(255,255,255,.9)}.mac-os .download-bs5-wrapper.hyper-link{display:none}.mac-os .hero-section .cta-elements .download-wrapper-layer[data-mac-cta-present=show]{display:block!important;margin-top:40px}.mac-os .hero-section .cta-elements .download-wrapper-layer[data-mac-cta-present=show] a{margin-left:0}@media (min-width:768px) and (max-width:1024px){.hero-section{height:auto;padding-bottom:40px}.hero-section .container{flex-direction:column;align-items:center;padding-top:115px;gap:40px}.hero-section .container .cta-elements{margin-top:20px}}@media (max-width:767px){.experiment_variant .insta_play_cta .download-button-mobile{width:180px;z-index:9}.hero-section{height:auto;padding-bottom:40px}.hero-section .container{padding-top:80px;flex-direction:column}.hero-section .container .left-section{max-width:100%;height:auto}.hero-section .container .left-section .background-layer svg{width:100%;height:auto}.hero-section .container .left-section .background-layer .content-layer svg{width:20px;height:inherit}.hero-section .container .left-section .background-layer .content-layer .bg-title{font-size:12px;line-height:24px}.hero-section .container .left-section .swiper-wrapper-layer{left:7px;top:5px;width:calc(100% - 14px);height:calc(100% - 33px)}.hero-section .container .right-section{max-width:100%}.hero-section .container .right-section .heading{font-size:24px;line-height:36px;margin-top:24px}.hero-section .container .right-section .sub-heading{font-size:16px;line-height:32px;margin:10px 0}}@media (min-width:685px) and (max-width:767px){.hero-section .container .left-section .background-layer .content-layer{bottom:12px}.hero-section .container .left-section .swiper-wrapper-layer{left:10px;top:12px;width:calc(100% - 22px);height:calc(100% - 70px)}}@media (min-width:460px) and (max-width:550px){.hero-section .container .left-section .background-layer .content-layer{bottom:6px}.hero-section .container .left-section .swiper-wrapper-layer{left:7px;top:5px;width:calc(100% - 14px);height:calc(100% - 42px)}}@media (min-width:551px) and (max-width:684px){.hero-section .container .left-section .background-layer .content-layer{bottom:12px}.hero-section .container .left-section .swiper-wrapper-layer{left:7px;top:5px;width:calc(100% - 14px);height:calc(100% - 55px)}}body,html{position:relative;margin:0;padding:0}html{-moz-text-size-adjust:100%;-ms-text-size-adjust:100%;text-size-adjust:100%;font-size:14px;-webkit-text-size-adjust:100%;font-variant-ligatures:none;-webkit-font-variant-ligatures:none;text-rendering:optimizeLegibility;-moz-osx-font-smoothing:grayscale;font-smoothing:antialiased;-webkit-font-smoothing:antialiased;text-shadow:rgba(0,0,0,.01) 0 0 1px}@media (min-width:480px){html{font-size:16px}}*{box-sizing:border-box;outline-width:thin}ul{margin:0;padding:0;list-style:none}a{text-decoration:none}h3{font-weight:400}abbr{text-decoration:none}figure{padding:0;margin:0}.hidden{display:none;opacity:0}button{border:none}button[type=button]{cursor:pointer}.container{margin-left:auto;margin-right:auto;padding:0 10px;max-width:1260px}.flex{display:flex}.flex.end{justify-content:flex-end}.flex.start{justify-content:flex-start}.flex.between{justify-content:space-between}.flex.center{justify-content:center}.flex.wrap{flex-wrap:wrap}.flex.y{align-items:center}.flex.top{align-items:flex-start}.flex.bottom{align-items:flex-end}[lang=vi],[lang=vi] body{font-family:Roboto,sans-serif}body:not(.mac-os) .hide-on-windows{display:none!important}body{transition:top .2s cubic-bezier(.5,0,.5,1)}.annoucement-bar{position:absolute;left:0;top:0;-webkit-transform:translate3d(0,-100%,0);transform:translate3d(0,-100%,0);z-index:999;padding:6px 0;background-color:#009ddf;width:100%}.annoucement-bar .wrp{margin-left:auto;margin-right:auto;max-width:95vw;align-items:center}.annoucement-bar .flex{display:flex}.annoucement-bar .flex.between{justify-content:space-between}.annoucement-bar .flex.center{justify-content:center}.annoucement-bar .flex.y{align-items:center}.annoucement-bar div.content{width:calc(100% - 50px)}.annoucement-bar p.content{flex:1 0 0;margin:0;color:#fff}.annoucement-bar figure{padding:0;margin:0 20px 0 0;font-size:0}.annoucement-bar .download-btn{display:inline-block;margin-left:20px;border-radius:5px;background:#283566;padding:10px 25px;color:#fff;font-size:20px;align-self:center;transition:-webkit-transform .1s cubic-bezier(.5,0,.5,1);transition:transform .1s cubic-bezier(.5,0,.5,1);transition:transform .1s cubic-bezier(.5,0,.5,1),-webkit-transform .1s cubic-bezier(.5,0,.5,1)}.annoucement-bar .download-btn:hover{background:#1a2241;-webkit-transform:translate3d(0,-1px,0);transform:translate3d(0,-1px,0)}.annoucement-bar .download-btn:active{-webkit-transform:translate3d(0,1px,0);transform:translate3d(0,1px,0)}.annoucement-bar #close-notification{position:relative;margin-left:3%;display:block;height:40px;width:40px;border:2px solid #fff;border-radius:6px;background:0 0;text-indent:-999em;cursor:pointer}.annoucement-bar #close-notification:after,.annoucement-bar #close-notification:before{position:absolute;left:50%;top:50%;margin-top:-10px;margin-left:-1px;height:20px;width:2px;content:'';background:#fff;border-radius:2px;-webkit-transform-origin:center;transform-origin:center}.annoucement-bar #close-notification:before{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.annoucement-bar #close-notification:after{-webkit-transform:rotate(45deg);transform:rotate(45deg)}.annoucement-bar #close-notification:hover{color:#3c4455;background:currentColor;border-color:currentColor}@media only screen and (max-width:1030px){.annoucement-bar .wrp{width:98vw}}@media only screen and (max-width:980px){.annoucement-bar figure{margin:0 10px 0 0}.annoucement-bar div.content{width:calc(100% - 50px)}.annoucement-bar div.content.flex.y{align-items:flex-start}.annoucement-bar div.content.flex .flex{display:block}.annoucement-bar .download-btn{margin:10px 0 0}}@media (max-width:480px){.annoucement-bar #close-notification{position:absolute;top:20px;right:20px}.annoucement-bar>.flex>.content{max-width:calc(100% - 80px)}.annoucement-bar .flex{align-items:flex-start}.annoucement-bar .download-btn{font-size:13px}}.string-if-on-pc{display:none;font-style:normal}@media (min-width:1025px){.string-if-on-pc{display:inline}}.bs-logo{display:inline-block}@media (max-width:640px){.bs-logo{display:block;width:14vw;overflow:hidden}.bs-logo img{width:40vw;height:auto}}@media (max-width:640px){[lang=ar] .bs-logo img{position:relative;left:186%}}.language-selector .language-selector-btn>svg{height:8px;width:14px;fill:#fff;transition:-webkit-transform .3s cubic-bezier(.77,0,.3,.99);transition:transform .3s cubic-bezier(.77,0,.3,.99);transition:transform .3s cubic-bezier(.77,0,.3,.99),-webkit-transform .3s cubic-bezier(.77,0,.3,.99)}.language-selector-btn{transition:.7s background cubic-bezier(0,.5,.5,1)}.language-selector{position:relative;right:-10px;font-size:14px;min-width:115px;transition:.5s opacity cubic-bezier(0,.5,.5,1),.3s cubic-bezier(.5,0,.5,1) right}.language-selector:not(.ready){opacity:0}.language-selector .language-selector-btn{position:relative;z-index:2;display:flex;justify-content:space-between;align-items:center;width:100%;padding-top:6.4px;padding-bottom:6.4px;padding-left:9.6px;padding-right:9.6px;background:0 0;border:2px solid #fff;border-radius:5px;cursor:pointer}@media (min-width:768px){.language-selector .language-selector-btn{padding-top:8px;padding-bottom:8px}}@media (min-width:768px){.language-selector .language-selector-btn{padding-left:12px;padding-right:12px}}.language-selector .language-selector-btn svg{position:absolute;right:10px;top:50%;margin-top:-4px;display:inline-block}.language-selector .selected-lang{position:relative;margin-right:15px;padding:3px 0;display:inline-block;font-size:15px;font-size:.9375rem;font-family:Lato,sans-serif;color:#fff;text-align:left;font-weight:900}.language-selector ul{position:absolute;z-index:9;top:calc(100% - 3px);padding-left:0;padding-right:0;padding-top:9.6px;padding-bottom:9.6px;width:100%;opacity:0;-webkit-transform-origin:top;transform-origin:top;-webkit-transform:scale3d(1,0,1);transform:scale3d(1,0,1);transition:.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.5,0,.5,1);transition:.3s transform cubic-bezier(.5,0,.5,1),.25s opacity cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.5,0,.5,1),.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.5,0,.5,1);border-radius:0 0 4px 4px;border:2px solid #fff;border-top:0;background:rgba(28,29,35,.7)}@media (min-width:768px){.language-selector ul{padding-top:12px;padding-bottom:12px}}.language-selector ul li{display:block;opacity:0;transition:.1s opacity cubic-bezier(.77,0,.3,.99)}.language-selector ul a{display:block;padding:5px 12px;color:#fff}.language-selector ul a:hover{background:rgba(28,29,35,.7);opacity:.5}.language-selector ul:hover{transition:.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.77,0,.3,.99),.25s opacity cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.77,0,.3,.99),.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.77,0,.3,.99);-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1);opacity:1}@media (max-width:1024px){.language-selector{right:60px}}@media (max-width:1024px){[lang=ar] .language-selector{right:-60px}}[lang=ar] .language-selector .language-selector-btn svg{right:initial;left:10px}.language-selector.active .language-selector-btn,.language-selector:hover .language-selector-btn{background:rgba(28,29,35,.7);transition:.1s background cubic-bezier(.77,0,.3,.99)}.language-selector.active ul,.language-selector:hover ul{opacity:1;-webkit-transform:scale3d(1,1,1);transform:scale3d(1,1,1);transition:.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.77,0,.3,.99),.25s opacity cubic-bezier(.77,0,.3,.99);transition:.3s transform cubic-bezier(.77,0,.3,.99),.25s opacity cubic-bezier(.77,0,.3,.99),.3s -webkit-transform cubic-bezier(.77,0,.3,.99)}.language-selector.active svg,.language-selector:hover svg{-webkit-transform:rotate(180deg);transform:rotate(180deg)}.language-selector ul:hover li,.language-selector.active ul li,.language-selector:hover ul li{opacity:1;transition:.1s .35s opacity cubic-bezier(.77,0,.3,.99);-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}.container-autocomplete{margin:auto;max-width:550px}.container-autocomplete form{margin:auto;border-radius:5px;background:#fff;overflow:hidden}@media (max-width:768px){.container-autocomplete{display:none}}.container-autocomplete input{position:relative;padding:10px 15px;font-size:18px;font-size:1.125rem;font-weight:300;font-family:Lato,sans-serif;font-style:italic;background:#fff;vertical-align:middle;white-space:nowrap;border:none;border-radius:4px;border:1px solid transparent;-webkit-appearance:textfield;outline:0}.container-autocomplete input:focus{border-color:rgba(117,207,229,.3)}@media (min-width:769px){.container-autocomplete input{width:35vw;min-width:250px;height:46px;left:4px}}.container-autocomplete button{background:0 0;border:none;fill:#a9adb8;outline:0;padding:13px}.container-autocomplete button svg{pointer-events:none}@media (min-width:540px){#mobile-search-opner{display:none}}.autocomplete-suggestions{position:absolute;z-index:9999;display:none;max-height:250px;border-radius:5px;background:#fff;overflow:hidden;overflow-y:auto}.autocomplete-suggestions .search-wrapper .heading{display:flex;justify-content:space-between;margin:14px 0 5px 0;padding:0 14px}.autocomplete-suggestions .search-wrapper .heading .heading-title{font-weight:600;font-size:16px;line-height:24px;color:#28304c;margin:0}.autocomplete-suggestions .search-wrapper .heading .view-all{font-weight:400;font-size:14px;line-height:24px;color:#0082e5}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion{padding:10px 14px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i{display:flex;align-items:center}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure{height:40px;width:40px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure .object-fit{width:100%;display:inline-block;height:100%;-o-object-fit:cover;object-fit:cover}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{margin-left:18px;font-size:18px;line-height:28px;color:#394566;width:calc(100% - 58px);-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:1;overflow:hidden;text-overflow:ellipsis}.autocomplete-suggestion{padding:10px;color:#3d4664;cursor:pointer}.autocomplete-suggestion:hover{background:#b0cfe5;color:#fff}.autocomplete-suggestion:not(:last-child){border-bottom:1px solid rgba(169,173,184,.3)}.autocomplete-suggestion b{font-weight:400;color:#1f8dd6}input.placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input:-moz-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input::-moz-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input::-webkit-input-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input:focus.placeholder{opacity:0;-webkit-transform:translateX(12px);transform:translateX(12px)}input:focus:-moz-placeholder{opacity:0;transform:translateX(12px)}input:focus::-moz-placeholder{opacity:0;transform:translateX(12px)}input:focus::-webkit-input-placeholder{opacity:0;-webkit-transform:translateX(12px);transform:translateX(12px)}[lang=ar] .autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{margin-left:0;margin-right:18px}@media (max-width:767px){.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion{padding:6px 14px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure{height:32px;width:32px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{font-size:14px;line-height:150%}}.main-header{width:100%;z-index:999;padding-top:9.6px;padding-bottom:9.6px}@media (min-width:768px){.main-header{padding-top:12px;padding-bottom:12px}}.main-header:not(.absolute){position:relative}.main-header.absolute{position:absolute;width:100%}.main-header.blue{background:rgba(20,26,47,.8)}.main-header.blue-opaque{background:#222d48}.main-header.gray{background:rgba(0,0,0,.3)}.menu-container{position:relative;margin:0 auto;width:calc(100% - 48px);align-items:center}@media (max-width:540px){.menu-container{width:calc(100% - 30px)}}.language-selector:not(.ready),.menu-opner{opacity:0}.menu-opner{position:absolute}.container-autocomplete+.ko-txt{display:none}.hide{display:none}.wrapper-links{display:flex;align-items:center;height:30px;margin-right:7%}.wrapper-links img{max-width:100%;max-height:100%}.wrapper-links .ko-lnk{color:#fff;display:flex;align-items:center;order:2}.wrapper-links .ko-lnk .img-section{height:30px;width:30px;margin-right:10px}.wrapper-links .samsung-store-link .img-section{height:30px;width:100%;margin-right:20px}.wrapper-links .samsung-store-link .img-section .pc-visible{display:block}.wrapper-links .samsung-store-link .img-section .mob-visible{display:none}@media (max-width:1285px){.wrapper-links .ko-lnk p{display:none}}@media (max-width:1150px){.wrapper-links{margin:0}}@media (max-width:1024px){[lang=ko-KR] .upcoming{margin-right:0;min-width:125px}.wrapper-links .samsung-store-link .img-section .pc-visible{display:none}.wrapper-links .samsung-store-link .img-section .mob-visible{display:block}}@media (max-width:767px){.wrapper-links{margin-right:45px}.wrapper-links .samsung-store-link .img-section{width:30px;margin-right:10px}}.pop-up-open{overflow:hidden}.pop-up{position:fixed;top:0;left:0;width:100%;height:100%;z-index:99999;background:rgba(35,51,76,.9);opacity:0}.pop-up:not(.active){display:none}.pop-up.active{-webkit-animation:modal-showing-up .2s cubic-bezier(.5,0,.5,1) forwards;animation:modal-showing-up .2s cubic-bezier(.5,0,.5,1) forwards}.pop-up .close-modal{position:absolute;top:2vw;right:2vw;border:0;padding:20px;background:0 0}.pop-up .close-modal:after,.pop-up .close-modal:before{position:absolute;left:50%;top:50%;content:'';background-color:#fff;-webkit-transform:translate(-50%,-50%) rotate(45deg);transform:translate(-50%,-50%) rotate(45deg);-webkit-transform-origin:center center;transform-origin:center center}.pop-up .close-modal:before{height:2px;width:50%}.pop-up .close-modal:after{height:50%;width:2px}.show-content .pop-up-content{opacity:1;-webkit-transform:translate3d(-50%,0,0);transform:translate3d(-50%,0,0)}.pop-up-content{position:absolute;top:90px;left:50%;-webkit-transform:translate3d(-50%,10px,0);transform:translate3d(-50%,10px,0);width:90%;max-width:700px;padding:20px 30px 40px;text-align:center;border-radius:2px;box-sizing:border-box;background:#e9f0f7;opacity:0;transition:opacity .15s cubic-bezier(0,.5,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:transform .2s cubic-bezier(.5,0,.5,1),opacity .15s cubic-bezier(0,.5,.5,1);transition:transform .2s cubic-bezier(.5,0,.5,1),opacity .15s cubic-bezier(0,.5,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);box-shadow:0 14px 28px rgba(19,28,41,.8),0 10px 10px rgba(19,28,41,.4)}.pop-up-content h5{color:#32406f;font-size:33px;margin:5px auto 20px}.pop-up-content p{color:gray;font-size:20px;margin:0 auto 8px;font-weight:300}.pop-up-content .small{font-size:14px}@-webkit-keyframes modal-showing-up{from{opacity:0}to{opacity:1}}@keyframes modal-showing-up{from{opacity:0}to{opacity:1}}#current-language-popup{position:fixed;right:-222px;top:15vh;width:220px;z-index:999;font-family:Lato,sans-serif;-webkit-animation:show-current-language-modal .5s cubic-bezier(0,.5,.5,1) forwards;animation:show-current-language-modal .5s cubic-bezier(0,.5,.5,1) forwards}#current-language-popup #language-popup-closer{position:absolute;opacity:0;height:0}#current-language-popup #language-popup-closer:checked+.l-p-content{opacity:0;pointer-events:none;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0);transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .5s cubic-bezier(.5,0,0,1);transition:transform .5s cubic-bezier(.5,0,0,1),opacity .2s cubic-bezier(.5,0,.5,1);transition:transform .5s cubic-bezier(.5,0,0,1),opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .5s cubic-bezier(.5,0,0,1)}#current-language-popup .l-p-content{position:relative;width:220px;padding:60px 20px 30px;box-sizing:border-box;background:rgba(29,45,83,.7);text-align:center;-webkit-transform:translate3d(-222px,0,0);transform:translate3d(-222px,0,0)}#current-language-popup .l-p-phrase{font-weight:700;color:#fff}#current-language-popup .l-p-flag img{width:80px}#current-language-popup .lp-close{position:absolute;right:20px;top:20px;width:35px;height:35px;cursor:pointer}#current-language-popup .lp-close::after,#current-language-popup .lp-close::before{content:'';position:absolute;top:50%;left:0;height:1px;width:100%;background:#fff}#current-language-popup .lp-close:before{-webkit-transform:rotate(45deg);transform:rotate(45deg)}#current-language-popup .lp-close::after{-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}#current-language-popup .lp-close:hover::after,#current-language-popup .lp-close:hover::before{box-shadow:0 0 0 1px #fff}#current-language-popup #l-p-button{position:relative;display:inline-block;margin:20px;padding:8px 25px;font-size:22px;color:#fff;text-decoration:none;background-color:#f1c40f;border-radius:5px;box-shadow:0 5px 0 0 #d8ab00;font-weight:700}#current-language-popup #l-p-button:active{-webkit-transform:translate(0,5px);transform:translate(0,5px);box-shadow:0 1px 0 0 #d8ab00}@-webkit-keyframes show-current-language-modal{0%{opacity:0;-webkit-transform:translate3d(110%,0,0);transform:translate3d(110%,0,0)}100%{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}@keyframes show-current-language-modal{0%{opacity:0;-webkit-transform:translate3d(110%,0,0);transform:translate3d(110%,0,0)}100%{opacity:1;-webkit-transform:translate3d(0,0,0);transform:translate3d(0,0,0)}}.menu-opner{position:absolute;right:16px;width:20px;height:18px;border:none;-webkit-perspective:400px;perspective:400px;background:0 0;cursor:pointer;opacity:1}.menu-opner .main-tick,.menu-opner:after,.menu-opner:before{position:absolute;left:0;width:100%;height:2px;border-radius:2px;background:#ebeff2;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.menu-opner:after,.menu-opner:before{content:'';-webkit-transform-origin:center right;transform-origin:center right}.menu-opner:before{top:0}.menu-opner:after{bottom:0}.menu-opner .main-tick{top:50%;-webkit-transform-origin:50% 50%;transform-origin:50% 50%;-webkit-transform:translate3d(0,-50%,0);transform:translate3d(0,-50%,0)}.menu-opner.menu-opened .main-tick{-webkit-transform:translate3d(0,-50%,0) scale3d(.1,1,1);transform:translate3d(0,-50%,0) scale3d(.1,1,1)}.menu-opner.menu-opened:before{-webkit-transform:translate3d(-6px,-3px,0) rotate3d(0,0,1,-45deg);transform:translate3d(-6px,-3px,0) rotate3d(0,0,1,-45deg)}.menu-opner.menu-opened:after{-webkit-transform:translate3d(-6px,4px,0) rotate3d(0,0,1,45deg);transform:translate3d(-6px,4px,0) rotate3d(0,0,1,45deg)}.menu-opened .menu-opner .main-tick{-webkit-transform:translate3d(0,-50%,0) scale3d(.1,1,1);transform:translate3d(0,-50%,0) scale3d(.1,1,1)}.menu-opened .menu-opner:before{-webkit-transform:translate3d(-6px,-3px,0) rotate3d(0,0,1,-45deg);transform:translate3d(-6px,-3px,0) rotate3d(0,0,1,-45deg)}.menu-opened .menu-opner:after{-webkit-transform:translate3d(-6px,4px,0) rotate3d(0,0,1,45deg);transform:translate3d(-6px,4px,0) rotate3d(0,0,1,45deg)}[lang=ar] .menu-opner{right:initial;left:0}.relative{position:relative}.header-wrapper{position:absolute;z-index:0;width:100%;top:0;opacity:0;background:linear-gradient(0deg,rgba(0,0,0,0),rgba(0,0,0,.5));height:88px}@media (min-width:1320px){.header-wrapper .container{max-width:1300px}}.header-wrapper.header-fixed{background-color:#090e3c}.header-wrapper .container{display:flex;align-items:center;justify-content:space-between;height:100%;position:relative}.header-wrapper .container .left-section{display:flex;align-items:center;justify-content:space-between;height:100%}.header-wrapper .container .left-section .logo-block{margin:0 50px 0 0;display:flex;align-items:center;justify-content:center;max-width:126px;margin-right:28px}.header-wrapper .container .left-section .logo-block .logo-img{max-width:80px;height:48px;width:auto;margin-right:12px}.header-wrapper .container .left-section .logo-block .logo-img-text{width:auto;height:31px}.header-wrapper .container .left-section .menu-block{height:100%;display:flex}.header-wrapper .container .left-section .wrapper-links{position:relative;top:0;margin:0 auto;padding:0;height:auto;left:0;right:0;-webkit-transform:scale(.9);transform:scale(.9)}.header-wrapper .container .left-section .wrapper-links img{max-width:100%;max-height:100%}.header-wrapper .container .left-section .wrapper-links .ko-lnk{color:#fff;display:flex;align-items:center;order:unset}.header-wrapper .container .left-section .wrapper-links .ko-lnk .img-section{height:30px;width:30px;margin-right:20px}.header-wrapper .container .left-section .wrapper-links .ko-lnk p{margin:0}.header-wrapper .container .left-section .wrapper-links .img-section{margin-left:20px;margin-right:0}.header-wrapper .container .left-section .wrapper-links .samsung-store-link{margin:0}.header-wrapper .container .left-section .wrapper-links .samsung-store-link .img-section{height:auto;width:100px}.header-wrapper .container .left-section .menu-list{display:flex;align-items:center;justify-content:center}.header-wrapper .container .left-section .menu-list>.menu-item-has-children:first-child .sub-menu,.header-wrapper .container .left-section .menu-list>.menu-item-has-children:nth-child(2) .sub-menu{height:400px;overflow-y:scroll}.header-wrapper .container .left-section .menu-list>.menu-item-has-children:first-child .sub-menu::-webkit-scrollbar,.header-wrapper .container .left-section .menu-list>.menu-item-has-children:nth-child(2) .sub-menu::-webkit-scrollbar{width:6px}.header-wrapper .container .left-section .menu-list>.menu-item-has-children:first-child .sub-menu::-webkit-scrollbar-track,.header-wrapper .container .left-section .menu-list>.menu-item-has-children:nth-child(2) .sub-menu::-webkit-scrollbar-track{background:#181b34}.header-wrapper .container .left-section .menu-list>.menu-item-has-children:first-child .sub-menu::-webkit-scrollbar-thumb,.header-wrapper .container .left-section .menu-list>.menu-item-has-children:nth-child(2) .sub-menu::-webkit-scrollbar-thumb{background:#f1f1f4;border-radius:4px}.header-wrapper .container .left-section .menu-list>.menu-item-has-children>a::after{right:0;content:'';border:solid #fff;border-width:0 2px 2px 0;display:inline-block;padding:4px;-webkit-transform:rotate(45deg) translate(0,-50%);transform:rotate(45deg) translate(0,-50%);position:absolute;top:calc(50% - 4px);transition:.3s ease-in-out}.header-wrapper .container .left-section .menu-list>.current-menu-item,.header-wrapper .container .left-section .menu-list>.current-page-ancestor{border-bottom:3px solid #fff}.header-wrapper .container .left-section .menu-list>.current-menu-item.border-none,.header-wrapper .container .left-section .menu-list>.current-page-ancestor.border-none{border-bottom:none}.header-wrapper .container .left-section .menu-list>li:hover{border-bottom:3px solid #fff}.header-wrapper .container .left-section .menu-list>li:hover>a::after{-webkit-transform:rotate(225deg);transform:rotate(225deg)}.header-wrapper .container .left-section .menu-list>li{border-bottom:3px solid transparent;height:80px;display:flex;align-items:center}.header-wrapper .container .left-section .menu-list>li>a{padding:0}.header-wrapper .container .left-section .menu-list>.menu-item-has-children a{padding:0 20px 0 0;position:relative}.header-wrapper .container .left-section .menu-list>li:hover .sub-menu{display:flex!important;position:absolute;top:80px;width:274px;flex-direction:column;border:1px solid #4a4a4a;background-color:rgba(0,0,0,.9);padding:0;z-index:1;left:0}.header-wrapper .container .left-section .menu-list>li:hover .sub-menu>li:hover{background-color:#222}.header-wrapper .container .left-section .menu-list>li:hover .sub-menu a{color:rgba(255,255,255,.8);font-size:16px;line-height:40px;text-transform:capitalize;transition:.2s ease-in-out;display:block}.header-wrapper .container .left-section .menu-list>li:hover .sub-menu a:hover{color:#fff}.header-wrapper .container .left-section .menu-list .sub-menu:hover{display:flex!important}.header-wrapper .container .left-section .menu-list li{position:relative;padding:0 15px 0 15px}.header-wrapper .container .left-section .menu-list li .sub-menu{display:none}.header-wrapper .container .left-section .menu-list li a{font-size:14px;letter-spacing:.88px;line-height:28px;color:rgba(255,255,255,.8);transition:.2s ease-in-out;padding:0;text-transform:uppercase;height:100%;display:flex;align-items:center}.header-wrapper .container .left-section .menu-list li a:hover{color:#fff}.header-wrapper .container .left-section .menu-list li.bs-store>a{gap:5px}.header-wrapper .container .left-section .menu-list li.bs-store>a .store-red-icon{display:inline-block;width:auto;white-space:nowrap;height:auto;background-image:linear-gradient(264deg,#ff5e00 -2.73%,#d60004 93.92%);border-radius:3px 4px 4px 3px;border-left:1px solid #d60004;margin-left:6px;position:relative;color:#fff;text-align:center;-webkit-font-feature-settings:"liga" off,"clig" off;font-feature-settings:"liga" off,"clig" off;font-size:10.575px;font-style:normal;font-weight:800;line-height:19.886px;letter-spacing:1px;padding:0 6px 0 7px}.header-wrapper .container .left-section .menu-list li.bs-store>a .store-red-icon:before{content:"";position:absolute;display:block;left:-8.3px;width:0;height:0;border-top:11px solid transparent;border-bottom:10px solid transparent;border-right:9px solid #d60004}.header-wrapper .container .left-section .menu-list li.bs-store>a .store-yellow-icon{display:inline-block;width:auto;white-space:nowrap;height:auto;background-image:linear-gradient(264deg,#df9300 -2.73%,#ffb800 93.92%);border-radius:3px 4px 4px 3px;border-left:1px solid #e89e00;margin-left:6px;position:relative;color:#fff;text-align:center;-webkit-font-feature-settings:"liga" off,"clig" off;font-feature-settings:"liga" off,"clig" off;font-size:10.575px;font-style:normal;font-weight:800;line-height:20.886px;letter-spacing:1px;padding:0 6px 0 7px}.header-wrapper .container .left-section .menu-list li.bs-store>a .store-yellow-icon:before{content:"";position:absolute;display:block;left:-8.3px;width:0;height:0;border-top:11px solid transparent;border-bottom:10px solid transparent;border-right:9px solid #ffb800}.header-wrapper .container .left-section .menu-list li.bs-store>a .nav-icon{display:flex;align-items:center}.header-wrapper .container .left-section .menu-list li.bs-store>a .nav-icon .offer-icon{width:26px;height:auto}.header-wrapper .container .left-section .menu-list#menu-homepage-v2-header-menu-arabic-homepage-v2-header-menu .bs-store>a span{margin-right:13px;padding:0 7px 0 15px;border-radius:3px 3px 4px 4px;border-right:1px solid #e89e00}.header-wrapper .container .left-section .menu-list#menu-homepage-v2-header-menu-arabic-homepage-v2-header-menu .bs-store>a span::before{border-left:9px solid #e89e00;border-right:none;right:-9px;left:auto}.header-wrapper .container .right-section{display:flex;align-items:center;justify-content:center;position:relative;height:100%}.header-wrapper .container .right-section .search-wrapper{margin-right:10px;display:flex}.header-wrapper .container .right-section .search-wrapper .icon-search{width:18px;height:18px;display:inline-block;cursor:pointer;margin-left:6px}.header-wrapper .container .right-section .search-wrapper+i{width:30px!important}.header-wrapper .container .right-section .container-autocomplete{display:inline-block;max-width:508px;height:38px;width:0;transition:.3s ease-in-out;position:absolute;right:calc(100% - 38px);z-index:10}.header-wrapper .container .right-section .container-autocomplete form{height:38px;border-radius:0;position:absolute;right:0;width:0;transition:.3s ease-in-out}.header-wrapper .container .right-section .container-autocomplete form input{font-size:16px;color:#333;font-style:normal;font-family:Roboto,sans-serif;min-width:100%;height:100%;left:0;padding-right:40px}.header-wrapper .container .right-section .container-autocomplete form input[type=search]::-webkit-search-cancel-button{display:none}.header-wrapper .container .right-section .container-autocomplete form button{fill:#333;position:absolute;right:0}.header-wrapper .container .right-section .container-autocomplete form button:hover{-webkit-transform:none;transform:none}.header-wrapper .container .right-section .container-autocomplete form button .search-icon{height:18px;width:18px}.header-wrapper .container .right-section .show{width:100%;height:100%;padding:25px 0;max-width:none}.header-wrapper .container .right-section .show form{width:650px}.header-wrapper .container .right-section .language-selector{right:0;margin-right:10px;height:100%;padding:0;display:flex;align-items:center}.header-wrapper .container .right-section .language-selector button{color:rgba(255,255,255,.8);font-size:14px;letter-spacing:2px;line-height:16px;display:flex;align-items:center;border:none;font-weight:400;width:auto;text-transform:uppercase;position:relative;transition:.3s ease-in-out;padding:3px 30px 0 15px;border-bottom:3px solid transparent;height:100%}.header-wrapper .container .right-section .language-selector button .selected-lang{margin-right:0;padding:0;font-size:14px;color:rgba(255,255,255,.8);font-weight:400;transition:.3s ease-in-out;font-family:Roboto,sans-serif}.header-wrapper .container .right-section .language-selector button .selected-lang:hover{color:#fff}.header-wrapper .container .right-section .language-selector button::after{right:12px;content:'';border:solid #fff;border-width:0 2px 2px 0;display:inline-block;padding:4px;-webkit-transform:rotate(45deg) translateY(-50%);transform:rotate(45deg) translateY(-50%);position:absolute;top:calc(50% - 4px);transition:.3s ease-in-out}.header-wrapper .container .right-section .language-selector button svg{display:none}.header-wrapper .container .right-section .language-selector button i{border:solid #fff;border-width:0 2px 2px 0;padding:4px;-webkit-transform:rotate(45deg);transform:rotate(45deg);margin-bottom:5px;margin-left:5px}.header-wrapper .container .right-section .language-selector button:hover{color:#fff}.header-wrapper .container .right-section .language-selector ul{left:0;border:1px solid #4a4a4a;border-top:3px solid #fff;background-color:rgba(0,0,0,.7);margin-top:0;padding:0;border-radius:0;top:100%;width:100%}.header-wrapper .container .right-section .language-selector ul li:hover{background:#222}.header-wrapper .container .right-section .language-selector ul li:hover a{color:#fff}.header-wrapper .container .right-section .language-selector ul a{padding:0 12px;line-height:28px}.header-wrapper .container .right-section .language-selector ul a:hover{color:#fff;background:0 0;opacity:1}.header-wrapper .container .right-section .language-selector:hover button{background-color:transparent;border-radius:0}.header-wrapper .container .right-section .language-selector:hover button::after{-webkit-transform:rotate(225deg);transform:rotate(225deg)}.header-wrapper .container .right-section .language-selector:hover button i{-webkit-transform:rotate(225deg);transform:rotate(225deg);margin:5px 0 0}.header-wrapper .container .right-section .download-buttons{display:none;position:relative;top:unset;left:unset;-webkit-transform:none;transform:none;font-size:12px;color:#fff;line-height:18px;font-weight:900;letter-spacing:.6px;text-align:center;background-color:transparent;padding:0;border:none;width:auto;min-width:210px;height:40px}.header-wrapper .container .right-section .download-buttons.pc-visible{min-width:210px}.header-wrapper .container .right-section .download-buttons .browser-cta{display:none}.header-wrapper .container .right-section .download-buttons .download-button{padding:6px 10px;background:linear-gradient(#159aff 0,#15b9ff 100%)!important;border:1px solid rgba(255,255,255,.2)!important;border-radius:6px!important;height:100%;box-shadow:none;font-size:12px;width:100%;max-width:235px}.header-wrapper .container .right-section .download-buttons .download-button svg{display:none}.header-wrapper .container .right-section .download-buttons .download-button .button-copy{width:100%}.header-wrapper .container .right-section .download-buttons .download-button .button-copy .so-button{font-weight:600;font-size:16px;line-height:24px;padding:0;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;display:block}.header-wrapper .container .right-section .download-buttons .download-button .download-started{justify-content:end;cursor:pointer}.header-wrapper .container .right-section .download-buttons .download-button .download-started::after,.header-wrapper .container .right-section .download-buttons .download-button .download-started::before{content:none}.header-wrapper .container .right-section .download-buttons .download-button .download-started .try-again{display:none}.header-wrapper .container .right-section .download-buttons .download-button:hover{-webkit-filter:brightness(110%);filter:brightness(110%)}.header-wrapper .container .right-section .download-buttons .download-started.retry-link{justify-content:center}.header-wrapper .container .right-section .download-buttons .download-started.retry-link .button-copy{width:90%}.header-wrapper .container .right-section .download-buttons .download-started.retry-link .button-copy .so-button{width:100%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;display:block}.header-wrapper .container .right-section .download-buttons .download-started.retry-link .button-copy .so-button:not([data-test]){padding-right:51px}@media (min-width:768px){[lang=es-ES] .header-wrapper .container .left-section .menu-list li a,[lang=es-ES] .header-wrapper .container .right-section .language-selector button .selected-lang{font-size:13px}[lang=vi] .header-wrapper .container .left-section .menu-list li{padding:0 12px 0 12px}}@media (min-width:1024px) and (max-width:1260px){[lang=vi] .header-wrapper .container .left-section .menu-list li{padding:0 11px 0 11px}[lang=vi] .header-wrapper .container .left-section .menu-list li a{font-size:13px;letter-spacing:.2px}}@media (min-width:1025px) and (max-width:1300px){[lang=fr-FR] .header-wrapper .container .left-section .menu-list li{padding:0 10px 0 10px}[lang=fr-FR] .header-wrapper .container .left-section .menu-list li a{letter-spacing:.2px}}@media (min-width:1024px) and (max-width:1170px){[lang=vi] .header-wrapper .container .left-section .menu-list li{padding:0 8px 0 8px}[lang=vi] .header-wrapper .container .left-section .menu-list li a{font-size:12px}[lang=vi] .header-wrapper .container .right-section .language-selector{margin-right:0}[lang=vi] .header-wrapper .container .right-section .language-selector button{font-size:12px;letter-spacing:.2px;padding:3px 24px 0 10px}[lang=vi] .header-wrapper .container .right-section .language-selector button .selected-lang{font-size:12px}[lang=vi] .header-wrapper .container .right-section .download-buttons.pc-visible{min-width:180px}[lang=vi] .header-wrapper .container .left-section .logo-block .logo-img{height:32px;margin-right:5px}[lang=vi] .header-wrapper .container .left-section .logo-block .logo-img-text{height:28px}}.footer-logo-lnk{position:relative}.sticky-header{background:#1f2640;position:fixed;transition:.3s ease-in-out;height:64px}.sticky-header .container .left-section .menu-list>.current-menu-item,.sticky-header .container .left-section .menu-list>.current-page-ancestor,.sticky-header .container .left-section .menu-list>li:hover{border-bottom:3px solid #37a8ff}.sticky-header .container .right-section .download-buttons.pc-visible{display:inline-block!important}.sticky-header .container .right-section .language-selector ul{border-top:3px solid #37a8ff!important}.sticky-header .container .right-section .show{padding:12px 0;max-width:none}.animate-header{z-index:999;-webkit-animation:fadeInDown 1s;animation:fadeInDown 1s;-webkit-animation-fill-mode:both;animation-fill-mode:both;-webkit-animation-delay:.1s;animation-delay:.1s}.animate-header .top-strip{z-index:99}.autocomplete-suggestions{position:fixed!important}.autocomplete-suggestions .autocomplete-suggestion.selected{background:#b0cfe5}@-webkit-keyframes fadeInDown{0%{opacity:0;-webkit-transform:translateY(-135px);transform:translateY(-135px)}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}@keyframes fadeInDown{0%{opacity:0;-webkit-transform:translateY(-135px);transform:translateY(-135px)}100%{opacity:1;-webkit-transform:translateY(0);transform:translateY(0)}}.autocomplete-suggestions{border-radius:0}.autocomplete-suggestions .autocomplete-suggestion{color:#556789;font-size:16px}.autocomplete-suggestions .autocomplete-suggestion b{color:#0091ff}.autocomplete-suggestions .autocomplete-suggestion:not(:last-child){border-bottom:1px solid #eee}.autocomplete-suggestions .autocomplete-suggestion:hover{background:#b0cfe5}[lang=ko-KR] .header-wrapper .container .left-section .logo-block{margin:0 20px 0 0}[lang=ko-KR] .header-wrapper .container .left-section .menu-list>.naver-com-link a{padding-left:35px!important;position:relative}[lang=ko-KR] .header-wrapper .container .left-section .menu-list>.naver-com-link a::before{content:'';height:25px;width:30px;background:url(https://cdn-www.bluestacks.com/bs-images/Naver-Community-Logo.png) no-repeat;background-size:contain;position:absolute;left:0}[lang=ko-KR] .header-wrapper .container .left-section .samsung-store-lnk{margin:0;display:none}[lang=ko-KR] .header-wrapper .container .left-section .samsung-store-lnk a{width:100px;height:100%;float:left;padding:0!important;position:relative;font-size:0!important}[lang=ko-KR] .header-wrapper .container .left-section .samsung-store-lnk a::before{content:'';background:url(https://cdn-www.bluestacks.com/bs-images/Samsung_Logo_White.png) no-repeat;width:100px;height:30px;background-size:contain;position:absolute;left:0;top:60%;-webkit-transform:translateY(-60%);transform:translateY(-60%)}[lang=ko-KR] .header-wrapper .container .left-section .samsung-store-lnk.show{display:block}[lang=ko-KR] .header-wrapper .container .left-section .menu-list li a{letter-spacing:0}[lang=ja] .header-wrapper .container .right-section .download-buttons{width:300px}@media (max-width:1024px){.sticky-header .download-buttons{display:none!important}.header-wrapper{position:absolute;height:auto}.header-wrapper .mob-visible{display:block!important}.header-wrapper .pc-visible{display:none!important}.header-wrapper .container{padding:10px 16px!important;max-width:100%}.header-wrapper .container .left-section .logo-block{height:40px;overflow:hidden;margin:0 50px 0 0;max-width:140px}.header-wrapper .container .left-section .logo-block .logo-img{max-width:115px;max-height:100%}.header-wrapper .container .left-section .menu-block{display:none}.header-wrapper .container .left-section .show-nav{display:block;position:fixed;top:55px;height:calc(100vh - 45px);width:100%;right:0;background-color:rgba(0,0,0,.95);overflow-y:scroll}.header-wrapper .container .left-section .show-nav .menu-list{flex-direction:column;height:100%;justify-content:flex-start;width:100%}.header-wrapper .container .left-section .show-nav .menu-list>.menu-item-has-children:first-child .sub-menu,.header-wrapper .container .left-section .show-nav .menu-list>.menu-item-has-children:nth-child(2) .sub-menu{height:auto;overflow-y:visible}.header-wrapper .container .left-section .show-nav .menu-list>.menu-item-has-children:hover>a{padding-bottom:0!important}.header-wrapper .container .left-section .show-nav .menu-list>.menu-item-has-children>a::after{bottom:25px;right:20px;top:unset}.header-wrapper .container .left-section .show-nav .menu-list>.menu-item-has-children:hover>a::after{-webkit-transform:rotate(45deg);transform:rotate(45deg);bottom:25px}.header-wrapper .container .left-section .show-nav .menu-list>li{position:relative;height:auto;width:100%;margin:0;padding:0;border:none;flex-direction:column}.header-wrapper .container .left-section .show-nav .menu-list>li a{color:rgba(255,255,255,.7);font-size:14px;letter-spacing:.88px;line-height:40px;padding-bottom:0!important;display:flex;position:relative;padding-left:20px!important}.header-wrapper .container .left-section .show-nav .menu-list>li a:hover .sub-menu{display:flex!important;position:relative;top:auto;border:none;background:0 0;width:90%;margin:0 auto}.header-wrapper .container .left-section .show-nav .menu-list>li>a{line-height:56px;padding:0 0 0 20px!important;width:100%}.header-wrapper .container .left-section .show-nav .menu-list>li:hover .sub-menu{display:none!important}.header-wrapper .container .left-section .show-nav .menu-list>li::after{content:'';position:absolute;height:1px;width:90%;border:1px solid #333;bottom:0;left:50%;-webkit-transform:translate(-50%);transform:translate(-50%)}.header-wrapper .container .left-section .show-nav .menu-list>li.active-nav>a::after{-webkit-transform:rotate(225deg)!important;transform:rotate(225deg)!important;bottom:25px!important}.header-wrapper .container .left-section .show-nav .menu-list>li.active-nav .sub-menu{display:flex!important;position:relative;top:auto;border:none;background:0 0;width:90%;margin:0 auto;flex-direction:column}.header-wrapper .container .right-section .container-autocomplete #mobile-search-opner{display:none}.header-wrapper .container .right-section .search-wrapper{display:none}.header-wrapper .container .right-section .language-selector{margin-right:25px}.header-wrapper .container .right-section .language-selector button{padding:0 25px 0 0;border:none}.header-wrapper .container .right-section .language-selector button::after{right:7px}.header-wrapper .container .right-section .language-selector:hover button{border:none!important;background:0 0}.header-wrapper .container .right-section .language-selector:hover button::after{-webkit-transform:rotate(45deg) translateY(-50%);transform:rotate(45deg) translateY(-50%)}.header-wrapper .container .right-section .language-selector:hover ul{opacity:0}.header-wrapper .container .right-section .language-selector.active ul{opacity:1}.header-wrapper .container .right-section .language-selector.active button::after{-webkit-transform:rotate(225deg);transform:rotate(225deg)}.header-wrapper .container .right-section .nav-menu-opner{position:absolute;right:0;width:20px;height:18px;border:none;-webkit-perspective:400px;perspective:400px;background:0 0;cursor:pointer;opacity:1}.header-wrapper .container .right-section .nav-menu-opner::after,.header-wrapper .container .right-section .nav-menu-opner::before{content:'';-webkit-transform-origin:center right;transform-origin:center right}.header-wrapper .container .right-section .nav-menu-opner .main-tick,.header-wrapper .container .right-section .nav-menu-opner::after,.header-wrapper .container .right-section .nav-menu-opner::before{position:absolute;left:0;width:100%;height:2px;border-radius:2px;background:#ebeff2;-webkit-transform-style:preserve-3d;transform-style:preserve-3d}.header-wrapper .container .right-section .nav-menu-opner::before{top:0}.header-wrapper .container .right-section .nav-menu-opner::after{bottom:0}.header-wrapper .container .right-section .nav-menu-opner .main-tick{top:50%;-webkit-transform-origin:50% 50%;transform-origin:50% 50%;-webkit-transform:translate3d(0,-50%,0);transform:translate3d(0,-50%,0)}.header-wrapper .container .right-section .menu-close::before{top:2px;-webkit-transform:rotate(-45deg);transform:rotate(-45deg)}.header-wrapper .container .right-section .menu-close .main-tick{-webkit-transform:rotate(45deg);transform:rotate(45deg);left:2px}.header-wrapper .container .right-section .menu-close::after{display:none}.sticky-header .container .right-section .download-buttons.pc-visible{display:none!important}.menu-opened .header-wrapper,.sticky-header{position:fixed}.menu-opened .language-selector{display:none}[lang=ko-KR] .header-wrapper .container .left-section .logo-block{margin:0 50px 0 0}[lang=ko-KR] .header-wrapper .container .left-section .menu-list>.naver-com-link a{padding-left:50px!important}[lang=ko-KR] .header-wrapper .container .left-section .menu-list>.naver-com-link a::before{left:20px;top:15px;height:20px;width:20px}[lang=ko-KR] .header-wrapper .container .left-section .samsung-store-lnk a{width:100%;height:auto;font-size:0!important}[lang=ko-KR] .header-wrapper .container .left-section .samsung-store-lnk a::before{left:20px;width:120px}[lang=ar] .header-wrapper .container .left-section .show-nav .menu-list>li>a{padding:0 20px 0 0!important}.header-wrapper .language-selector{right:-10px}.header-wrapper .container .right-section .language-selector ul{top:calc(100% + 20px)}}[lang=ar] .header-wrapper .container .left-section .logo-block{flex-direction:row-reverse}[lang=ar] .header-wrapper .container .right-section{justify-content:flex-end}[lang=ar] .header-wrapper .container .right-section .search-wrapper{margin-right:0}[lang=ar] .header-wrapper .container .right-section .language-selector{right:10px}[lang=ar] .header-wrapper .container .right-section .language-selector button svg{right:initial!important;left:-8px}[lang=ar] .header-wrapper .container .right-section .container-autocomplete{right:unset;left:calc(100% - 38px)}[lang=ar] .header-wrapper .container .right-section .container-autocomplete form{left:0;right:unset}[lang=ar] .header-wrapper .container .right-section .container-autocomplete form input:focus{border:none;outline:0}[lang=ar] .header-wrapper .container .right-section .container-autocomplete form button{left:5px;right:unset;z-index:2}[lang=ar] .header-wrapper .container .right-section .show+.search-wrapper{display:none}[lang=ar] .header-wrapper .container .left-section .menu-list>.menu-item a{padding:0}[lang=ar] .header-wrapper .container .left-section .menu-list>.menu-item-has-children a{padding:0 0 0 20px}[lang=ar] .header-wrapper .container .left-section .menu-list>.menu-item-has-children>a::after{left:0;right:unset}[lang=ar] .header-wrapper .container .right-section .download-buttons .download-started.retry-link{padding:6px 0 6px 51px}[lang=ar] .header-wrapper .container .right-section .download-buttons .download-started.retry-link .button-copy .so-button:not([data-test]){padding-right:0}@media (min-width:768px) and (max-width:1200px){[lang=es-ES] .menu-list>li a,[lang=es-ES] .selected-lang,[lang=pt-br] .menu-list>li a,[lang=pt-br] .selected-lang{text-transform:capitalize!important}.header-wrapper .container .right-section .language-selector:hover ul{opacity:1}.header-wrapper .container .right-section .language-selector:hover button::after{-webkit-transform:rotate(225deg);transform:rotate(225deg);top:calc(50% - 4px)}}@media (max-width:1024px){[lang=ar] .header-wrapper .container .left-section .logo-block{margin:0}[lang=ar] .header-wrapper .container .left-section .menu-list>.menu-item-has-children>a::after{left:unset;right:90%}[lang=ar] .header-wrapper .container .right-section .nav-menu-opner{left:0;right:unset}}.non-clickable{pointer-events:none}@media (min-width:1024px) and (max-width:1200px){[lang=ko-KR] .header-wrapper .container .left-section .samsung-store-lnk a{width:30px}[lang=ko-KR] .header-wrapper .container .left-section .samsung-store-lnk a::before{background:url(https://cdn-www.bluestacks.com/bs-images/Samsung_Logo_Art.png) no-repeat;background-size:contain;width:30px}[lang=ko-KR] .header-wrapper .container .left-section .menu-list>.naver-com-link a{font-size:0}}@media (min-width:1024px) and (max-width:1110px){.header-wrapper .container .right-section .download-buttons .download-button .button-copy .so-button{font-size:14px}.header-wrapper .container .left-section .logo-block{margin:0}[lang=ja] .header-wrapper .container .right-section .download-buttons{width:240px}}@media (min-width:1025px){[lang=de-DE] .sticky-header .container .left-section .logo-block,[lang=es-ES] .sticky-header .container .left-section .logo-block,[lang=it-IT] .sticky-header .container .left-section .logo-block,[lang=vi] .sticky-header .container .left-section .logo-block{margin:0 5px 0 0}[lang=ko-KR] .header-wrapper.sticky-header .container .left-section .logo-block{margin:0 5px 0 0}[lang=ko-KR] .header-wrapper.sticky-header .container .left-section .menu-list>.naver-com-link a{padding-left:35px!important}[lang=ko-KR] .header-wrapper .container .left-section .menu-list>li{padding:0 10px 0 14px}.sticky-header .container .left-section .logo-block{margin:0 5px 0 0;overflow:hidden}.sticky-header .container .left-section .logo-block .logo-img{height:40px}.sticky-header .container .left-section .logo-block .logo-img-text{height:26px}.sticky-header .container .left-section .menu-list>li:hover .sub-menu{top:64px}[lang=es-ES] .header-wrapper .container .left-section .menu-list>.menu-item-has-children>a::after,[lang=es-ES] .header-wrapper .container .right-section .language-selector button::after,[lang=pt-br] .header-wrapper .container .left-section .menu-list>.menu-item-has-children>a::after,[lang=pt-br] .header-wrapper .container .right-section .language-selector button::after{top:calc(50% - 3px);padding:3px}}[lang=ar] .header-wrapper .container .left-section .menu-list>li:hover .sub-menu{right:0;left:unset}@media (min-width:1024px) and (max-width:1200px){.sticky-header .container .left-section .logo-block,[lang=de-DE] .header-wrapper .container .left-section .logo-block,[lang=es-ES] .header-wrapper .container .left-section .logo-block,[lang=it-IT] .header-wrapper .container .left-section .logo-block,[lang=vi] .header-wrapper .container .left-section .logo-block{margin:0 5px 0 0}[lang=ar] .sticky-header .container .left-section .menu-list>li>a{padding-right:0!important}.sticky-header.header-wrapper .container .left-section .menu-list>li:hover .sub-menu a,[lang=de-DE] .header-wrapper .container .left-section .menu-list>li:hover .sub-menu a,[lang=vi] .header-wrapper .container .left-section .menu-list>li:hover .sub-menu a{font-size:14px}}@media (min-width:1025px) and (max-width:1200px){html:not([lang=ko-KR]) .header-wrapper .container .left-section .menu-list{position:relative}html:not([lang=ko-KR]) .replicate-item-has-children.hover{border-bottom:3px solid #fff!important}html:not([lang=ko-KR]) .replicate-item-has-children.hover a::after{-webkit-transform:rotate(225deg)!important;transform:rotate(225deg)!important}html[lang=ja]:not([lang=ko-KR]) .header-wrapper .container .left-section .menu-list>li:last-child{min-width:155px}}[lang=ko-KR] .mac-os .download-btn-a3{display:none!important}@media (min-width:1025px){[lang=ja] .download-btn-a3,[lang=ko-KR] .download-btn-a3{position:absolute;right:-48px;height:36px;width:38px;display:flex!important;flex-direction:column;background:url(https://cdn-www.bluestacks.com/bs-images/download-ic.png) no-repeat;background-color:#67b221;background-position:48% 13%;background-size:14px}[lang=ja] .download-btn-a3::after,[lang=ko-KR] .download-btn-a3::after{content:'64 bit';font-size:8px;line-height:12px;text-align:center;color:rgba(0,0,0,.8);margin:0;background:#fff;position:absolute;bottom:0;width:100%}[lang=ja] .download-btn-a3 .retry-button,[lang=ko-KR] .download-btn-a3 .retry-button{display:none}[lang=ja] .download-btn-a3{background-color:#0082e5}}@media (min-width:1025px) and (max-width:1280px){[lang=ja] .download-btn-a3,[lang=ko-KR] .download-btn-a3{position:relative;right:unset;margin-left:10px}}@media (min-width:1025px) and (max-width:1500px){[lang=ko-KR] .header-wrapper .container .right-section .language-selector{min-width:auto}[lang=ko-KR] .header-wrapper .container .right-section .download-buttons .download-button{padding:6px 10px}}body.mac-os .header-wrapper .container .right-section .download-buttons .download-button{background:0 0}body.mac-os .header-wrapper .container .right-section .download-buttons .download-button.download-started{background:#76d009}body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started .button-copy{width:90%}body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started .button-copy .so-button{width:100%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;display:block}body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started .button-copy .so-button:not([data-test]){padding-right:51px}body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started .download-loader{position:absolute;left:0;bottom:0;height:4px;background:linear-gradient(180deg,#1675b1 0,#00558a 100%);width:100%;overflow:hidden}body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started .download-loader::after{position:absolute;left:-100%;content:'';display:block;background-color:#fff;height:4px;width:100%;-webkit-animation:getWidth .5s ease-in infinite;animation:getWidth .5s ease-in infinite}[lang=ar] body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started{padding-right:0;padding-left:51px}[lang=ar] body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started .button-copy .so-button:not([data-test]){padding-right:0}[lang=es-ES] .header-wrapper .container .right-section .download-buttons .download-button{max-width:230px}.scroll-top-section{position:fixed;right:30px;height:48px;width:48px;bottom:30px;background:#37a8ff;box-shadow:0 4px 4px rgba(46,98,130,.15),0 8px 8px rgba(46,98,130,.15),0 16px 16px rgba(39,103,149,.15);border-radius:50%;cursor:pointer;opacity:0;transition:.4s ease-in;z-index:-1}.scroll-top-section .scroll-top-arrow{content:'';border:solid #fff;border-width:0 3px 3px 0;display:inline-block;padding:5px;-webkit-transform:rotate(45deg);transform:rotate(225deg) translate(-50%,-50%);position:absolute;left:calc(50% - 6px);top:11px}.animate-scroll{opacity:1;z-index:90}.container-autocomplete{margin:auto;max-width:550px}.container-autocomplete form{margin:auto;border-radius:5px;background:#fff;overflow:hidden}@media (max-width:768px){.container-autocomplete{display:none}}.container-autocomplete input{position:relative;padding:10px 15px;font-size:18px;font-size:1.125rem;font-weight:300;font-family:Lato,sans-serif;font-style:italic;background:#fff;vertical-align:middle;white-space:nowrap;border:none;border-radius:4px;border:1px solid transparent;-webkit-appearance:textfield;outline:0}.container-autocomplete input:focus{border-color:rgba(117,207,229,.3)}@media (min-width:769px){.container-autocomplete input{width:35vw;min-width:250px;height:46px;left:4px}}.container-autocomplete button{background:0 0;border:none;fill:#a9adb8;outline:0;padding:13px}.container-autocomplete button svg{pointer-events:none}@media (min-width:540px){#mobile-search-opner{display:none}}.autocomplete-suggestions{position:absolute;z-index:9999;display:none;max-height:250px;border-radius:5px;background:#fff;overflow:hidden;overflow-y:auto}.autocomplete-suggestions .search-wrapper .heading{display:flex;justify-content:space-between;margin:14px 0 5px 0;padding:0 14px}.autocomplete-suggestions .search-wrapper .heading .heading-title{font-weight:600;font-size:16px;line-height:24px;color:#28304c;margin:0}.autocomplete-suggestions .search-wrapper .heading .view-all{font-weight:400;font-size:14px;line-height:24px;color:#0082e5}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion{padding:10px 14px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i{display:flex;align-items:center}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure{height:40px;width:40px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure .object-fit{width:100%;display:inline-block;height:100%;-o-object-fit:cover;object-fit:cover}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{margin-left:18px;font-size:18px;line-height:28px;color:#394566;width:calc(100% - 58px);-webkit-box-orient:vertical;display:-webkit-box;-webkit-line-clamp:1;overflow:hidden;text-overflow:ellipsis}.autocomplete-suggestion{padding:10px;color:#3d4664;cursor:pointer}.autocomplete-suggestion:hover{background:#b0cfe5;color:#fff}.autocomplete-suggestion:not(:last-child){border-bottom:1px solid rgba(169,173,184,.3)}.autocomplete-suggestion b{font-weight:400;color:#1f8dd6}input.placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input:-moz-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input::-moz-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input::-webkit-input-placeholder{transition:opacity .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1);transition:opacity .2s cubic-bezier(.5,0,.5,1),transform .2s cubic-bezier(.5,0,.5,1),-webkit-transform .2s cubic-bezier(.5,0,.5,1)}input:focus.placeholder{opacity:0;-webkit-transform:translateX(12px);transform:translateX(12px)}input:focus:-moz-placeholder{opacity:0;transform:translateX(12px)}input:focus::-moz-placeholder{opacity:0;transform:translateX(12px)}input:focus::-webkit-input-placeholder{opacity:0;-webkit-transform:translateX(12px);transform:translateX(12px)}[lang=ar] .autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{margin-left:0;margin-right:18px}@media (max-width:767px){.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion{padding:6px 14px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figure{height:32px;width:32px}.autocomplete-suggestions .search-wrapper .suggestion-list .autocomplete-suggestion .search-i figcaption{font-size:14px;line-height:150%}}.header-wrapper{opacity:1;height:80px;background:linear-gradient(180deg,rgba(0,0,0,.55767) 0,rgba(6,6,6,.4) 50.52%,rgba(0,0,0,.112) 100%);-webkit-backdrop-filter:blur(40px);backdrop-filter:blur(40px);z-index:999}.header-wrapper .container .left-section .menu-list>li:hover .sub-menu{top:80px}.header-wrapper .container .right-section .container-autocomplete{width:0}.header-wrapper .container .right-section .container-autocomplete form{width:0;border-radius:24px;background:inherit}.header-wrapper .container .right-section .container-autocomplete form input{background:inherit;padding-left:40px;padding-right:20px;color:#fff}.header-wrapper .container .right-section .container-autocomplete form input::-webkit-input-placeholder{font-size:14px;line-height:24px;color:#c9cede;letter-spacing:.2px}.header-wrapper .container .right-section .container-autocomplete form input::-moz-placeholder{font-size:14px;line-height:24px;color:#c9cede;letter-spacing:.2px}.header-wrapper .container .right-section .container-autocomplete form input::-ms-input-placeholder{font-size:14px;line-height:24px;color:#c9cede;letter-spacing:.2px}.header-wrapper .container .right-section .container-autocomplete form input::placeholder{font-size:14px;line-height:24px;color:#c9cede;letter-spacing:.2px}.header-wrapper .container .right-section .container-autocomplete form button{left:0;right:unset}.header-wrapper .container .right-section .container-autocomplete.show{height:100%;padding:20px 0;max-width:none;background:0 0}.header-wrapper .container .right-section .container-autocomplete.show form{width:650px;border:1px solid #9ca2b7}.header-wrapper .container .right-section .language-selector button{padding-left:24px}.header-wrapper .container .right-section .language-selector button .selected-lang{line-height:24px}.header-wrapper .container .right-section .search-wrapper{opacity:1;transition:.3s ease-in-out}.header-wrapper .container .right-section .container-autocomplete.show+.search-wrapper{opacity:0;transition:.3s ease-in-out}.header-wrapper .container .right-section .download-buttons{min-width:210px!important;height:50px}.header-wrapper .container .right-section .download-buttons .browser-cta{display:none}.header-wrapper .container .right-section .download-buttons .browser-cta .svg-icon{display:none}.header-wrapper .container .right-section .download-buttons .download-button{background:linear-gradient(180deg,#159aff 0,#15b9ff 100%);border-radius:6px;transition:none;border:1px solid rgba(255,255,255,.2);padding:0 20px;max-width:235px}.header-wrapper .container .right-section .download-buttons .download-button .button-copy .so-button{line-height:28px;font-weight:500;width:100%;text-overflow:ellipsis;overflow:hidden;white-space:nowrap;display:block}.header-wrapper .container .right-section .download-buttons .download-button:hover{border:1px solid rgba(255,255,255,.2);box-shadow:0 4px 4px rgba(154,206,92,.4);color:#fff;-webkit-filter:brightness(110%);filter:brightness(110%)}.header-wrapper .container .right-section .download-buttons .cta-layout{margin-left:0;min-width:100%}.header-wrapper .container .right-section .download-buttons .cta-layout .svg-icon{margin-right:0}.header-wrapper .container .right-section .download-buttons .cta-layout .download-icon{padding:0}.header-wrapper .container .right-section .download-buttons .download-started{padding:0 10px}.header-wrapper .container .right-section .download-buttons .download-started .retry-button .retry-dwnld{height:30px;width:30px}.header-wrapper .container .right-section .download-buttons .download-started .retry-button svg{display:block!important}.header-wrapper .container .right-section .download-buttons .download-started .button-copy .so-button:not([data-test]){padding-right:0!important}.header-wrapper .container .right-section .download-buttons .download-started .button-copy .so-button .light-str{font-size:16px}.header-wrapper .container .right-section .download-buttons .download-started.retry-link .button-copy{width:90%;overflow:hidden}.header-wrapper .container .right-section .download-buttons .download-started.retry-link .button-copy .so-button:not([data-test]){padding-right:20px}.header-wrapper.sticky-header .container .right-section .download-buttons{display:block}@media (max-width:1024px){.header-wrapper{height:50px;position:fixed;top:0}.header-wrapper .container .left-section .show-nav{top:50px;height:calc(100vh - 40px)}.header-wrapper .container .right-section .language-selector ul{top:calc(100% + 10px)}.header-wrapper .container .right-section .language-selector button{padding-left:0}.header-wrapper .container .right-section .language-selector.active ul{margin-top:0}.header-wrapper .container .right-section .language-selector.active button::after{-webkit-transform:rotate(225deg);transform:rotate(225deg)}}body.mac-os .header-wrapper .container .right-section .download-buttons{background:0 0}body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started .button-copy{overflow:hidden}body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started .retry-button{background:0 0}.header-wrapper .container .right-section .language-selector button .selected-lang{font-family:Poppins,sans-serif}.header-wrapper .container .right-section .download-buttons .download-bs *{pointer-events:none}[lang=es-ES] .header-wrapper .container .right-section .download-buttons .download-button{max-width:235px}[lang=ar] .header-wrapper .download-bsx.download-started .retry-button{padding-right:12px}[lang=ar] body:not(.mac-os) .header-wrapper .container .right-section .download-buttons .download-button.download-started{padding-left:0}[lang=ja] .header-wrapper .container .right-section .download-buttons{width:255px}.firefox .header-wrapper{background:#1f2640}.notification-popup{width:100%;background:linear-gradient(112.34deg,rgba(255,196,183,.3) 35.44%,rgba(211,204,255,.3) 100%),#fff;height:115px;padding:16px;overflow:hidden;display:flex;align-items:center}.notification-popup .desc{margin-left:0;font-weight:700;font-size:15px;line-height:150%;color:#1f1637;flex:1;text-align:left}.notification-popup .heading-wrapper{position:relative;align-items:center;width:100%}.notification-popup .content-added{margin-left:8px}.notification-popup .button-desc{margin-top:8px}.notification-popup .close-notify-popup{height:14px;width:14px;justify-content:center;align-items:center;position:absolute;right:-6px;top:-6px}.notification-popup .close-notify-popup *{pointer-events:none}.notification-popup .content{font-weight:400;font-size:12px;line-height:150%;margin-top:0;color:#473e5f;width:100%;margin-bottom:0;flex:1;text-align:left}.notification-popup .content-wrapper{margin-top:0}.notification-popup .try-now-btn{background:linear-gradient(270deg,#7b4cff 0,#0ea4c5 99.48%);border-radius:6px;font-weight:600;font-size:12px;line-height:150%;color:#fff;text-align:center;padding:5px 14px;margin-left:8px;height:32px}.notification-popup.sticky-popup{position:fixed;top:0;z-index:999999;left:0;right:0}.notification-popup.hide{display:none}.flex{display:flex}.flex-col{flex-direction:column}.notification-popup:not(.hide)+.header-wrapper{top:114px}.notification-popup:not(.hide)+.header-wrapper+.main-container .hero-banner-section .container.flex .download-buttons:not(.download-wrapper-prereg){top:180px}.is-iPhone .notification-popup+.bsx-header-wrapper,.mac-os .notification-popup+.bsx-header-wrapper,.mobile-ios .notification-popup+.bsx-header-wrapper,.safari .notification-popup+.bsx-header-wrapper{top:0}@media (max-width:767px){html[lang=en-US] body[data-country=AU].chrome-mobile .show-subscribe-content,html[lang=en-US] body[data-country=AU].is-android .show-subscribe-content,html[lang=en-US] body[data-country=AU].is-iPhone .show-subscribe-content,html[lang=en-US] body[data-country=AU].linux .show-subscribe-content,html[lang=en-US] body[data-country=AU].mac-os .show-subscribe-content,html[lang=en-US] body[data-country=AU].mobile-android .show-subscribe-content,html[lang=en-US] body[data-country=AU].mobile-ios .show-subscribe-content,html[lang=en-US] body[data-country=AU].safari .show-subscribe-content,html[lang=en-US] body[data-country=CA].chrome-mobile .show-subscribe-content,html[lang=en-US] body[data-country=CA].is-android .show-subscribe-content,html[lang=en-US] body[data-country=CA].is-iPhone .show-subscribe-content,html[lang=en-US] body[data-country=CA].linux .show-subscribe-content,html[lang=en-US] body[data-country=CA].mac-os .show-subscribe-content,html[lang=en-US] body[data-country=CA].mobile-android .show-subscribe-content,html[lang=en-US] body[data-country=CA].mobile-ios .show-subscribe-content,html[lang=en-US] body[data-country=CA].safari .show-subscribe-content,html[lang=en-US] body[data-country=CH].chrome-mobile .show-subscribe-content,html[lang=en-US] body[data-country=CH].is-android .show-subscribe-content,html[lang=en-US] body[data-country=CH].is-iPhone .show-subscribe-content,html[lang=en-US] body[data-country=CH].linux .show-subscribe-content,html[lang=en-US] body[data-country=CH].mac-os .show-subscribe-content,html[lang=en-US] body[data-country=CH].mobile-android .show-subscribe-content,html[lang=en-US] body[data-country=CH].mobile-ios .show-subscribe-content,html[lang=en-US] body[data-country=CH].safari .show-subscribe-content,html[lang=en-US] body[data-country=GB].chrome-mobile .show-subscribe-content,html[lang=en-US] body[data-country=GB].is-android .show-subscribe-content,html[lang=en-US] body[data-country=GB].is-iPhone .show-subscribe-content,html[lang=en-US] body[data-country=GB].linux .show-subscribe-content,html[lang=en-US] body[data-country=GB].mac-os .show-subscribe-content,html[lang=en-US] body[data-country=GB].mobile-android .show-subscribe-content,html[lang=en-US] body[data-country=GB].mobile-ios .show-subscribe-content,html[lang=en-US] body[data-country=GB].safari .show-subscribe-content,html[lang=en-US] body[data-country=US].chrome-mobile .show-subscribe-content,html[lang=en-US] body[data-country=US].is-android .show-subscribe-content,html[lang=en-US] body[data-country=US].is-iPhone .show-subscribe-content,html[lang=en-US] body[data-country=US].linux .show-subscribe-content,html[lang=en-US] body[data-country=US].mac-os .show-subscribe-content,html[lang=en-US] body[data-country=US].mobile-android .show-subscribe-content,html[lang=en-US] body[data-country=US].mobile-ios .show-subscribe-content,html[lang=en-US] body[data-country=US].safari .show-subscribe-content{display:none}html[lang=en-US] body[data-country=AU].chrome-mobile .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=AU].is-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=AU].is-iPhone .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=AU].linux .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=AU].mac-os .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=AU].mobile-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=AU].mobile-ios .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=AU].safari .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CA].chrome-mobile .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CA].is-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CA].is-iPhone .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CA].linux .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CA].mac-os .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CA].mobile-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CA].mobile-ios .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CA].safari .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CH].chrome-mobile .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CH].is-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CH].is-iPhone .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CH].linux .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CH].mac-os .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CH].mobile-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CH].mobile-ios .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=CH].safari .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=GB].chrome-mobile .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=GB].is-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=GB].is-iPhone .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=GB].linux .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=GB].mac-os .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=GB].mobile-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=GB].mobile-ios .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=GB].safari .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=US].chrome-mobile .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=US].is-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=US].is-iPhone .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=US].linux .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=US].mac-os .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=US].mobile-android .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=US].mobile-ios .btn-subscribe.mob-visible,html[lang=en-US] body[data-country=US].safari .btn-subscribe.mob-visible{display:none!important}}html:not([lang=en-US]) .notification-popup{display:none!important}body[data-language=en] .notification-popup:not(.hide){display:flex!important}@media (min-width:768px){.notification-popup{display:none!important}}@media (max-width:355px){.notification-popup{padding:10px}.notification-popup .notification-icon img{height:48px;width:48px}.notification-popup .content-added{margin-left:6px}.notification-popup .content-added .desc{font-size:13px}.notification-popup .try-now-btn{margin-left:2px}.notification-popup .button-desc{margin-top:8px}.notification-popup .button-desc .content{font-size:10px}.header-wrapper .container .left-section .logo-block .logo-img-text{display:none}}  </style>

  <noscript id="deferred-styles">
    <link rel="stylesheet"
      href="https://www.bluestacks.com/wp-content/themes/bluestacks/dist/css/download-page/styles.css?v=d3f29ce352">
    <link rel="stylesheet"
      href="https://www.bluestacks.com/wp-content/themes/bluestacks/dist/css/home-v2/common-footer.css?v=4d219022be">
  </noscript>
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
  <link rel="preload" as="image" href="https://cdn-www.bluestacks.com/bs-images/Experience_ultimate_gameplay.png">
  <link rel="preload" as="style"
    href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Roboto&display=swap"
    onload="this.rel='stylesheet'" />
  <link href="https://fonts.googleapis.com/css2?family=Roboto+Slab:wght@900&display=swap" rel="stylesheet">
  <style>
  .halowin-goht-img,
  .pumpkin-animation,
  .pumpkin-animation-faq,
  .swinging-spider,.all-versions .download-version-list .version-li .r-section .section-block.section-block-2.hide-mac {
    display: none;
  }

  .windows-os .cloud-games-section .download-bsx:not(.download-bs10) .download-loader,
  .windows-os .cloud-games-section .download-bs5 .retry-dwnld,
  .bsx-hero-section .download-deal-bs5-btn-width-auto:hover::before {
    display: none !important;
  }

  .windows-os .cloud-games-section .cta-layout.download-bs10 {
    cursor: pointer;
  }

  .bsx-hero-section .hero-upper-text .hero-text .cta-elements .download-wrapper-layer .download-other-versions:hover {
    text-decoration: underline !important;
  }

  .windows-os .cloud-games-section .cta-layout .svg-icon::after,
  .windows-os .cloud-games-section .download-bs5.download-bsx.download-started .retry-button::after {
    display: none;
  }

  .windows-os .cloud-games-section .cta-layout .svg-icon {
    padding-right: 4px !important;
  }

  .windows-os .cloud-games-section .download-bsx.download-bs5.download-started .retry-button {
    background: url(https://cdn-www.bluestacks.com/bs-images/download-generic-img.png) no-repeat;
    width: 33px;
    height: 30px;
    background-size: 29px;
    margin-right: 4px;
    background-position: center center;
    display: none;
  }

  .windows-os .cloud-games-section .download-bsx:not(.download-bs10) {
    background: transparent;
    border: none;
    padding: 0 !important;
    width: auto;
    height: auto;
    margin-bottom: 0 !important;
    min-width: auto;
    position: relative;
    margin-right: 24px;
    display: none;
  }

  .windows-os .cloud-games-section .download-bsx:not(.download-bs10) .download-icon img {
    width: 30px;
    height: 30px;
  }

  .windows-os .cloud-games-section .download-bs5 .retry-dwnld {
    width: 25px;
  }

  .windows-os .cloud-games-section .download-bs5 .so-button {
    flex-direction: row-reverse !important;
    align-items: center !important;
  }

  .windows-os .cloud-games-section .download-bs5.download-started .so-button {
    gap: 3px;
  }

  .windows-os .cloud-games-section .download-bs5 .so-button .small-txt,
  .windows-os .cloud-games-section .download-bs5 .so-button,
  .windows-os .cloud-games-section .download-bs5.download-bsx.download-started .button-copy .so-button .light-str {
    font-size: 14px !important;
  }

  .windows-os[data-language="en"] .cloud-games-section .download-bsx.download-bs5.download-started .button-copy .so-button .light-str {
    font-size: 0 !important;
  }

  .windows-os[data-language="en"] .cloud-games-section .download-bsx.download-bs5.download-started .button-copy .so-button .light-str::after {
    content: "BlueStacks 5";
    font-size: 14px;
    font-weight: 500;
    position: relative;
    line-height: 28px;
    top: 0;
  }

  .windows-os .cloud-games-section .download-bsx.download-bs5.download-started .button-copy .so-button .light-str::after {
    top: 0;
  }

  .windows-os .cloud-games-section .download-bs5 .so-button,
  .windows-os .cloud-games-section .cta-layout.download-bs5 .so-button {
    font-weight: 500;
  }

  .windows-os .cloud-games-section .download-bs5 .so-button .small-txt {
    margin: 0px 4px;
  }

  .windows-os .cloud-games-section .download-bs5 .download-icon {
    height: 30px !important;
    margin-right: 0;
    display: none;
  }

  .windows-os .cloud-games-section .download-bs5:hover {
    box-shadow: none;
    text-decoration: none !important;
  }

  .windows-os .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements {
    flex-flow: column;
    justify-content: center;
    align-items: center;
  }

  .download-page .main-container .cloud-games-section .heading-layer .cta-wrapper .cta-elements {
    flex-flow: column;
    justify-content: center;
    align-items: center;
  }
  .mobile-android .download-page .main-container .cloud-games-section .container-full .know-more-btn{
    display: none !important;
  }
  .mobile-android .download-page .main-container .cloud-games-section .container-full .install-mobile-app{
    display: flex !important;

  }
            
  </style>
</head>

<body data-rsssl=1 id="n-h" data-language="ar" class="experiment_variant">
  <!-- Google Tag Manager (noscript) -->
<!-- <noscript><iframe src="https://www.googletagmanager.com/ns.html?id=GTM-W4Z4SG"
height="0" width="0" style="display:none;visibility:hidden"></iframe></noscript> -->
<!-- End Google Tag Manager (noscript) -->
  <div class="body-wrapper download-page">
    <header class="header-wrapper bsx-header-wrapper" data-header="new">
    <div class="container">
    <div class="left-section">
      <a href="https://www.bluestacks.com/ar/index.html" class="logo-block">
        <img src="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" height="38" class="img-responsive logo-img"
          alt="BlueStacks Logo" />
        <img src="https://cdn-www.bluestacks.com/bs-images/logo-text.png" height="38"
          class="img-responsive logo-img-text" alt="BlueStacks - Android Emulator" />
      </a>
      <nav id="header-menu" class="menu-block"><ul id="menu-homepage-v2-header-menu-arabic-homepage-v2-header-menu" class="menu-list"><li id="menu-item-1674810" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-1674810"><a href="https://www.bluestacks.com/ar/features.html">المميزات</a>
<ul class="sub-menu">
	<li id="menu-item-1853574" class="mob-visible menu-item menu-item-type-post_type menu-item-object-page menu-item-1853574"><a href="https://www.bluestacks.com/ar/features.html">الجميع</a></li>
	<li id="menu-item-1674812" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674812"><a href="https://www.bluestacks.com/ar/features/game-controls.html">أدوات تحكم اللعبة</a></li>
	<li id="menu-item-1674834" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674834"><a href="https://www.bluestacks.com/ar/features/multi-instance.html">متعددة مثيل</a></li>
	<li id="menu-item-1674836" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674836"><a href="https://www.bluestacks.com/ar/features/multi-instance-sync.html">متعدد مثيل المزامنة</a></li>
	<li id="menu-item-1674822" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674822"><a href="https://www.bluestacks.com/ar/features/smart-controls.html">الضوابط الذكية</a></li>
	<li id="menu-item-1674830" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674830"><a href="https://www.bluestacks.com/ar/features/macros.html">وحدات الماكرو</a></li>
	<li id="menu-item-1941748" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1941748"><a href="https://www.bluestacks.com/ar/features/native-gamepad-support.html">دعم Gamepad الأصلي</a></li>
	<li id="menu-item-1674818" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674818"><a href="https://www.bluestacks.com/ar/features/eco-mode.html">نمط الخفيف</a></li>
	<li id="menu-item-2847068" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2847068"><a href="https://www.bluestacks.com/ar/features/performance-modes.html">أوضاع الأداء</a></li>
	<li id="menu-item-2847072" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2847072"><a href="https://www.bluestacks.com/ar/features/trim-memory.html">تقليم الذاكرة</a></li>
	<li id="menu-item-1674814" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674814"><a href="https://www.bluestacks.com/ar/features/rerolling.html">إعادة المتداول</a></li>
	<li id="menu-item-1674838" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674838"><a href="https://www.bluestacks.com/ar/features/real-time-translation.html">الترجمة في الوقت الحقيقي</a></li>
	<li id="menu-item-1674828" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674828"><a href="https://www.bluestacks.com/ar/features/shooting-mode.html">وضع إطلاق النار</a></li>
	<li id="menu-item-1674826" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674826"><a href="https://www.bluestacks.com/ar/features/moba-mode.html">وضع موبا</a></li>
	<li id="menu-item-1674820" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674820"><a href="https://www.bluestacks.com/ar/features/script.html">النصي</a></li>
	<li id="menu-item-1674832" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674832"><a href="https://www.bluestacks.com/ar/features/high-fps.html">FPS عالية</a></li>
	<li id="menu-item-1674824" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674824"><a href="https://www.bluestacks.com/ar/features/high-definition.html">الرسومات عالية الوضوح</a></li>
	<li id="menu-item-1864396" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1864396"><a href="https://www.bluestacks.com/ar/features/utc-converter.html">محول UTC</a></li>
	<li id="menu-item-1674816" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674816"><a href="https://www.bluestacks.com/ar/features/utility.html">ميزات الأداة المساعدة</a></li>
</ul>
</li>
<li id="menu-item-1648972" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-1648972"><a href="https://www.bluestacks.com/ar/apps.html">ألعاب</a>
<ul class="sub-menu">
	<li id="menu-item-1853576" class="mob-visible menu-item menu-item-type-post_type menu-item-object-page menu-item-1853576"><a href="https://www.bluestacks.com/ar/apps.html">الجميع</a></li>
	<li id="menu-item-2445752" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2445752"><a href="https://www.bluestacks.com/ar/top-games.html">أفضل الألعاب</a></li>
	<li id="menu-item-1648974" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648974"><a href="https://www.bluestacks.com/ar/apps/action.html">ألعاب حركة</a></li>
	<li id="menu-item-1648984" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648984"><a href="https://www.bluestacks.com/ar/apps/role-playing.html">تقمص الأدوار</a></li>
	<li id="menu-item-1648990" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648990"><a href="https://www.bluestacks.com/ar/apps/strategy.html">الإستراتيجية</a></li>
	<li id="menu-item-1648976" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648976"><a href="https://www.bluestacks.com/ar/apps/adventure.html">مغامرات</a></li>
	<li id="menu-item-1648978" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648978"><a href="https://www.bluestacks.com/ar/apps/arcade.html">ألعاب كلاسيكية</a></li>
	<li id="menu-item-1849884" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1849884"><a href="https://www.bluestacks.com/ar/apps/card.html">الورق</a></li>
	<li id="menu-item-1849886" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1849886"><a href="https://www.bluestacks.com/ar/apps/casual.html">خفيفة</a></li>
	<li id="menu-item-1648980" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648980"><a href="https://www.bluestacks.com/ar/apps/puzzle.html">ألغاز</a></li>
	<li id="menu-item-1648982" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648982"><a href="https://www.bluestacks.com/ar/apps/racing.html">سباق</a></li>
	<li id="menu-item-1648986" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648986"><a href="https://www.bluestacks.com/ar/apps/simulation.html">المحاكاة</a></li>
	<li id="menu-item-1648988" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1648988"><a href="https://www.bluestacks.com/ar/apps/sports.html">رياضة</a></li>
</ul>
</li>
<li id="menu-item-1843784" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-1843784"><a href="https://www.bluestacks.com/ar/blog.html">مدونة</a>
<ul class="sub-menu">
	<li id="menu-item-1853578" class="mob-visible menu-item menu-item-type-post_type menu-item-object-page menu-item-1853578"><a href="https://www.bluestacks.com/ar/blog.html">مدونة</a></li>
	<li id="menu-item-3192916" class="menu-item menu-item-type-taxonomy menu-item-object-blog_category menu-item-3192916"><a href="https://www.bluestacks.com/ar/blog/games.html">ألعاب</a></li>
	<li id="menu-item-3192732" class="menu-item menu-item-type-taxonomy menu-item-object-blog_category menu-item-3192732"><a href="https://www.bluestacks.com/ar/blog/inside-bluestacks.html">داخل بلوستاكس</a></li>
	<li id="menu-item-3192890" class="menu-item menu-item-type-taxonomy menu-item-object-blog_category menu-item-3192890"><a href="https://www.bluestacks.com/ar/blog/bluestacks-roundups.html">BlueStacks جولات</a></li>
	<li id="menu-item-3192892" class="menu-item menu-item-type-taxonomy menu-item-object-blog_category menu-item-3192892"><a href="https://www.bluestacks.com/ar/blog/news.html">أخبار</a></li>
	<li id="menu-item-3183612" class="menu-item menu-item-type-taxonomy menu-item-object-blog_category menu-item-3183612"><a href="https://www.bluestacks.com/ar/blog/bluestacks-videos.html">BlueStacks الفيديو</a></li>
</ul>
</li>
<li id="menu-item-1648994" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1648994"><a href="https://support.bluestacks.com/hc/ar">الدعم</a></li>
</ul></nav>    </div>
    <div class="right-section">
      
<div class="container-autocomplete">

	<button type="button" id="mobile-search-opner">
		<svg width="21" height="22">
			<path fill-rule="nonzero" d="M7.5728836 15.4271164C8.9627505 16.4174803 10.6633478 17 12.5 17c4.69442061 0 8.5-3.8055794 8.5-8.5C21 3.80557939 17.19442061 0 12.5 0 7.8055794 0 4 3.80557939 4 8.5c0 2.3472103.9513948 4.4722103 2.4895923 6.0104077L.4922264 20.5077736C.217172 20.782828.2238576 21.2238576.5 21.5c.2780676.2780676.7203773.2796227.9922264.0077736l6.0806572-6.0806572zM12.5 16C8.3578642 16 5 12.6421358 5 8.5 5 4.35786417 8.3578642 1 12.5 1S20 4.35786417 20 8.5c0 4.1421358-3.3578642 7.5-7.5 7.5z"/>
		</svg>
	</button>

	<form class="flex y">
		<input type="search" data-search-autocomplete placeholder="البحث" maxlength="50">
		<button type="submit">
      <svg class="search-icon"><use xlink:href="#search" fill="#FFD500" /></svg>
		</button>
	</form>

</div>

      <div class="search-wrapper pc-visible">
        <svg class="icon-search">
          <use xlink:href="#search" fill="#FFD500" />
        </svg>
      </div>
            <i style="width: 15%">&nbsp;</i>      <div class="download-buttons">
                  <a style="" data-extra='null' data-utm-mac='homepage-dl-button' data-app-mac='' data-win-text-alignment="" data-mac-text-color="" data-win-text-color="" data-win-bg-color="" data-mac-bg-color="" data-mac-border-color="" data-win-bg-img=""  data-mac-bg-img="" data-mac-text-alignment="" data-mac-text-alignment-with-pipe= href="https://www.bluestacks.com/download.html?utm_campaign=download-page-en" class=" download-bs avocado-green bigger  download-button homepage-download-button activated cta-layout download-bsx " data-utm="download-page-dl-button-ar" data-animation="إعادة التحميل"  data-lang="en" data-mac-attr=" قم بتنزيل BlueStacks " data-mac-attr-is-default="">

            <div class="button-copy button-copy flex align-center wfull">
          <span class="download-icon svg-icon relative" >
              </span>
      <span style="" class="so-button flex flex-col" data-test=" قم بتنزيل BlueStacks " data-macos=" قم بتنزيل BlueStacks "  data-mac-text-alignment="">
       قم بتنزيل BlueStacks         <span class="version-info">&nbsp;</span>
      
            </div>
      </a>

        <!-- <button>Download BlueStacks</button> -->
      </div>
      <button class="nav-menu-opner mob-visible">
        <i class="main-tick"></i>
      </button>
    </div>
  </div>
</header><style>
  .apk-btn-banner-container {
    display:none;
    position: fixed;
    bottom: 20px;
    left: 0;
    right: 0;
    justify-content: center;
    padding: 0 20px;
    z-index: 999;
    transition: transform 0.3s ease, opacity 0.3s ease;
    padding: 11px 14px;


    .apk-btn-banner-container.hidden {
      transform: translateY(100%);
      opacity: 0;
    }

    .apk-banner {
      display: flex;
      align-items: center;
      gap:10px;
      justify-content: space-between;
      background-color: #e8f0f7;
      border-radius: 16px;
      padding: 10px;
      width: 100%;
      max-width: 450px;
      box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
      height: 62px;
      border: 2px solid #B2C2D4;
      background: linear-gradient(106deg, #FFF3C9 -35.6%, #C3DFFF 70.26%);
    }

    .logo-section {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .logo {
      position: relative;
      width: 36px;
      display: flex;
      align-items: center;
      min-width: 22px;
    }

    .about-text {
      display: flex;
      align-items: center;
      gap: 4px;
      font-weight: 600;
      font-size: 14px;
      color: #333;
    }

    .chevron-right {
      width: 20px;
      height: 20px;
      position: relative;
    }

    .action-section {
      display: flex;
      align-items: center;
      gap: 8px;
    }

    .install-button {
      background-color: #0095ff;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 8px 20px;
      font-weight: 600;
      font-size: 14px;
      cursor: pointer;
      transition: background-color 0.2s;
      border-radius: 7.156px;
      border: 1.063px solid #159AFF;
      background: linear-gradient(180deg, #159AFF 0%, #15B9FF 100%);
      height: 40px;
    }

    .install-button:hover {
      background-color: #0085e6;
    }

    .close-button {
      display: flex;
      align-items: center;
      justify-content: center;
      background: none;
      border: none;
      color: #666;
      cursor: pointer;
      padding: 4px;
      position: relative;
      width: 24px;
      height: 24px;
    }

    .close-button:hover {
      color: #333;
    }

    .close-icon {
      position: relative;
      width: 16px;
      height: 16px;
    }

    .close-icon::before {
      transform: rotate(45deg);
    }

    .close-icon::after {
      transform: rotate(-45deg);
    }

    .close-button:hover .close-icon::before,
    .close-button:hover .close-icon::after {
      background-color: #333;
    }
  }


  @media (max-width: 480px) {
    .apk-btn-banner-container {
      bottom: 10px;
      padding: 0 10px;

      .banner {
        padding: 8px 12px;
      }

      .about-text {
        font-size: 13px;
      }

      .install-button {
        padding: 6px 16px;
        font-size: 13px;
        height: 36px;
      }
    }
  }

  .is-iPhone .apk-btn-banner-container{
    display: none !important;
  }
  @media (min-width: 767px) {
    .apk-btn-banner-container {
      display: none !important;
    }
  }
</style>

<div class="apk-btn-banner-container" id="apkBannerContainer">
  <div class="apk-banner">
    <div class="logo-section">
      <div class="logo">
        <img class="img-responsive" src="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" height="40px" width="40px" />
      </div>
      <a href="/ar/mobileapp.html" class="about-text">ما هو BlueStacks Mobile<span class="chevron-right">
          <svg xmlns="http://www.w3.org/2000/svg" width="17" height="17" viewBox="0 0 17 17" fill="none">
            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.64228 14.336C5.63665 14.569 5.68798 14.7987 5.78974 14.9959C5.89149 15.1931 6.03905 15.3488 6.21359 15.4432C6.38814 15.5376 6.58176 15.5664 6.76976 15.5259C6.95777 15.4854 7.13163 15.3775 7.26918 15.2159L12.3719 9.36164C12.4676 9.25192 12.5439 9.11934 12.596 8.97224C12.6481 8.82514 12.675 8.66665 12.675 8.50637C12.675 8.34609 12.6481 8.18745 12.596 8.04036C12.5439 7.89326 12.4676 7.76082 12.3719 7.6511L7.26918 1.79686C7.17804 1.68643 7.0697 1.59939 6.95055 1.54094C6.8314 1.48249 6.70387 1.4537 6.57548 1.45644C6.4471 1.45918 6.32046 1.49334 6.20306 1.55683C6.08565 1.62033 5.97986 1.71197 5.89194 1.82622C5.80401 1.94046 5.73573 2.07505 5.69113 2.22206C5.64653 2.36906 5.62651 2.52545 5.63226 2.68205C5.63801 2.83866 5.66941 2.99235 5.72461 3.13389C5.77981 3.27543 5.85768 3.40192 5.95363 3.5061L10.3116 8.50637L5.95363 13.5065C5.85899 13.6124 5.78267 13.7403 5.7292 13.8827C5.67572 14.0252 5.64617 14.1793 5.64228 14.336Z" fill="#020D24" />
          </svg>
        </span>
      </a>
    </div>
    <div class="action-section">
      <button class="install-button show-apk-download-popup">تثبيت</button>
      <button class="close-button" id="apkBannerCloseButton" aria-label="Close">
        <span class="close-icon">
          <svg xmlns="http://www.w3.org/2000/svg" width="14" height="13" viewBox="0 0 14 13" fill="none">
            <path d="M12.5 1L1.5 12" stroke="#020D24" stroke-opacity="0.7" stroke-width="2" stroke-linecap="round" />
            <path d="M12.5 12L1.5 1" stroke="#020D24" stroke-width="2" stroke-linecap="round" />
          </svg>
        </span>
      </button>
    </div>
  </div>
</div>

<script>
  window.addEventListener("scroll", function () {
  const note = document.getElementById("apkBannerContainer");
  if (window.scrollY > 100) { 
    note.style.display="flex"
  } else {
    note.style.display="none"
  }
});

  document.addEventListener('DOMContentLoaded', function() {
    const apkBannerCloseButton = document.getElementById('apkBannerCloseButton');
    const apkBannerContainer = document.getElementById('apkBannerContainer');
    // Show banner with a slight delay and animation
    setTimeout(() => {
      apkBannerContainer.style.transform = 'translateY(0)';
      apkBannerContainer.style.opacity = '1';
    }, 500);

    apkBannerCloseButton.addEventListener('click', function() {
      apkBannerContainer.classList.add('hidden');

      // Remove from DOM after animation completes
      setTimeout(() => {
        apkBannerContainer.style.display = 'none';
      }, 300);
    });

    window.showApkDownloadPopup = () => {
      document.querySelector('.apk-download-popup').style.display = 'block';
    }

    document.querySelector('.show-apk-download-popup').addEventListener('click', function() {
      sendAddExperimentData(
          'BlueStacksMobileAppPopup',
          'DOWNLOAD_PAGE',
          'en',
          'open_popup',
          'sticky',
          '',
          '',
          'download-page-dl-button-ar',
        );
      window.showApkDownloadPopup();
    });
    const downloadMobileButton = document.querySelector('.download-button-mobile');
    downloadMobileButton && downloadMobileButton.addEventListener('click', () => {
      sendAddExperimentData(
          'BlueStacksMobileAppPopup',
          'DOWNLOAD_PAGE',
          'en',
          'open_popup',
          'center',
          '',
          '',
          'download-page-dl-button-ar',
        );
    });

    const downloadAppOnMobileBtn = document.querySelector('.download-app-on-mobile');
    downloadAppOnMobileBtn && downloadAppOnMobileBtn.addEventListener('click', () => {
      sendAddExperimentData(
          'BlueStacksMobileAppPopup',
          'DOWNLOAD_PAGE',
          'en',
          'open_popup',
          'center',
          '',
          '',
          'download-page-dl-button-ar',
        );
    });
  });
</script><style>
  .mobileapp-popup {
    position: fixed;
    top: 0px;
    left: 0px;
    width: 100%;
    height: 959px;
    background: rgba(0, 0, 0, 0.7) none repeat scroll 0% 0% / auto padding-box border-box;
    z-index: 99999999;
    display: block;
    outline: rgb(0, 0, 0) none 0px;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
    display: none;
  }

  .mobilapp-popup-container {
    border-radius: 12px;
    background: linear-gradient(180deg, rgba(244, 250, 255, 0.95) 0%, rgba(240, 243, 255, 0.95) 100%);
    -webkit-backdrop-filter: blur(11.449999809265137px);
    backdrop-filter: blur(11.449999809265137px);
    max-width: 675px;
    position: absolute;
    top: calc(100vh / 2);
    left: 50%;
    width: 100%;
    -webkit-transform: translate(-50%, -50%);
    -ms-transform: translate(-50%, -50%);
    transform: translate(-50%, -50%);
    text-align: center;
    background-clip: padding-box;
  }

  .qr-close-modal {
    position: absolute;
    top: 2px;
    right: 2px;
    border: 0px none rgb(0, 0, 0);
    padding: 21px;
    background: rgba(0, 0, 0, 0) none repeat scroll 0px 0px / auto padding-box border-box;
    cursor: pointer;
    box-sizing: border-box;
    margin: 0px;
  }

  .qr-close-modal::before,
  .qr-close-modal::after {
    position: absolute;
    left: 50%;
    top: 50%;
    content: "";
    background-color: #3d5a85e5;
    -webkit-transform: translate(-50%, -50%) rotate(45deg);
    -ms-transform: translate(-50%, -50%) rotate(45deg);
    transform: translate(-50%, -50%) rotate(45deg);
    -webkit-transform-origin: center center;
    -ms-transform-origin: center center;
    transform-origin: center center;
  }

  .qr-close-modal::after {
    height: 50%;
    width: 2px;
  }

  .qr-close-modal::before {
    height: 2px;
    width: 50%;
  }

  .mobile-app-popup {
    padding: 25px 38px;
    gap: 20px;
    flex-direction: column;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: flex;
    box-sizing: border-box;
    margin: 0px;
  }

  .popup-heading {
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 30px;
    color: rgb(40, 48, 76);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .apk-app-section {
    gap: 20px;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: flex;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .bluestacks-verified-container {
    gap: 4px;
    flex-direction: column;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: flex;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .bluestacks-verified-container>figure {
    width: 70px;
    height: 62px;
    position: relative;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .bluestacks-verified-container .icon {
    width: 100%;
    height: 62px;
    object-fit: cover;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .bluestacks-verified-container .icon-title {
    font-size: 12px;
    font-style: normal;
    font-weight: 500;
    line-height: 30px;
    color: rgb(40, 48, 76);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .verify-section {
    gap: 17px;
    -webkit-box-align: stretch;
    align-items: stretch;
    -webkit-box-pack: center;
    justify-content: center;
    display: flex;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .qr-div {
    border-radius: 12px;
    background: rgb(255, 255, 255) none repeat scroll 0% 0% / auto padding-box border-box;
    padding: 21px 16px;
    gap: 13px;
    flex-direction: column;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: flex;
    box-sizing: border-box;
    margin: 0px;
    width: 200px;
  }

  .qr-div-heading {
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 700;
    line-height: 30px;
    color: rgb(40, 48, 76);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .qr-img-figure {
    width: 137px;
    height: 137px;
    position: relative;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .qr-img {
    width: 100%;
    height: 137px;
    object-fit: cover;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .qr-desc {
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 600;
    line-height: 21px;
    color: rgb(40, 48, 76);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .or-text {
    text-align: center;
    font-size: 14px;
    font-style: normal;
    font-weight: 700;
    line-height: 30px;
    align-self: center;
    color: rgba(39, 49, 63, 0.4);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .email-verification-container {
    padding: 21px 16px;
    gap: 20px;
    border-radius: 12px;
    background: rgb(255, 255, 255) none repeat scroll 0% 0% / auto padding-box border-box;
    flex-direction: column;
    display: flex;
    box-sizing: border-box;
    margin: 0px;
  }

  .email-heading {
    text-align: center;
    font-size: 16px;
    font-weight: 700;
    line-height: 30px;
    color: rgb(40, 48, 76);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .email-subheading {
    font-size: 14px;
    text-align: left;
    font-weight: 600;
    line-height: 24px;
    color: rgb(40, 48, 76);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .email-inputs {
    flex-direction: column;
    display: flex;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .email-input-field {
    padding: 11px 10px;
    font-size: 14px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    border-radius: 3px;
    border: 1px solid rgba(0, 0, 0, 0.03);
    background: rgb(247, 244, 244) none repeat scroll 0% 0% / auto padding-box border-box;
    margin-bottom: 8px;
    box-sizing: border-box;
    margin: 0px 0px 8px;
  }

  .terms-div {
    gap: 8px;
    -webkit-box-align: center;
    align-items: center;
    margin-bottom: 16px;
    display: flex;
    box-sizing: border-box;
    margin: 0px 0px 16px;
    padding: 0px;
  }

  .terms-input {
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .terms-label {
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
    color: rgba(40, 48, 76, 0.5);
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .terms-and-condition-end {
    color: rgb(26, 159, 255);
    text-decoration: underline solid rgb(26, 159, 255);
    background-color: rgba(0, 0, 0, 0);
    cursor: pointer;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .send-mail {
    border-radius: 4px;
    background: rgba(0, 0, 0, 0) linear-gradient(rgb(21, 154, 255) 0%, rgb(21, 185, 255) 100%) repeat scroll 0% 0% / auto padding-box border-box;
    color: rgb(255, 255, 255);
    text-align: center;
    font-size: 16px;
    font-style: normal;
    font-weight: 500;
    line-height: 24px;
    padding: 10px 0px;
    cursor: pointer;
    box-sizing: border-box;
    margin: 0px;
  }

  .send-mail:disabled {
    cursor: default;
    pointer-events: none;
  }

  .apk-email-desc {
    font-size: 12px;
    font-weight: 500;
    line-height: 18px;
    color: rgba(40, 48, 76, 0.5);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
    text-align: left;
  }

  .apk-info-section {
    gap: 14px;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: flex;
    box-sizing: border-box;
    margin: 0px;
    padding: 0px;
  }

  .apk-verified-app {
    border-radius: 4px;
    background: rgba(61, 90, 133, 0.1) none repeat scroll 0% 0% / auto padding-box border-box;
    gap: 4px;
    padding: 5px 10px;
    -webkit-box-pack: center;
    justify-content: center;
    -webkit-box-align: center;
    align-items: center;
    display: flex;
    margin: 0px;
    font-weight: 400;
    box-sizing: border-box;
    font-family: Ponnala, sans-serif;
    }


  .apk-verify-txt {
    text-align: center;
    font-size: 14px;
    font-weight: 400;
    color: rgb(61, 90, 133);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .apk-about-txt {
    text-align: center;
    font-size: 14px;
    font-weight: 500;
    line-height: 35.833px;
    position: relative;
    color: rgb(61, 90, 133);
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }

  .apk-about-txt::after {
    content: "";
    border: solid #535d6c;
    margin-left: 5px;
    border-width: 0 1px 1px 0;
    display: inline-block;
    padding: 4px;
    margin-top: 4px;
    -webkit-transform: rotate(45deg) translateY(-50%);
    -ms-transform: rotate(45deg) translateY(-50%);
    -webkit-transform: rotate(320deg) translateY(-50%);
    -ms-transform: rotate(320deg) translateY(-50%);
    transform: rotate(320deg) translateY(-50%);
    position: absolute;
    top: calc(50% - 5px);
    -webkit-transition: 0.3s ease-in-out;
    -webkit-transition: 0.3s ease-in-out;
    transition: 0.3s ease-in-out;
  }

  .bstapk-close-button {
    position: absolute;
    top: 2px;
    right: 2px;
    border: 0px none rgb(0, 0, 0);
    padding: 21px;
    background: rgba(0, 0, 0, 0) none repeat scroll 0px 0px / auto padding-box border-box;
    cursor: pointer;
    box-sizing: border-box;
    margin: 0px;
  }


  .re-send-mail-container {
    display: none;
    justify-content: space-between;
  }

  .re-send-mail-message {
    gap: 4px;
    color: rgb(40, 48, 76);
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    align-items: center;
    font-family: Poppins;
    display: flex;
    text-align:left;
  }

  .re-send-mail-resend-link {
    color: rgb(9, 132, 221);
    font-family: Poppins;
    font-size: 12px;
    font-weight: 500;
    line-height: 20px;
    cursor: pointer;
    margin: 0px;
    box-sizing: border-box;
    padding: 0px;
  }
</style>
<section class="mobileapp-popup apk-download-popup">
  <div class="mobilapp-popup-container"><button type="button" class="qr-close-modal"></button>
    <div class="mobile-app-popup">
      <h2 class="popup-heading">اختر طريقة تثبيت تطبيق BlueStacks Mobile على جوالك</h2>
      <div class="apk-app-section">
        <div class="bluestacks-verified-container">
          <figure><img src="https://cdn-www.bluestacks.com/bs-images/bluestacks-mobile-logo.png" class="icon" /></figure>
          <p class="icon-title">BlueStacks Mobile</p>
        </div>
      </div>
      <div class="verify-section">
        <div class="qr-div">
          <p class="qr-div-heading">رمز الاستجابة السريعة</p>
          <figure class="qr-img-figure"><img src="https://cdn-www.bluestacks.com/web/mobile_app_QR.png" class="qr-img" /></figure>
          <p class="qr-desc">امسح رمز الاستجابة السريعة لتنزيل اللعبة على جوالك</p>
        </div>
        <p class="or-text">أو</p>
        <div class="email-verification-container">
          <p class="email-heading">بريد الالكتروني</p>
          <p class="email-subheading">احصل على رابط التحميل عبر البريد الإلكتروني</p>
          <div class="email-inputs">
            <input type="text" placeholder="أدخل عنوان بريدك الإلكتروني"  required="" class="email-input-field" value="" />
            <div class="terms-div">
              <input type="checkbox" name="terms-and-condition" class="terms-input" checked="" />
              <label for="terms-and-condition" class="terms-label">أوافق على <a class="terms-and-condition-end" href="/ar/terms-and-privacy.html"> الشروط والأحكام</a>
              </label>
            </div>
            <button class="send-mail" disabled="" type="submit">إرسال</button>

            <div class="re-send-mail-container">
              <p class="re-send-mail-message">
                <svg xmlns="http://www.w3.org/2000/svg" width="17" height="18" viewBox="0 0 17 18" fill="none">
                  <path d="M8.50033 0.666626C6.85215 0.666626 5.24099 1.15537 3.87058 2.07105C2.50017 2.98672 1.43206 4.28821 0.801333 5.81093C0.170603 7.33365 0.00557511 9.0092 0.327119 10.6257C0.648662 12.2422 1.44234 13.7271 2.60777 14.8925C3.77321 16.058 5.25807 16.8516 6.87458 17.1732C8.49109 17.4947 10.1666 17.3297 11.6894 16.699C13.2121 16.0682 14.5136 15.0001 15.4292 13.6297C16.3449 12.2593 16.8337 10.6481 16.8337 8.99996C16.8337 7.90561 16.6181 6.82198 16.1993 5.81093C15.7805 4.79988 15.1667 3.88122 14.3929 3.1074C13.6191 2.33358 12.7004 1.71975 11.6894 1.30096C10.6783 0.882174 9.59468 0.666626 8.50033 0.666626ZM13.2303 7.53079L8.23033 12.1141C8.07202 12.2593 7.86379 12.3377 7.64906 12.3331C7.43433 12.3284 7.22969 12.241 7.07783 12.0891L4.57783 9.58913C4.49824 9.51225 4.43475 9.4203 4.39108 9.31863C4.3474 9.21696 4.32442 9.10761 4.32345 8.99696C4.32249 8.88631 4.34358 8.77658 4.38548 8.67416C4.42738 8.57175 4.48926 8.47871 4.5675 8.40046C4.64574 8.32222 4.73879 8.26034 4.8412 8.21844C4.94361 8.17654 5.05335 8.15546 5.164 8.15642C5.27465 8.15738 5.384 8.18037 5.48567 8.22404C5.58734 8.26772 5.67929 8.3312 5.75616 8.41079L7.692 10.3466L12.1037 6.30246C12.2665 6.15305 12.4821 6.07448 12.7029 6.08401C12.9238 6.09354 13.1318 6.19041 13.2812 6.35329C13.4306 6.51618 13.5091 6.73175 13.4996 6.95257C13.4901 7.1734 13.3932 7.38139 13.2303 7.53079Z" fill="url(#paint0_linear_52386_6672)"></path>
                  <defs>
                    <linearGradient id="paint0_linear_52386_6672" x1="13.6932" y1="20.3889" x2="5.88514" y2="-9.42275" gradientUnits="userSpaceOnUse">
                      <stop stop-color="#27B835"></stop>
                      <stop offset="1" stop-color="#96CD20"></stop>
                    </linearGradient>
                  </defs>
                </svg>
                <span id="email-status" data-retry="تم إعادة إرسال البريد الإلكتروني!">تم إرسال البريد الإلكتروني </span>
              </p>
              <p class="re-send-mail-resend-link">إعادة إرسال الرابط</p>
            </div>
          </div>
          <p class="apk-email-desc">افتح البريد الإلكتروني على جوالك واضغط على رابط التحميل لبدء التنزيل</p>
        </div>
      </div>
      <div class="apk-info-section">
        <p class="apk-verified-app">
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 18 18" fill="none">
            <g clip-path="url(#clip0_52461_307)">
              <path d="M17.4688 4.50433C17.3894 6.45783 16.5661 12.5752 9.64407 17.1043C9.25231 17.3611 8.74669 17.3611 8.35493 17.1043C1.43554 12.5752 0.609694 6.45783 0.530269 4.50433C0.514371 4.12317 0.694353 3.75784 1.00145 3.52758L4.51411 0.930801C4.71522 0.78255 4.95614 0.703125 5.2076 0.703125H12.7915C13.0429 0.703125 13.2864 0.78255 13.485 0.930801L16.9976 3.52758C17.3046 3.75784 17.4847 4.12317 17.4688 4.50433Z" fill="#3D5A85"></path>
              <path d="M9.00017 16.238C8.98401 16.238 8.95972 16.2348 8.93619 16.2194C2.38976 11.9355 1.65743 6.13728 1.58892 4.46226C1.58776 4.43227 1.60456 4.40067 1.63274 4.37973L5.14371 1.78224C5.16232 1.76854 5.18455 1.76117 5.20769 1.76117H12.7928C12.8159 1.76117 12.8381 1.76854 12.857 1.78243L16.3679 4.37986C16.396 4.40073 16.4128 4.4324 16.4117 4.46251C16.3437 6.13735 15.613 11.9353 9.06441 16.2194C9.04089 16.2348 9.01646 16.238 9.00017 16.238Z" fill="#4F678C"></path>
              <path d="M8.19624 10.1739C7.96206 10.1739 7.73757 10.0889 7.57193 9.9374L5.73723 8.25992C5.57161 8.10852 5.47854 7.90316 5.47852 7.68902C5.47849 7.47488 5.57149 7.26949 5.73707 7.11805C5.90265 6.96661 6.12724 6.88152 6.36143 6.88149C6.59563 6.88147 6.82024 6.96651 6.98586 7.11791L8.19624 8.22481L11.7973 4.9321C11.8793 4.85713 11.9766 4.79767 12.0838 4.7571C12.1909 4.71654 12.3057 4.69567 12.4217 4.69568C12.5376 4.69569 12.6525 4.71659 12.7596 4.75718C12.8667 4.79777 12.9641 4.85725 13.046 4.93224C13.128 5.00723 13.1931 5.09624 13.2374 5.19421C13.2818 5.29217 13.3046 5.39717 13.3046 5.5032C13.3046 5.60924 13.2817 5.71423 13.2373 5.81218C13.193 5.91014 13.1279 5.99914 13.0459 6.07411L8.82056 9.93726C8.73858 10.0122 8.64125 10.0717 8.53413 10.1123C8.42701 10.1529 8.31219 10.1739 8.19624 10.1739Z" fill="white"></path>
            </g>
            <defs>
              <clipPath id="clip0_52461_307">
                <rect width="18" height="18" fill="white"></rect>
              </clipPath>
            </defs>
          </svg>
          <span class="apk-verify-txt">تطبيق مُوثّق</span>
        </p>
        <a href="/ar/mobileapp.html" class="apk-about-txt">ما هو BlueStacks Mobile</a>
      </div>
    </div>
  </div>
</section>


<script>
  const getLocaleReverse = {
    "": "en-US",
    "ar": "ar-AR",
    "de": "de-DE",
    "en": "en-US",
    "es": "es-ES",
    "fr": "fr-FR",
    "id": "id-ID",
    "it": "it-IT",
    "ja": "ja-JP",
    "ko": "ko-KR",
    "pl": "pl-PL",
    "pt-br": "pt-BR",
    "ru": "ru-RU",
    "th": "th-TH",
    "tr": "tr-TR",
    "vi": "vi-VN",
    "tw": "zh-TW"
  };
  document.addEventListener("DOMContentLoaded", function() {
    const closeBtn = document.querySelector(".qr-close-modal");
    const popup = document.querySelector(".apk-download-popup");

    if (closeBtn && popup) {
      closeBtn.addEventListener("click", function() {
        popup.style.display = "none";
      });
    }


    const emailInput = document.querySelector('.email-input-field');
    const sendButton = document.querySelector('.send-mail');
    const termsCheckbox = document.querySelector('.terms-div');
    const reSendMail = document.querySelector('.re-send-mail-container');
    const reSendMailLink = document.querySelector('.re-send-mail-resend-link');


    function validateEmail(email) {
      const re = /^(?:(?:[^<>()\[\]\\.,;:\s@"]+(?:\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@(?:(?:[a-zA-Z0-9-]+\.)+[a-zA-Z]{2,})$/;
      return re.test(email);
    }

    function toggleSendButton() {
      const isValidEmail = validateEmail(emailInput.value.trim());
      sendButton.disabled = !isValidEmail;
    }
    let mailSent= false;

    function sendMail() {
      const email = emailInput.value.trim();
      if (!validateEmail(email)) return;

      let mobileApkLinkBase = "https://cloud.bluestacks.com";
      mobileApkLinkBase = location.host.includes('bluestacks.com') ? mobileApkLinkBase : "https://bs3-cloud-engg.bstkinternal.net";
      fetch(`${mobileApkLinkBase}/get_mobile_link`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            email: email,
            locale: getLocaleReverse["en"] || "en-US"
          })
        })
        .then(response => {
          if (!response.ok) throw new Error('Network response was not ok');
          return response.json();
        })
        .then(data => {
          sendButton.style.display = "none";
          termsCheckbox.style.display = "none";
          reSendMail.style.display = "flex";
          if(mailSent){
            const emailStatus = document.getElementById("email-status");
            const retryValue = emailStatus.getAttribute("data-retry");
            emailStatus.innerText = retryValue;
          }
          mailSent = true;
        })
        .catch(error => {
          console.error('Error sending email:', error);
          alert('Failed to send email.');
        });
    }

    emailInput.addEventListener('input', toggleSendButton);

    sendButton.addEventListener('click', function(e) {
      window.sendAddExperimentData(
          'GetMobileAppLink',
          'DOWNLOAD_PAGE',
          'en',
          'send_app_link',
          emailInput.value.trim(),
          '',
          '',
          'download-page-dl-button-ar',
        );
      e.preventDefault();
      sendMail();
    });
    reSendMailLink.addEventListener('click', function(e) {
      window.sendAddExperimentData(
          'GetMobileAppLink',
          'DOWNLOAD_PAGE',
          'en',
          'resend_app_link',
          emailInput.value.trim(),
          '',
          '',
          'download-page-dl-button-ar',
        );
      e.preventDefault();
      sendMail();
    });
  });
</script>    <div class="main-container">
      <section class="cloud-games-section relative flex flex-col wfull">
        <div class="container container-full hero-banner-container">
        <div class="download-banner">
          <div>
            &nbsp;
          </div>
            <!-- <figure class="cloud-video">
              <img src="https://cdn-www.bluestacks.com/bs-images/Experience_ultimate_gameplay.png" height="100%" width="100%" alt="Download banner" />
            </figure> -->
            <div class="heading-layer">
              <h1 class="heading hero-heading">World's #1 Android Emulator</h1>
              <h3 class="hero-sub-heading sub-heading">Get the latest BlueStacks version</h3>
              <section class="cta-wrapper">
                <div class="cta-elements flex">
                                    <div class="mac-ctas">
                      <a class="browser-cta cta-layout bsx-link hide" target="_blank" data-utm="download-page-dl-cloud-button-ar" data-btn-utm="download-page-dl-cloud-button-ar" data-lang="ar" data-comingsoon="قريبا" data-mac-cta-str=""  >
    <span class="browser-icon svg-icon relative">
      <svg class="browser" width="17" height="17" viewBox="0 0 17 17">
        <use xlink:href="#cloud-icon" fill="none" /></svg>
    </span>
    <span class="play-so-button flex flex-col">
      <span class='light-str'>Powered by <svg width='53' height='13' class='light-str-svg' viewBox='0 0 53 14'><use xlink:href='#powered_nowgg' fill='none' /></svg></span><span class='main-str'>Play instantly on Cloud</span>    </span>
  </a>
                  </div>
                    <a style="" data-extra='null' data-utm-mac='homepage-dl-button' data-app-mac='' data-win-text-alignment="" data-mac-text-color="" data-win-text-color="" data-win-bg-color="" data-mac-bg-color="" data-mac-border-color="" data-win-bg-img=""  data-mac-bg-img="" data-mac-text-alignment="" data-mac-text-alignment-with-pipe=text-align:;width:100% href="https://www.bluestacks.com/download.html?utm_campaign=download-page-en" class=" download-bs avocado-green bigger  download-button homepage-download-button activated cta-layout download-bsx download-bs10 non-us-download-btn " data-utm="bsx-install-button-download-page-ar" data-animation="إعادة التحميل"  data-lang="en" data-mac-attr="&lt;span class=&quot;small-txt&quot;&gt;قريبا&lt;/span&gt;BlueStacks 10&lt;/span&gt;" data-mac-attr-is-default="">

            <div class="button-copy button-copy flex align-center wfull">
          <span class="download-icon svg-icon relative" >
        <img src="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" height="54" width="54" alt="BlueStacks Logo" class="img-responsive new-logo-icon" />      </span>
      <span style="text-align:;width:100%" class="so-button flex flex-col" data-test="&lt;span class=&quot;small-txt&quot;&gt;BlueStacks&lt;/span&gt;تحميل&lt;/span&gt;" data-macos="&lt;span class=&quot;small-txt&quot;&gt;قريبا&lt;/span&gt;BlueStacks 10&lt;/span&gt;"  data-mac-text-alignment="">
      <span class="small-txt">BlueStacks</span>تحميل</span>        <span class="version-info">&nbsp;</span>
      
            </div>
      </a>

                    <a style="" data-extra='null' data-utm-mac='homepage-dl-button' data-app-mac='' data-win-text-alignment="" data-mac-text-color="" data-win-text-color="" data-win-bg-color="" data-mac-bg-color="" data-mac-border-color="" data-win-bg-img=""  data-mac-bg-img="" data-mac-text-alignment="" data-mac-text-alignment-with-pipe= href="https://www.bluestacks.com/download.html?utm_campaign=download-page-en" class=" download-bs avocado-green bigger  download-button homepage-download-button activated cta-layout download-bsx download-bs5 " data-utm="download-page-dl-button-ar" data-animation="إعادة التحميل"  data-lang="en" data-mac-attr=" قم بتنزيل BlueStacks " data-mac-attr-is-default="">

            <div class="button-copy button-copy flex align-center wfull">
          <span class="download-icon svg-icon relative" >
        <figure class="download-icon" style="height:54px;"><img src="https://cdn-www.bluestacks.com/bs-images/download-generic-img.png" alt="win-logo" height="54" width="54" class="non-mac" /><img src="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" height="54" width="54" class="mac-visible hide" alt="mac-logo" /></figure>      </span>
      <span style="text-align:;width:100%" class="so-button flex flex-col" data-test="&lt;span class=&quot;small-txt&quot;&gt;BlueStacks 5&lt;/span&gt;تحميل&lt;/span&gt;" data-macos=" قم بتنزيل BlueStacks "  data-mac-text-alignment="">
      <span class="small-txt">BlueStacks 5</span>تحميل</span>        <span class="version-info">&nbsp;</span>
      
            </div>
      </a>

                                    <div class="swinging-spider" id="swinging-spider"></div>
                </div>
              </section>
              <a class="download-other-versions" href="#all-versions" onclick="smoothScrollToAllVersions(event)">
              انظر لجميع الإصدارات
<svg xmlns="http://www.w3.org/2000/svg" width="13" height="12" viewBox="0 0 13 12" fill="none">
              <path d="M4.5 10L8.5 6L4.5 2" stroke="white" stroke-opacity="0.9"/>
              </svg>
            </a>
            </div>
          </div>
        </div>
      </section>
      <section class="common-bg">

      <section class=" container top-games-section" id="top-games-section">
        <div class="header">
          <h2 class="title">الألعاب الشعبية
 🔥</h2>
          <a href="/apps/popular.html" class="view-all">مشاهدة الكلعرض الكل</a>
        </div>
        
        <div class="games-grid">
          
        </div>
      </section>



        <section class="all-versions" id="all-versions">
          <div class="container">
            <h2 class="heading">Download a specific version based on your need</h2>
            <ul class="download-version-list">
              <li class="version-li version-li-1">
                <div class="l-section">
                  <figure class="bs-version-logo">
                    <img class="lazy" data-src="https://cdn-www.bluestacks.com/bs-images/BS5_img1.png" height="224"
                      width="189" alt="bs-logo" />
                  </figure>
                  <h5 class="v-heading">BlueStacks 5 </h5>
                </div>
                <div class="r-section">
                  <div class="section-block section-block-1">
                    <div class="heading-group">
                      <div class="l-side">
                        <svg class="svg-icon win-icon" width="19" height="19" viewBox="0 0 19 19" data-fill="#394566">
                          <use xlink:href="#windows-white-icon" fill="#fff" />
                        </svg>
                        <span class="title">For Windows <span class="subtitle">(Optimized for Windows 11)</span></span>
                      </div>
                    </div>
                    <ul class="versions-list">
                      <li class="version-item version-item-1">
                        <span>Nougat 32-bit</span>
                        <a class="download-bs5 download-bs download-button pc-visible download-page-other-builds"
                          data-animation="إعادة التحميل"
                          data-utm="nxt-bs5-n32_button_download_page-ar" data-platform="win"
                          data-package="nxt_n32">Download</a>
                      </li>
                      <li class="version-item version-item-2">
                        <span>Nougat 64-bit </span>
                        <a class="download-bs5 download-bs download-button pc-visible download-page-other-builds"
                          data-animation="إعادة التحميل" data-package="nxt_n64"
                          data-utm="nxt-bs5-n64_button_download_page-ar"
                          data-platform="win">Download</a>
                      </li>
                      <li class="version-item version-item-3">
                        <span>Pie 64-bit <span class="subtext"></span></span>
                        <a class="download-bs5 download-bs download-button pc-visible download-page-other-builds"
                          data-animation="إعادة التحميل" data-package="nxt_pie64"
                          data-utm="nxt-bs5-pie64_button_download_page-ar"
                          data-platform="win">Download</a>
                      </li>
                      <li class="version-item version-item-4">
                        <a href="/ar/android-11.html"><span>Android 11 64-bit</span></a>
                        <a class="download-bs5 download-bs download-button pc-visible download-page-other-builds"
                          data-animation="إعادة التحميل" data-package="nxt_rvc64"
                          data-utm="nxt-rvc64-button-download-page-ar"
                          data-platform="win">Download</a>
                      </li>
                       <li class="version-item version-item-5">
                        <span>Android 13 64-bit (Beta)</span>
                        <a class="download-bs5 download-bs download-button pc-visible download-page-other-builds"
                          data-animation="إعادة التحميل" data-package="nxt_tiramisu64"
                          data-utm="nxt_tiramisu64-button-download-page-ar"
                          data-platform="win">Download</a>
                      </li>
                    </ul>
                  </div>
                  <div class="section-block section-block-2">
                    <div class="heading-group">
                      <div class="l-side">
                        <svg class="svg-icon mac-icon" width="18" height="25" viewBox="0 0 18 25">
                          <use xlink:href="#mac-white-icon" fill="#fff" />
                        </svg>
                        <span class="title">For macOS</span>
                      </div>
                      <a class="download-bs download-button pc-visible download-page-other-builds download-button-mac"
                      data-animation="إعادة التحميل"
                      data-utm="product-mac-air-download_page-ar" data-platform="mac"
                      data-bs-version="bs4">Download</a>
                      <!-- <a class="download-not-available pc-visible">
                        <div class="btn-txt">Coming soon</div>
                      </a> -->
                    </div>
                  </div>
                  <p class="mob-note mob-visible">Note: BlueStacks 5 is currently available for PC only</p>
                </div>
              </li>
              <li class="version-li version-li-2">
                <div class="l-section">
                  <figure class="bs-version-logo">
                    <img class="lazy" data-src="https://cdn-www.bluestacks.com/bs-images/BS4_img.png" height="190"
                      width="189" alt="bs-logo" />
                  </figure>
                  <h5 class="v-heading">BlueStacks 4 </h5>
                </div>
                <div class="r-section">
                  <div class="section-block section-block-1">
                    <div class="heading-group">
                      <div class="l-side">
                        <svg class="svg-icon win-icon" width="19" height="19" viewBox="0 0 19 19" data-fill="#394566">
                          <use xlink:href="#windows-white-icon" fill="#fff" />
                        </svg>
                        <span class="title">For Windows</span>
                      </div>
                    </div>
                    <ul class="versions-list">
                      <li class="version-item version-item-1">
                        <span>Nougat 32-bit</span>
                        <a class="download-bs download-button pc-visible download-page-other-builds"
                          data-animation="إعادة التحميل"
                          data-utm="bgp-bs4-n32_button_download_page-ar" data-platform="win"
                          data-bs-version="bs4" data-package="bgp_n32">Download</a>
                      </li>
                      <li class="version-item version-item-2">
                        <span>Nougat 64-bit</span>
                        <a class="download-bs download-button pc-visible download-page-other-builds"
                          data-animation="إعادة التحميل" data-package="bgp_n64"
                          data-utm="bgp-bs4-n64_button_download_page-ar" data-platform="win"
                          data-bs-version="bs4">Download</a>
                      </li>
                      <li class="version-item version-item-3">
                        <span>Hyper-V, Nougat 64-bit</span>
                        <a class="download-bs download-button pc-visible download-page-other-builds"
                          data-animation="إعادة التحميل"
                          data-utm="hyperv-n64-button_download_page-ar" data-platform="win"
                          data-bs-version="bs4">Download</a>
                      </li>
                    </ul>
                  </div>
                  <div class="section-block section-block-2 hide-mac">
                    <div class="heading-group">
                      <div class="l-side">
                        <svg class="svg-icon mac-icon" width="18" height="25" viewBox="0 0 18 25">
                          <use xlink:href="#mac-white-icon" fill="#fff" />
                        </svg>
                        <span class="title">For macOS</span>
                      </div>
                      <a class="download-bs download-button pc-visible download-page-other-builds"
                        data-animation="إعادة التحميل"
                        data-utm="bgp-mac-bs4-button_download_page-ar" data-platform="mac"
                        data-bs-version="bs4">Download</a>
                    </div>
                  </div>
                  <p class="mob-note mob-visible">Note: BlueStacks 4 is currently available for PC only</p>
                </div>
              </li>
            </ul>
          </div>
          <div class="pumpkin-animation"></div>
        </section>
        <section class="faq-section">
          <div class="container">
            <h2 class="faq-heading">
              FAQs
            </h2>
            <ul class="faq-list">
              <li class="faq-item faq-item-2">
                <p class="faq-q">
                  How can I download BlueStacks 5?
                </p>
                <span class="toggle-arrow"></span>
                <p class="faq-a">
                  You can download BlueStacks 5 from the links on this page or from our website bluestacks.com. <br />
                  Download the .exe file and complete the setup within minutes to start playing your favorite mobile
                  games
                  on PC.
                </p>
              </li>
              <li class="faq-item faq-item-3">
                <p class="faq-q">
                  How to install BlueStacks?
                </p>
                <span class="toggle-arrow"></span>
                <div class="faq-a">
                  <p>To install BlueStacks on your PC or Mac simply do the following</p>
                  <ul class="step-list">
                    <li class="step-1">Download the .exe/.dmg file from bluestacks.com</li>
                    <li class="step-2">Once the download is complete, just follow the instructions as they appear</li>
                    <li class="step-3">After the first boot is complete, sign in with your Gmail account or add it later
                    </li>
                  </ul>
                  <p>In case you don’t have a Gmail account, you can sign in with another one by following a few simple
                    steps.</p>
                </div>
              </li>
              <li class="faq-item faq-item-4">
                <p class="faq-q">
                  How to update BlueStacks?
                </p>
                <span class="toggle-arrow"></span>
                <div class="faq-a">
                  <p>Follow the steps below to update BlueStacks on your <strong>Windows</strong> PC</p>
                  <ul class="step-list">
                    <li class="step-21">Open the App Player, click on the gear icon on the side toolbar for Settings
                    </li>
                    <li class="step-22">Click on ‘About’ on the Settings window</li>
                    <li class="step-23">Click on ‘Check for update’</li>
                    <li class="step-24">If an update is available, you can download and install it by clicking on
                      ‘Download now’ </li>
                  </ul>
                  <br />
                  <p class="step-list-p">In case you are using a <strong>Mac</strong>, do the following</p>
                  <ul class="step-list">
                    <li class="step-2-1">Open the App Player, click on the top left corner of your screen</li>
                    <li class="step-2-2">Click on ‘Check for Updates’</li>
                  </ul>
                  <br />
                  <p>To update to BlueStacks 5, simply download it from this page or visit bluestacks.com. Please note,
                    we
                    are presently working on making BlueStacks 5 available for macOS</p>
                </div>
              </li>
              <li class="faq-item faq-item-5">
                <p class="faq-q">
                  Is BlueStacks free to download?
                </p>
                <span class="toggle-arrow"></span>
                <p class="faq-a">
                  Yes, the Android Emulator is free to download. Although there is a paid version as well, however, that
                  doesn’t affect the gaming experience in any way.
                </p>
              </li>
              <li class="faq-item faq-item-6">
                <p class="faq-q">
                  Why does BlueStacks need me to log in to my Google account?
                </p>
                <span class="toggle-arrow"></span>
                <p class="faq-a">
                  Google account login is required to help install games from Google Play Store and for troubleshooting
                  issues just the way it is on your phone.
                </p>
              </li>
            </ul>
          </div>
          <div class="pumpkin-animation-faq"></div>
        </section>
      </section>
    </div>
    <div class="halowin-goht-img">
      <img src="https://cdn-www.bluestacks.com/bs-images/Ghost_Default.gif" alt="Ghost">
    </div>
    <footer>
    <section class="unlock-banner-section fade-in">
    <img height="220" width="100%" data-src="https://cdn-www.bluestacks.com/bs-images/footer-bsx-img.png" class="lazy bg-image">
    <div class="bg-tint"></div>
    <div class="container">
      <div class="unlock-content">
        <div class="heading">
          <h2>
          
            اكتشف طريقة لعب رائعة ومكافآت حصرية على الكمبيوتر العادي أو جهاز ماك.          </h2>
        </div>
                <div class="cta-elements flex">
            <a style="" data-extra='null' data-utm-mac='homepage-dl-button-en' data-app-mac='' data-win-text-alignment="" data-mac-text-color="" data-win-text-color="" data-win-bg-color="" data-mac-bg-color="" data-mac-border-color="" data-win-bg-img=""  data-mac-bg-img="" data-mac-text-alignment="" data-mac-text-alignment-with-pipe=text-align:;width:100% href="https://www.bluestacks.com/download.html?utm_campaign=download-page-en" class=" download-bs avocado-green bigger  download-button homepage-download-button activated cta-layout download-bsx download-bs10 us-download-btn " data-utm="bsx-install-button-bottom-us-exp-en" data-animation="إعادة التحميل"  data-lang="en" data-mac-attr="&lt;span class=&quot;small-txt&quot;&gt;قريبا&lt;/span&gt;BlueStacks 10&lt;/span&gt;" data-mac-attr-is-default="">

            <div class="button-copy button-copy flex align-center wfull">
          <span class="download-icon svg-icon relative" >
        <img src="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" height="54" width="54" alt="BlueStacks Logo" class="img-responsive new-logo-icon" />      </span>
      <span style="text-align:;width:100%" class="so-button flex flex-col" data-test="&lt;span class=&quot;small-txt&quot;&gt;BlueStacks&lt;/span&gt;تحميل&lt;/span&gt;" data-macos="&lt;span class=&quot;small-txt&quot;&gt;قريبا&lt;/span&gt;BlueStacks 10&lt;/span&gt;"  data-mac-text-alignment="">
      <span class="small-txt">BlueStacks</span>تحميل</span>        <span class="version-info">&nbsp;</span>
      
            </div>
      </a>

  <a style="" data-extra='null' data-utm-mac='homepage-dl-button-en' data-app-mac='' data-win-text-alignment="" data-mac-text-color="" data-win-text-color="" data-win-bg-color="" data-mac-bg-color="" data-mac-border-color="" data-win-bg-img=""  data-mac-bg-img="" data-mac-text-alignment="" data-mac-text-alignment-with-pipe=text-align:;width:100% href="https://www.bluestacks.com/download.html?utm_campaign=download-page-en" class=" download-bs avocado-green bigger  download-button homepage-download-button activated cta-layout download-bsx download-bs10 non-us-download-btn " data-utm="bsx-install-button-bottom-en" data-animation="إعادة التحميل"  data-lang="en" data-mac-attr="&lt;span class=&quot;small-txt&quot;&gt;قريبا&lt;/span&gt;BlueStacks 10&lt;/span&gt;" data-mac-attr-is-default="">

            <div class="button-copy button-copy flex align-center wfull">
          <span class="download-icon svg-icon relative" >
        <img src="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" height="54" width="54" alt="BlueStacks Logo" class="img-responsive new-logo-icon" />      </span>
      <span style="text-align:;width:100%" class="so-button flex flex-col" data-test="&lt;span class=&quot;small-txt&quot;&gt;BlueStacks&lt;/span&gt;تحميل&lt;/span&gt;" data-macos="&lt;span class=&quot;small-txt&quot;&gt;قريبا&lt;/span&gt;BlueStacks 10&lt;/span&gt;"  data-mac-text-alignment="">
      <span class="small-txt">BlueStacks</span>تحميل</span>        <span class="version-info">&nbsp;</span>
      
            </div>
      </a>

            <a style="" data-extra='null' data-utm-mac='homepage-dl-button-en' data-app-mac='' data-win-text-alignment="" data-mac-text-color="" data-win-text-color="" data-win-bg-color="" data-mac-bg-color="" data-mac-border-color="" data-win-bg-img=""  data-mac-bg-img="" data-mac-text-alignment="" data-mac-text-alignment-with-pipe= href="https://www.bluestacks.com/download.html?utm_campaign=download-page-en" class=" download-bs avocado-green bigger  download-button homepage-download-button activated cta-layout download-bsx download-bs5 " data-utm="download-page-dl-button-ar-bottom-en" data-animation="إعادة التحميل"  data-lang="en" data-mac-attr=" قم بتنزيل BlueStacks " data-mac-attr-is-default="">

            <div class="button-copy button-copy flex align-center wfull">
          <span class="download-icon svg-icon relative" >
        <figure class="download-icon" style="height:54px;"><img src="https://cdn-www.bluestacks.com/bs-images/download-generic-img.png" height="54" width="54" class="non-mac" /><img src="https://cdn-www.bluestacks.com/bs-images/logo-icon.png" height="54" width="54" class="mac-visible hide" /></figure>      </span>
      <span style="text-align:;width:100%" class="so-button flex flex-col" data-test="&lt;span class=&quot;small-txt&quot;&gt;BlueStacks 5&lt;/span&gt;تحميل&lt;/span&gt;" data-macos=" قم بتنزيل BlueStacks "  data-mac-text-alignment="">
      <span class="small-txt">BlueStacks 5</span>تحميل</span>        <span class="version-info">&nbsp;</span>
      
            </div>
      </a>

  <a class="browser-cta cta-layout bsx-link hide" target="_blank" data-utm="download-page-dl-cloud-button-en" data-btn-utm="download-page-dl-cloud-button-en" data-lang="en" data-comingsoon="قريبا" data-mac-cta-str=""  >
    <span class="browser-icon svg-icon relative">
      <svg class="browser" width="17" height="17" viewBox="0 0 17 17">
        <use xlink:href="#cloud-icon" fill="none" /></svg>
    </span>
    <span class="play-so-button flex flex-col">
      <span class='light-str'>Powered by <svg width='53' height='13' class='light-str-svg' viewBox='0 0 53 14'><use xlink:href='#powered_nowgg' fill='none' /></svg></span><span class='main-str'>Play instantly on Cloud</span>    </span>
  </a>
        </div>
        
      </div>
    </div>
  </section>
    <section class="footer-wrapper">
    <div class="container">
      <div class="logo-section">
        <a href="https://www.bluestacks.com/ar/index.html" class="footer-logo-lnk">
          <img class="lazy" height="32" width="32" data-src="https://cdn-www.bluestacks.com/bs-images/logo-icon.png"
            alt="BlueStacks">
          <div class="img-loading">
  <svg class="svg-icon" viewBox="0 0 100 100" height="100" width="100">
    <use xlink:href="#loader" />
  </svg>
  <div class="shine"></div>
</div>        </a>
                <ul class="breadcrumb-list">
                    <li>
                        <a href="/">
              الرئيسية            </a>
            
          </li>
                    <li>
                        Download            
          </li>
                  </ul>
              </div>
      <div class="menu-section">
        <div id="footer-menu" class="games-section"><ul id="menu-homepage-v2-footer-menu-arabic-homepage-v2-footer-menu" class="games-item-list"><li id="menu-item-2571700" class="no-hover menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-2571700"><a>منتجات</a>
<ul class="sub-menu">
	<li id="menu-item-3837227" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-3837227"><a href="/ar/mac">BlueStacks Mac</a></li>
	<li id="menu-item-2031534" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2031534"><a href="https://www.bluestacks.com/ar/bluestacks-5.html">BlueStacks 5</a></li>
	<li id="menu-item-3809249" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3809249"><a href="https://www.bluestacks.com/ar/products/store.html">متجر</a></li>
	<li id="menu-item-1650392" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-1650392"><a href="https://www.bluestacks.com/ar/apps.html">ألعاب</a>
	<ul class="sub-menu">
		<li id="menu-item-2637310" class="mob-visible menu-item menu-item-type-post_type menu-item-object-page menu-item-2637310"><a href="https://www.bluestacks.com/ar/apps.html">جميع الالعاب</a></li>
		<li id="menu-item-2445912" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2445912"><a href="https://www.bluestacks.com/ar/top-games.html">أفضل الألعاب</a></li>
		<li id="menu-item-1650394" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650394"><a href="https://www.bluestacks.com/ar/apps/action.html">ألعاب حركة</a></li>
		<li id="menu-item-1650396" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650396"><a href="https://www.bluestacks.com/ar/apps/adventure.html">مغامرات</a></li>
		<li id="menu-item-1650482" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650482"><a href="https://www.bluestacks.com/ar/apps/arcade.html">ألعاب كلاسيكية</a></li>
		<li id="menu-item-1650484" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650484"><a href="https://www.bluestacks.com/ar/apps/puzzle.html">ألغاز</a></li>
		<li id="menu-item-1650486" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650486"><a href="https://www.bluestacks.com/ar/apps/racing.html">سباق</a></li>
		<li id="menu-item-1650488" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650488"><a href="https://www.bluestacks.com/ar/apps/role-playing.html">تقمص الأدوار</a></li>
		<li id="menu-item-1650490" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650490"><a href="https://www.bluestacks.com/ar/apps/simulation.html">المحاكاة</a></li>
		<li id="menu-item-1650492" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650492"><a href="https://www.bluestacks.com/ar/apps/sports.html">رياضة</a></li>
		<li id="menu-item-1650498" class="menu-item menu-item-type-taxonomy menu-item-object-app_category menu-item-1650498"><a href="https://www.bluestacks.com/ar/apps/strategy.html">الإستراتيجية</a></li>
	</ul>
</li>
</ul>
</li>
<li id="menu-item-1674840" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-has-children menu-item-1674840"><a href="https://www.bluestacks.com/ar/features.html">المميزات</a>
<ul class="sub-menu">
	<li id="menu-item-2637304" class="mob-visible menu-item menu-item-type-post_type menu-item-object-page menu-item-2637304"><a href="https://www.bluestacks.com/ar/features.html">كل المميزات</a></li>
	<li id="menu-item-1674842" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674842"><a href="https://www.bluestacks.com/ar/features/game-controls.html">أدوات تحكم اللعبة</a></li>
	<li id="menu-item-1674858" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674858"><a href="https://www.bluestacks.com/ar/features/shooting-mode.html">وضع إطلاق النار</a></li>
	<li id="menu-item-1674856" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674856"><a href="https://www.bluestacks.com/ar/features/moba-mode.html">وضع موبا</a></li>
	<li id="menu-item-1674864" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674864"><a href="https://www.bluestacks.com/ar/features/multi-instance.html">متعددة مثيل</a></li>
	<li id="menu-item-1674866" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674866"><a href="https://www.bluestacks.com/ar/features/multi-instance-sync.html">متعدد مثيل المزامنة</a></li>
	<li id="menu-item-1674860" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674860"><a href="https://www.bluestacks.com/ar/features/macros.html">وحدات الماكرو</a></li>
	<li id="menu-item-1674848" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674848"><a href="https://www.bluestacks.com/ar/features/eco-mode.html">نمط الخفيف</a></li>
	<li id="menu-item-1674868" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674868"><a href="https://www.bluestacks.com/ar/features/real-time-translation.html">الترجمة في الوقت الحقيقي</a></li>
	<li id="menu-item-1674852" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674852"><a href="https://www.bluestacks.com/ar/features/smart-controls.html">الضوابط الذكية</a></li>
	<li id="menu-item-1674844" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674844"><a href="https://www.bluestacks.com/ar/features/rerolling.html">إعادة المتداول</a></li>
	<li id="menu-item-2847032" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2847032"><a href="https://www.bluestacks.com/ar/features/performance-modes.html">أوضاع الأداء</a></li>
	<li id="menu-item-2847036" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2847036"><a href="https://www.bluestacks.com/ar/features/trim-memory.html">تقليم الذاكرة</a></li>
	<li id="menu-item-1674850" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674850"><a href="https://www.bluestacks.com/ar/features/script.html">النصي</a></li>
	<li id="menu-item-1674862" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674862"><a href="https://www.bluestacks.com/ar/features/high-fps.html">FPS عالية</a></li>
	<li id="menu-item-1674854" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674854"><a href="https://www.bluestacks.com/ar/features/high-definition.html">الرسومات عالية الوضوح</a></li>
	<li id="menu-item-1674846" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1674846"><a href="https://www.bluestacks.com/ar/features/utility.html">ميزات الأداة المساعدة</a></li>
	<li id="menu-item-1868806" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1868806"><a href="https://www.bluestacks.com/ar/features/utc-converter.html">محول UTC</a></li>
</ul>
</li>
<li id="menu-item-3411341" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-3411341"><a>معلومات‎</a>
<ul class="sub-menu">
	<li id="menu-item-3411878" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-3411878"><a href="https://www.bluestacks.com/ar/about-us.html">معلومات عنا‎</a></li>
	<li id="menu-item-1650548" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1650548"><a href="https://www.bluestacks.com/ar/about-us/contact-us.html">اتصل بنا</a></li>
	<li id="menu-item-1650528" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1650528"><a href="https://www.bluestacks.com/ar/work-at-bluestacks.html">إعمل في بلوستاكس</a></li>
	<li id="menu-item-2099014" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2099014"><a href="https://www.bluestacks.com/ar/press.html">الصحافة</a></li>
	<li id="menu-item-1971090" class="no-hover menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-1971090"><a>معرفة</a>
	<ul class="sub-menu">
		<li id="menu-item-1971096" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-1971096"><a href="https://www.bluestacks.com/ar/using-android-on-pc.html">العب ألعاب Android على جهاز الكمبيوتر</a></li>
		<li id="menu-item-2078182" class="menu-item menu-item-type-post_type menu-item-object-page menu-item-2078182"><a href="https://www.bluestacks.com/ar/android-emulator.html">ما هو محاكي Android</a></li>
	</ul>
</li>
</ul>
</li>
<li id="menu-item-1650544" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-has-children menu-item-1650544"><a href="https://support.bluestacks.com/hc/ar">الدعم</a>
<ul class="sub-menu">
	<li id="menu-item-1650546" class="menu-item menu-item-type-custom menu-item-object-custom menu-item-1650546"><a href="https://support.bluestacks.com/hc/ar/articles/360056960211-%D9%85%D9%84%D8%A7%D8%AD%D8%B8%D8%A7%D8%AA-%D8%A7%D9%84%D8%A5%D8%B5%D8%AF%D8%A7%D8%B1-BlueStacks-5-">تحديث</a></li>
</ul>
</li>
  <li class="download-social-share">
    <div class="follow-us-on">
      <p class="follow-us-heading">اتبعنا</p>
      <ul class="socail-ftr flex y social-share-links">
        
            <li>
              <a href="https://discord.gg/bluestacks" class="discord" target="_blank" rel="noopener">
                <svg class="dflt" viewBox="0 0 512 512" xmlns="http://www.w3.org/2000/svg">
                  <path d="M372.4 168.7s-33.3-26.1-72.7-29.1l-3.5 7.1c35.6 8.7 51.9 21.2 69 36.5-29.4-15-58.5-29.1-109.1-29.1s-79.7 14.1-109.1 29.1c17.1-15.3 36.5-29.2 69-36.5l-3.5-7.1c-41.3 3.9-72.7 29.1-72.7 29.1s-37.2 54-43.6 160c37.5 43.3 94.5 43.6 94.5 43.6l11.9-15.9c-20.2-7-43.1-19.6-62.8-42.3 23.5 17.8 59.1 36.4 116.4 36.4S349 332 372.6 314.1c-19.7 22.7-42.6 35.3-62.8 42.3l11.9 15.9s57-.3 94.5-43.6c-6.6-106-43.8-160-43.8-160zM208.7 299.6c-14.1 0-25.5-13-25.5-29.1s11.4-29.1 25.5-29.1 25.5 13 25.5 29.1-11.4 29.1-25.5 29.1zm94.6 0c-14.1 0-25.5-13-25.5-29.1s11.4-29.1 25.5-29.1 25.5 13 25.5 29.1-11.5 29.1-25.5 29.1z" fill="#FFF" />
                </svg>
              </a>
            </li>

                                <li>
              <a target="_blank" rel="noopener" href="https://www.reddit.com/r/BlueStacks/" class="Reddit">
                <svg class="dflt" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 226.63 226.63" xmlns:v="https://vecta.io/nano">
                  <rect y="-.04" width="226.63" height="226.63" rx="37.2" fill="#fe4500" />
                  <path d="M214.55 109.5c-4.02-15.62-22.31-22.01-35.2-12.3-.92.69-1.54 1.96-3.22.81-16.8-11.51-35.63-16.81-55.78-17.89-1.92-.1-2.59-.52-2.13-2.62l9.05-42.59c.49-2.39 1.3-2.8 3.53-2.3l27.12 5.78c2.15.44 3.25 1.19 3.74 3.61 4.02 17.25 28.08 15.69 31.02-.89 2.58-16.83-18.99-25.87-28.8-11.57-.95 1.37-1.7 1.73-3.36 1.36l-32.96-7.01c-4.78-1.01-6.05-.2-7.06 4.57l-10 47.57c-.63 3.18-1.84 4-4.93 4.16-19.84 1.03-38.37 6.36-54.96 17.58-1.26.85-1.94.65-3.01-.22-12.8-10.9-33.5-2.72-35.93 13.47-1.48 9.52 2.52 18.21 10.83 23.07 1.56.91 1.92 1.82 1.77 3.52-3.24 45 50.98 67.25 88.87 66.48 38.39.73 92.39-21.22 89.06-66.84-.17-1.82.73-2.39 1.97-3.15 8.7-5.3 12.81-15.11 10.36-24.61zM62.3 130.97c-.06-8.83 6.97-16.01 15.73-16.07 21.13.73 21.43 30.78.29 31.81-8.87.07-15.96-6.89-16.02-15.74zm88.03 42.67c-9.41 8.72-23.97 10.31-35.75 11.15-14.53-.68-28.4-2.07-38.89-11.64-1.8-1.81-1.94-4.24-.36-5.87 1.72-1.76 3.79-2.02 5.6-.39 13.69 12.62 50.1 12.64 64.05.5 2.33-2.05 4.65-1.98 6.31-.05 1.57 1.83 1.23 4.31-.96 6.29zm-2.11-26.93c-8.81-.04-15.88-7.17-15.85-15.97.99-21.05 31.05-21.04 31.81.16-.04 8.84-7.11 15.84-15.96 15.8z" fill="#fefefe" />
                </svg>
              </a>
            </li>
                                <li>
              <a target="_blank" rel="noopener" href="https://www.facebook.com/BlueStacksInc" class="facebook">
                <svg class="dflt" viewBox="0 0 112.196 112.196">
                  <path fill="#fff" d="M70.201 58.294h-10.01v36.672H45.025V58.294h-7.213V45.406h7.213v-8.34c0-5.964 2.833-15.303 15.301-15.303l11.234.047v12.51h-8.151c-1.337 0-3.217.668-3.217 3.513v7.585h11.334l-1.325 12.876z" />
                </svg>
              </a>
            </li>
                    <li>
            <a target="_blank" rel="noopener" href="https://twitter.com/bluestacksinc" class="twitter">
              <img src="https://cdn-www.bluestacks.com/bs-images/twitter-social-logo.png" width="32" height="32" />
            </a>
          </li>
                      </ul>
    </div>
    <div class="youtube-subscribe">
      <p class="subscribe-us-heading">الإشتراك</p>
              <a href="https://www.youtube.com/channel/UC9eB0Ysv4UcKgjEy47zcZ5A?sub_confirmation=1" target="_blank">
          <div class="icon-with-text">
            <svg class="dflt" id="youtube_icon" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M26.5055 9.5036C26.2288 8.52465 25.4182 7.753 24.3903 7.48939C22.5122 7 14.9998 7 14.9998 7C14.9998 7 7.48753 7 5.60943 7.47073C4.60125 7.73417 3.77087 8.52482 3.4942 9.5036C3 11.2917 3 15 3 15C3 15 3 18.727 3.4942 20.4964C3.77105 21.4752 4.58148 22.247 5.60961 22.5106C7.50731 23 14.9999 23 14.9999 23C14.9999 23 22.5122 23 24.3903 22.5293C25.4184 22.2658 26.2288 21.494 26.5057 20.5152C26.9999 18.727 26.9999 15.0188 26.9999 15.0188C26.9999 15.0188 27.0197 11.2917 26.5055 9.5036Z" fill="#FFFFFF" />
              <path d="M12.6079 18.426L18.8549 15.0001L12.6079 11.5742V18.426Z" fill="#FF0000" />
            </svg>
            <span class="text-youtube">YouTube</span>
          </div>
                    <span class="subscriber-count">138K</span>
        </a>
        </li>
  </ul>
  </div>
      </div>
      <div class="copyright-section">
        <p>
          © 2025 اسم وشعار BlueStacks هما علامتان تجاريتان مسجلتان لشركة now.gg, inc        </p>
        <p class="footer-lnks">
          <span><a
              href="https://www.bluestacks.com/ar/terms-and-privacy.html">الشروط والخصوصية</a></span>
          | <span><a
              href="https://www.bluestacks.com/copyright-dispute-policy.html">حقوق الطبع والنشر سياسة النزاع</a></span>
          | <span><a
              href="https://www.bluestacks.com/ar/terms-and-privacy.html#eu-privacy">خصوصية الاتحاد الأوروبي</a></span>
                  </p>
      </div>
    </div>
  </section>
</footer>
<!--<script src="https://apis.google.com/js/platform.js"></script>-->
<script>
  window.pageInfo = {
    pageId: 118168,
    pageType: "PAGE",
    API_URL : "https://webapi-cloud.bluestacks.com/api/v1/report-page-view",
    PAGE_VIEW_AUTH_TOKEN: "zJMs#54KP5qvU6^kdC8Ly_ThyAXf&13px!^lo%3c!",
    category: [],
    ELASTIC_SEARCH_AUTH_TOKEN: "zJMs#54KP5qvU6^kdC8Ly_ThyAXf&13px!^lo%3c!",
    pageHost: "https://www.bluestacks.com",
    deviceMemory: navigator.deviceMemory,
    deviceCpuCores: navigator.hardwareConcurrency,
  };
</script>    <div class="scroll-top-section pc-visible">
  <span class="scroll-top-arrow"></span>
</div><svg xmlns="http://www.w3.org/2000/svg" style="display:none">
  <symbol width="45" height="44" viewBox="0 0 45 44" id="browser-icon">
    <path d="M38.6332 22.0004C38.6332 30.9106 31.4101 38.1337 22.4999 38.1337C13.5897 38.1337 6.36657 30.9106 6.36657 22.0004C6.36657 13.0902 13.5897 5.86706 22.4999 5.86706C31.4101 5.86706 38.6332 13.0902 38.6332 22.0004Z" stroke="white" stroke-width="2.93333" stroke-miterlimit="10" />
    <path d="M29.1569 21.9994C29.1569 26.6967 28.2752 30.8779 26.9127 33.8299C25.4928 36.9065 23.8267 38.1327 22.5005 38.1327C21.1743 38.1327 19.5083 36.9065 18.0883 33.8299C16.7259 30.8779 15.8441 26.6967 15.8441 21.9994C15.8441 17.3022 16.7259 13.1209 18.0883 10.169C19.5083 7.09238 21.1743 5.86608 22.5005 5.86608C23.8267 5.86608 25.4928 7.09237 26.9127 10.169C28.2752 13.1209 29.1569 17.3022 29.1569 21.9994Z" stroke="white" stroke-width="2.93333" stroke-miterlimit="10" />
    <path d="M7.30664 28.3174H37.9938" stroke="white" stroke-width="2.93333" stroke-miterlimit="10" />
    <path d="M7.30664 15.5312H37.9938" stroke="white" stroke-width="2.93333" stroke-miterlimit="10" />
  </symbol>
  <symbol width="44" height="44" viewBox="0 0 44 44" fill="none" id="download-icon">
    <path d="M21.9999 39.6004C31.7201 39.6004 39.5999 31.7206 39.5999 22.0004C39.5999 12.2802 31.7201 4.40039 21.9999 4.40039C12.2797 4.40039 4.3999 12.2802 4.3999 22.0004C4.3999 31.7206 12.2797 39.6004 21.9999 39.6004Z" stroke="white" stroke-width="2.93333" stroke-miterlimit="10" />
    <path d="M22 29.5225V13.4268" stroke="white" stroke-width="2.93333" stroke-miterlimit="10" stroke-linecap="round" />
    <path d="M30.2732 22L21.9997 30.2735L13.2749 22" stroke="white" stroke-width="2.93333" stroke-miterlimit="10" stroke-linecap="round" />
  </symbol>
  <symbol width="44" height="44" viewBox="0 0 44 44" fill="none" id="retry-download">
    <path d="M22.0412 37.5972V37.3493L21.7932 37.3472C11.6635 37.2641 3.5456 28.6979 4.29848 18.429C4.96812 9.86725 11.9565 2.92162 20.7054 2.29778L20.7056 2.29777C31.0879 1.5491 39.75 9.70052 39.75 19.8439C39.75 23.5846 38.5793 27.0332 36.5726 29.8589C36.2615 30.2681 35.682 30.3954 35.2492 30.1308L35.2493 30.1308L35.2452 30.1284L33.5915 29.1595C33.5913 29.1593 33.5911 29.1592 33.5909 29.1591C33.0779 28.8529 32.9475 28.2177 33.2845 27.749C35.1443 25.1706 36.0953 21.9476 35.7497 18.4701C35.101 11.8533 29.6114 6.5722 22.9538 6.14242C14.8726 5.58459 8.12735 11.9825 8.21347 19.9303C8.25707 27.279 14.3529 33.3749 21.7444 33.5037V33.5038H21.7487H21.7487H21.7487H21.7488H21.7488H21.7488H21.7488H21.7488H21.7489H21.7489H21.7489H21.7489H21.7489H21.749H21.749H21.749H21.749H21.7491H21.7491H21.7491H21.7491H21.7492H21.7492H21.7492H21.7493H21.7493H21.7493H21.7494H21.7494H21.7494H21.7495H21.7495H21.7495H21.7496H21.7496H21.7497H21.7497H21.7497H21.7498H21.7498H21.7499H21.7499H21.75H21.75H21.7501H21.7501H21.7502H21.7502H21.7503H21.7503H21.7504H21.7504H21.7505H21.7506H21.7506H21.7507H21.7507H21.7508H21.7509H21.7509H21.751H21.751H21.7511H21.7512H21.7512H21.7513H21.7514H21.7514H21.7515H21.7516H21.7517H21.7517H21.7518H21.7519H21.7519H21.752H21.7521H21.7522H21.7523H21.7523H21.7524H21.7525H21.7526H21.7527H21.7527H21.7528H21.7529H21.753H21.7531H21.7532H21.7532H21.7533H21.7534H21.7535H21.7536H21.7537H21.7538H21.7539H21.754H21.7541H21.7542H21.7543H21.7544H21.7545H21.7546H21.7547H21.7547H21.7549H21.755H21.7551H21.7552H21.7553H21.7554H21.7555H21.7556H21.7557H21.7558H21.7559H21.756H21.7561H21.7562H21.7563H21.7564H21.7566H21.7567H21.7568H21.7569H21.757H21.7571H21.7572H21.7574H21.7575H21.7576H21.7577H21.7578H21.758H21.7581H21.7582H21.7583H21.7584H21.7586H21.7587H21.7588H21.7589H21.7591H21.7592H21.7593H21.7595H21.7596H21.7597H21.7598H21.76H21.7601H21.7602H21.7604H21.7605H21.7606H21.7608H21.7609H21.7611H21.7612H21.7613H21.7615H21.7616H21.7617H21.7619H21.762H21.7622H21.7623H21.7625H21.7626H21.7627H21.7629H21.763H21.7632H21.7633H21.7635H21.7636H21.7638H21.7639H21.7641H21.7642H21.7644H21.7645H21.7647H21.7648H21.765H21.7651H21.7653H21.7655H21.7656H21.7658H21.7659H21.7661H21.7662H21.7664H21.7666H21.7667H21.7669H21.767H21.7672H21.7674H21.7675H21.7677H21.7679H21.768H21.7682H21.7683H21.7685H21.7687H21.7689H21.769H21.7692H21.7694H21.7695H21.7697H21.7699H21.77H21.7702H21.7704H21.7706H21.7707H21.7709H21.7711H21.7713H21.7714H21.7716H21.7718H21.772H21.7721H21.7723H21.7725H21.7727H21.7729H21.773H21.7732H21.7734H21.7736H21.7738H21.7739H21.7741H21.7743H21.7745H21.7747H21.7749H21.775H21.7752H21.7754H21.7756H21.7758H21.776H21.7762H21.7764H21.7766H21.7767H21.7769H21.7771H21.7773H21.7775H21.7777H21.7779H21.7781H21.7783H21.7785H21.7787H21.7789H21.7791H21.7793H21.7795H21.7797H21.7798H21.78H21.7802H21.7804H21.7806H21.7808H21.781H21.7812H21.7814H21.7816H21.7818H21.782H21.7823H21.7825H21.7827H21.7829H21.7831H21.7833H21.7835H21.7837H21.7839H21.7841H21.7843H21.7845H21.7847H21.7849H21.7851H21.7853H21.7855H21.7858H21.786H21.7862H21.7864H21.7866H21.7868H21.787H21.7872H21.7874H21.7877H21.7879H21.7881H21.7883H21.7885H21.7887H21.7889H21.7892H21.7894H21.7896H21.7898H21.79H21.7902H21.7905H21.7907H21.7909H21.7911H21.7913H21.7916H21.7918H21.792H21.7922H21.7924H21.7927H21.7929H21.7931H21.7933H21.7935H21.7938H21.794H21.7942H21.7944H21.7947H21.7949H21.7951H21.7953H21.7956H21.7958H21.796H21.7962H21.7965H21.7967H21.7969H21.7971H21.7974H21.7976H21.7978H21.798H21.7983H21.7985H21.7987H21.799H21.7992H21.7994H21.7996H21.7999H21.8001H21.8003H21.8006H21.8008H21.801H21.8013H21.8015H21.8017H21.802H21.8022H21.8024H21.8027H21.8029H21.8031H21.8034H21.8036H21.8038H21.8041H21.8043H21.8045H21.8048H21.805H21.8052H21.8055H21.8057H21.806H21.8062H21.8064H21.8067H21.8069H21.8071H21.8074H21.8076H21.8078H21.8081H21.8083H21.8086H21.8088H21.809H21.8093H21.8095H21.8098H21.81H21.8102H21.8105H21.8107H21.811H21.8112H21.8114H21.8117H21.8119H21.8122H21.8124H21.8127H21.8129H21.8131H21.8134H21.8136H21.8139H21.8141H21.8144H21.8146H21.8148H21.8151H21.8153H21.8156H21.8158H21.8161H21.8163H21.8165H21.8168H21.817H21.8173H21.8175H21.8178H21.818H21.8183H21.8185H21.8187H21.819H21.8192H21.8195H21.8197H21.82H21.8202H21.8205H21.8207H21.821H21.8212H21.8214H21.8217H21.8219H21.8222H21.8224H21.8227H21.8229H21.8232H21.8234H21.8237H21.8239H21.8242H21.8244H21.8247H21.8249H21.8252H21.8254H21.8256H21.8259H21.8261H21.8264H21.8266H21.8269H21.8271H21.8274H21.8276H21.8279H21.8281H21.8284H21.8286H21.8289H21.8291H21.8294H21.8296H21.8299H21.8301H21.8304H21.8306H21.8309H21.8311H21.8314H21.8316H21.8319H21.8321H21.8324H21.8326H21.8329H21.8331H21.8333H21.8336H22.0836V33.2538V30.0489C22.0836 29.3123 22.8905 28.8499 23.5258 29.2486L23.5272 29.2495L32.186 34.605L32.1877 34.606C32.7914 34.9726 32.7914 35.8363 32.1877 36.2028L32.1857 36.204L23.4845 41.6017L23.4834 41.6024C22.8481 42.0011 22.0412 41.5387 22.0412 40.8021V37.5972Z" fill="white" stroke="#99CE5C" stroke-width="0.5" />
  </symbol>
  <symbol width="17" height="17" viewBox="0 0 17 17" fill="none" id="cloud-icon">
    <path d="M8.71392 12.5898C7.543 12.5898 6.37207 12.5898 5.20115 12.5898C4.89126 12.5953 4.5824 12.5525 4.28554 12.4628C3.61947 12.2491 3.12972 11.8252 2.8167 11.2007C2.47698 10.5208 2.4054 9.80753 2.62702 9.0808C2.88626 8.22747 3.46053 7.67499 4.29767 7.39978C4.38139 7.37209 4.419 7.33952 4.42426 7.24425C4.47157 6.58023 4.71139 5.99478 5.16677 5.50867C5.66703 4.9737 6.28863 4.68057 7.01294 4.60648C7.51725 4.55212 8.02681 4.63535 8.48817 4.84745C8.94954 5.05955 9.34572 5.3927 9.63518 5.81198C9.65063 5.84097 9.67593 5.8634 9.70646 5.87513C9.73698 5.88687 9.77069 5.88715 9.8014 5.8759C10.6887 5.67478 11.4886 5.86938 12.1749 6.46298C12.7067 6.92344 12.9979 7.52395 13.0529 8.23235C13.0598 8.3207 13.0845 8.36385 13.1714 8.39805C13.9253 8.69648 14.3802 9.25302 14.4781 10.0595C14.576 10.8661 14.3499 11.5692 13.7202 12.1102C13.3967 12.3899 13.0117 12.5328 12.5878 12.5731C12.4641 12.5849 12.3391 12.5882 12.215 12.5882C11.0497 12.5901 9.88269 12.5906 8.71392 12.5898Z" stroke="white" stroke-width="1.01227" />
  </symbol>
  <symbol width="24" height="24" viewBox="0 0 24 24" fill="none" id="green-tick">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z" fill="#34CE43" />
    <path fill-rule="evenodd" clip-rule="evenodd" d="M17.0566 7.05786L18.9437 8.94202L10.0125 17.887L5.05664 12.9235L6.94371 11.0393L10.0122 14.1126L17.0566 7.05786Z" fill="white" />
  </symbol>
  <symbol width="24" height="24" viewBox="0 0 24 24" fill="none" id="cross-tick">
    <path fill-rule="evenodd" clip-rule="evenodd" d="M12 24C18.6274 24 24 18.6274 24 12C24 5.37258 18.6274 0 12 0C5.37258 0 0 5.37258 0 12C0 18.6274 5.37258 24 12 24Z" fill="#676A72" />
    <path d="M14.0207 17L11.8031 14.2222L9.93782 17H6.10363L9.9171 11.8925L6 7H9.97927L12.1969 9.75986L14.0622 7H17.8964L14.0207 12.0358L18 17H14.0207Z" fill="white" />
  </symbol>
  <symbol width="36" height="50" viewBox="0 0 36 50" id="download-bs5-logo" fill="none">
    <mask id="path-1-inside-1_1597_5790" fill="white">
      <path d="M32.3315 5.32287L35.8593 1.01218C36.1598 0.643031 35.9483 0.011908 35.5143 0.011908L15.2268 0C15.1155 0 15.0153 0.0714479 14.9597 0.17862L8.31587 14.8493C8.19346 15.0993 8.33813 15.4208 8.58296 15.4208H15.5606C15.7832 15.4208 15.9279 15.6947 15.85 15.9329L0.0250194 49.4777C-0.108525 49.8825 0.325493 50.1922 0.548067 49.8587L35.2695 9.76455C35.4364 9.51448 35.2918 9.15724 35.0247 9.15724H17.4191C17.1743 9.15724 17.0296 8.83573 17.152 8.59757L18.6099 5.63248C18.6655 5.5134 18.7657 5.45386 18.877 5.45386H32.1201C32.198 5.44195 32.2759 5.39432 32.3315 5.32287Z" />
    </mask>
    <path d="M32.3315 5.32287L35.8593 1.01218C36.1598 0.643031 35.9483 0.011908 35.5143 0.011908L15.2268 0C15.1155 0 15.0153 0.0714479 14.9597 0.17862L8.31587 14.8493C8.19346 15.0993 8.33813 15.4208 8.58296 15.4208H15.5606C15.7832 15.4208 15.9279 15.6947 15.85 15.9329L0.0250194 49.4777C-0.108525 49.8825 0.325493 50.1922 0.548067 49.8587L35.2695 9.76455C35.4364 9.51448 35.2918 9.15724 35.0247 9.15724H17.4191C17.1743 9.15724 17.0296 8.83573 17.152 8.59757L18.6099 5.63248C18.6655 5.5134 18.7657 5.45386 18.877 5.45386H32.1201C32.198 5.44195 32.2759 5.39432 32.3315 5.32287Z" fill="url(#paint0_linear_1597_5790)" />
    <path d="M32.3315 5.32287L31.2481 4.43621L31.2374 4.4493L31.227 4.46265L32.3315 5.32287ZM35.8593 1.01218L36.9428 1.89884L36.9451 1.89597L35.8593 1.01218ZM35.5143 0.011908L35.5135 1.41191H35.5143V0.011908ZM15.2268 0L15.2276 -1.4H15.2268V0ZM14.9597 0.17862L13.7172 -0.466489L13.6999 -0.433148L13.6844 -0.398927L14.9597 0.17862ZM8.31587 14.8493L9.57329 15.4648L9.58253 15.4459L9.59119 15.4268L8.31587 14.8493ZM15.85 15.9329L17.1162 16.5302L17.1534 16.4512L17.1806 16.3681L15.85 15.9329ZM0.0250194 49.4777L-1.24116 48.8803L-1.2777 48.9578L-1.30452 49.0391L0.0250194 49.4777ZM0.548067 49.8587L-0.510249 48.9422L-0.567652 49.0085L-0.616335 49.0814L0.548067 49.8587ZM35.2695 9.76455L36.3278 10.681L36.3852 10.6148L36.4339 10.5418L35.2695 9.76455ZM17.152 8.59757L18.3972 9.23758L18.4029 9.22648L18.4084 9.21528L17.152 8.59757ZM18.6099 5.63248L19.8662 6.25019L19.8724 6.23773L19.8782 6.22515L18.6099 5.63248ZM32.1201 5.45386V6.85386H32.2265L32.3316 6.83778L32.1201 5.45386ZM33.415 6.20953L36.9428 1.89884L34.7759 0.125517L31.2481 4.43621L33.415 6.20953ZM36.9451 1.89597C37.4485 1.27753 37.4861 0.5045 37.2873 -0.0783336C37.091 -0.65392 36.5135 -1.38809 35.5143 -1.38809V1.41191C34.9491 1.41191 34.6944 0.993297 34.6372 0.82556C34.5775 0.650575 34.5706 0.377683 34.7735 0.12839L36.9451 1.89597ZM35.5151 -1.38809L15.2276 -1.4L15.226 1.4L35.5135 1.41191L35.5151 -1.38809ZM15.2268 -1.4C14.4845 -1.4 13.9592 -0.932716 13.7172 -0.466489L16.2022 0.823728C16.0714 1.07561 15.7465 1.4 15.2268 1.4V-1.4ZM13.6844 -0.398927L7.04055 14.2717L9.59119 15.4268L16.235 0.756167L13.6844 -0.398927ZM7.05845 14.2337C6.79938 14.763 6.82513 15.3483 7.04032 15.815C7.25515 16.281 7.7737 16.8208 8.58296 16.8208V14.0208C9.14739 14.0208 9.47119 14.4 9.58308 14.6427C9.69533 14.8861 9.70996 15.1856 9.57329 15.4648L7.05845 14.2337ZM8.58296 16.8208H15.5606V14.0208H8.58296V16.8208ZM15.5606 16.8208C15.0395 16.8208 14.719 16.4935 14.5924 16.2643C14.4671 16.0374 14.4323 15.7636 14.5194 15.4976L17.1806 16.3681C17.3455 15.864 17.2773 15.3342 17.0435 14.9108C16.8085 14.4852 16.3043 14.0208 15.5606 14.0208V16.8208ZM14.5838 15.3356L-1.24116 48.8803L1.2912 50.075L17.1162 16.5302L14.5838 15.3356ZM-1.30452 49.0391C-1.556 49.8016 -1.29759 50.6247 -0.666261 51.0803C-0.351047 51.3078 0.0903335 51.4592 0.581618 51.3781C1.09196 51.2939 1.47678 50.9891 1.71247 50.636L-0.616335 49.0814C-0.491934 48.8951 -0.244835 48.6766 0.125708 48.6155C0.477188 48.5575 0.776678 48.6687 0.972259 48.8098C1.36432 49.0927 1.4725 49.5587 1.35456 49.9162L-1.30452 49.0391ZM1.60638 50.7752L36.3278 10.681L34.2112 8.84805L-0.510249 48.9422L1.60638 50.7752ZM36.4339 10.5418C36.7963 9.99896 36.8147 9.35483 36.608 8.8429C36.4013 8.33119 35.8646 7.75724 35.0247 7.75724V10.5572C34.4518 10.5572 34.121 10.1619 34.0117 9.89134C33.9023 9.62055 33.9096 9.28007 34.1051 8.98726L36.4339 10.5418ZM35.0247 7.75724H17.4191V10.5572H35.0247V7.75724ZM17.4191 7.75724C17.9806 7.75724 18.3035 8.13274 18.4162 8.37404C18.5287 8.61475 18.554 8.93248 18.3972 9.23758L15.9069 7.95755C15.6277 8.50081 15.6641 9.09838 15.8795 9.55939C16.0952 10.021 16.6128 10.5572 17.4191 10.5572V7.75724ZM18.4084 9.21528L19.8662 6.25019L17.3535 5.01476L15.8957 7.97985L18.4084 9.21528ZM19.8782 6.22515C19.8055 6.38084 19.6783 6.54734 19.4843 6.67314C19.2906 6.79876 19.076 6.85386 18.877 6.85386V4.05386C18.2034 4.05386 17.6193 4.44528 17.3415 5.0398L19.8782 6.22515ZM18.877 6.85386H32.1201V4.05386H18.877V6.85386ZM32.3316 6.83778C32.8069 6.76513 33.1894 6.49986 33.4361 6.18309L31.227 4.46265C31.3624 4.28877 31.5891 4.11876 31.9085 4.06993L32.3316 6.83778Z" fill="white" mask="url(#path-1-inside-1_1597_5790)" />
    <defs>
      <linearGradient id="paint0_linear_1597_5790" x1="7.14301" y1="4.95034" x2="25.4201" y2="34.5353" gradientUnits="userSpaceOnUse">
        <stop offset="0.0481283" stop-color="#1177B9" />
        <stop offset="0.7754" stop-color="#DF001F" />
      </linearGradient>
    </defs>
  </symbol>

  <symbol width="57" height="56" viewBox="0 0 57 56" fill="none" id="download-bsx-logo">
    <path d="M40.3611 49.2588C40.2063 49.2588 40.0525 49.2547 39.9 49.2477C39.7192 49.2388 39.5681 49.2275 39.4184 49.2133L39.3784 49.2094C39.2695 49.1985 39.1607 49.1857 39.0526 49.1711H38.7669L38.6389 49.1066C38.2579 49.0385 37.8785 48.9471 37.5083 48.8345L37.4778 48.8251C37.335 48.7814 37.1935 48.7342 37.0535 48.6838L37.0281 48.6747C36.8864 48.6231 36.7464 48.5692 36.6076 48.5113L36.5458 48.4852C36.428 48.4356 36.3003 48.3787 36.1731 48.3189L36.0997 48.2834C35.9762 48.2236 35.8597 48.1642 35.745 48.1033L35.6477 48.0507C35.5326 47.9872 35.4248 47.9259 35.3186 47.8619L35.194 47.7858C35.1045 47.731 35.0013 47.6649 34.8991 47.5964L34.7933 47.5246C34.6869 47.4518 34.5835 47.3776 34.4818 47.3015L34.3862 47.2302C34.2821 47.1504 34.1767 47.0662 34.0729 46.9805L34.0063 46.9251C33.8903 46.827 33.7787 46.7282 33.6684 46.6271L33.6319 46.5936C33.5073 46.4777 33.3852 46.3588 33.2663 46.2363L28.4998 41.3355L23.7313 46.2378C23.6131 46.3596 23.4919 46.4784 23.3682 46.5936L23.3343 46.6245C23.2239 46.7261 23.1116 46.8255 22.9976 46.922L22.9233 46.9836C22.8237 47.066 22.7183 47.1499 22.6109 47.2321L22.5297 47.2928C22.4142 47.3785 22.3118 47.452 22.2077 47.5236C22.1766 47.5455 22.1356 47.5729 22.095 47.6001C22.0015 47.6632 21.8968 47.7302 21.7908 47.7954L21.6848 47.8598C21.5778 47.9241 21.4684 47.9866 21.358 48.0475L21.2413 48.1103C21.1431 48.1629 21.0249 48.223 20.9056 48.2806L20.8383 48.3132C20.7008 48.378 20.5737 48.435 20.4446 48.4896L20.3784 48.5172C20.2522 48.5696 20.1123 48.6238 19.9712 48.6751L19.9383 48.6866C19.8073 48.7338 19.6675 48.7803 19.5262 48.8241L19.4904 48.8362L19.4572 48.8449C19.0974 48.9532 18.7287 49.0409 18.3581 49.107L18.2317 49.1707L17.9482 49.1711C17.8429 49.1853 17.7367 49.1979 17.6305 49.2086L17.5825 49.2133C17.4325 49.2275 17.2811 49.2388 17.129 49.2466L17.0757 49.2484C16.9471 49.2547 16.7931 49.2588 16.6382 49.2588C16.4836 49.2588 16.3296 49.2547 16.1765 49.2477C15.9943 49.2388 15.842 49.2275 15.691 49.2131L15.6462 49.2086C15.5402 49.1979 15.4344 49.1853 15.3295 49.1711L15.1988 49.1531C14.7216 49.0822 14.2461 48.975 13.7847 48.8345L13.7507 48.8241C13.609 48.7803 13.4689 48.7334 13.3302 48.6838C13.1664 48.6248 13.027 48.5703 12.8895 48.5137L12.8472 48.4957C12.7047 48.4356 12.5778 48.3791 12.4515 48.3195L12.4016 48.2954C12.2524 48.2232 12.135 48.164 12.0191 48.1018L11.9172 48.0464C11.8099 47.9876 11.6995 47.9244 11.5912 47.8591L11.4899 47.7982C11.3799 47.7306 11.2752 47.6632 11.1715 47.5934L11.0702 47.5251C10.9619 47.4503 10.8604 47.3772 10.7595 47.3024L10.6632 47.2302C10.56 47.1515 10.4536 47.0666 10.3487 46.9796L10.2711 46.9146C10.1664 46.8263 10.0536 46.7267 9.94256 46.6245L9.90745 46.5925C9.66144 46.3633 9.42347 46.1187 9.20031 45.8655L9.16647 45.827C9.06832 45.7137 8.97165 45.5982 8.87773 45.4806L8.84156 45.4345C8.73961 45.3042 8.6569 45.1948 8.57652 45.083L8.50735 44.9856C8.43713 44.886 8.36584 44.7814 8.29667 44.6753L8.22306 44.56C8.15875 44.458 8.09487 44.353 8.03269 44.2473L7.96711 44.1335C7.90387 44.0213 7.84316 43.9089 7.78393 43.7952L7.7374 43.7042C7.67224 43.5753 7.61428 43.4548 7.55844 43.3328L7.51762 43.243C7.46431 43.1247 7.40826 42.9918 7.35453 42.8567L7.33359 42.8046C7.27669 42.6582 7.2236 42.5149 7.17431 42.37L7.15823 42.3222C7.11656 42.2008 7.07151 42.056 7.02857 41.9086L7.01968 41.879C6.91075 41.5014 6.82254 41.1146 6.75676 40.7257L6.69266 40.595L6.69245 40.2923C6.67743 40.1746 6.66432 40.0561 6.65332 39.9376L6.64973 39.8956C6.63767 39.7673 6.62667 39.6092 6.61863 39.4496L6.61715 39.3963C6.61101 39.2632 6.60699 39.1046 6.60699 38.9455C6.60699 38.7863 6.61102 38.6277 6.61778 38.4703C6.62625 38.2911 6.63661 38.1391 6.65015 37.9888L6.65332 37.9535C6.66432 37.8348 6.67743 37.7172 6.69245 37.5995L6.71001 37.4657C6.7667 37.063 6.84708 36.6613 6.94925 36.2685V36.2535L7.03428 35.9617C7.07553 35.8212 7.11931 35.682 7.16627 35.5441C7.22423 35.3741 7.27732 35.231 7.33296 35.0887L7.34988 35.0457C7.40868 34.8991 7.4641 34.7671 7.5227 34.6364L7.5614 34.5509C7.61365 34.4376 7.67182 34.3161 7.73232 34.196L7.78668 34.0905C7.84316 33.982 7.90387 33.8698 7.96669 33.7584L8.03205 33.6449C8.09276 33.541 8.15791 33.4342 8.22412 33.3291L8.29329 33.2208C8.36668 33.1084 8.43839 33.0029 8.51201 32.8983L8.57187 32.8146C8.65753 32.6959 8.73876 32.588 8.82231 32.4814L8.87456 32.4144C8.9725 32.2914 9.06959 32.1754 9.16922 32.0615L9.20137 32.0239C9.31348 31.8973 9.42749 31.7733 9.54489 31.6526L12.5936 28.5182L9.54531 25.3837C9.42643 25.2619 9.31158 25.1373 9.19968 25.0101L9.16985 24.9759C9.07044 24.8613 8.97377 24.7463 8.87985 24.6288L8.83099 24.5662C8.74024 24.4509 8.6569 24.3405 8.57588 24.2276L8.51073 24.1354C8.43755 24.0319 8.36584 23.9266 8.29646 23.8196L8.22264 23.7048C8.15897 23.6047 8.09487 23.4982 8.03205 23.391L7.96288 23.2709C7.90238 23.1643 7.8421 23.0528 7.7833 22.9393L7.72766 22.8305C7.67246 22.7216 7.61492 22.6015 7.5595 22.4808L7.52227 22.3984C7.46368 22.2679 7.40784 22.1357 7.35453 22.0022L7.3283 21.9358C7.27796 21.8082 7.22571 21.6644 7.17579 21.5194C7.13412 21.3969 7.09901 21.2882 7.06707 21.1834L7.06305 21.1751L7.01905 21.0233C6.89721 20.5988 6.80097 20.1636 6.73349 19.7274L6.73053 19.718L6.70916 19.5638C6.68738 19.4048 6.66834 19.245 6.65332 19.0836L6.65057 19.0536C6.63703 18.902 6.62667 18.7494 6.61863 18.5956L6.61673 18.5384C6.61101 18.4082 6.60699 18.2507 6.60699 18.0917C6.60699 17.9315 6.61102 17.7738 6.61778 17.6165C6.62667 17.433 6.63703 17.2797 6.65057 17.1279L6.65332 17.0981C6.66834 16.9367 6.68738 16.7769 6.70916 16.6179L6.7318 16.4548L6.73497 16.4454C6.79843 16.0383 6.88642 15.6325 6.99726 15.2363L7.06305 15.0062L7.06664 14.9986C7.09816 14.8959 7.13074 14.7937 7.16522 14.6928L7.18214 14.645C7.22508 14.5193 7.2769 14.3777 7.33169 14.2372L7.3594 14.1678C7.40762 14.0467 7.46304 13.9151 7.52142 13.7846L7.55717 13.7059C7.6145 13.5809 7.67182 13.4619 7.73105 13.3442L7.7907 13.2283C7.84231 13.1281 7.90344 13.0152 7.9669 12.9039L8.03099 12.7921C8.09488 12.6836 8.15897 12.577 8.2256 12.4726L8.29371 12.3658C8.36584 12.2553 8.43755 12.1497 8.51073 12.0459L8.57419 11.9557C8.65753 11.8408 8.73918 11.7323 8.82295 11.6253L8.87689 11.5566C8.97419 11.4357 9.07001 11.3204 9.16901 11.2071L9.19418 11.1778C9.31136 11.0444 9.42644 10.9198 9.54447 10.7983L10.0506 10.2781H10.0902C10.1526 10.2226 10.2156 10.168 10.2791 10.1143L10.3402 10.0641C10.4521 9.97076 10.5596 9.88508 10.6689 9.80201L10.7601 9.73328C10.8614 9.65804 10.963 9.58518 11.066 9.51429L11.1781 9.4386C11.2762 9.37271 11.3805 9.30616 11.4859 9.24092L11.5925 9.17567C11.7016 9.11043 11.8093 9.04889 11.9178 8.98886L12.0202 8.93384C12.1357 8.8723 12.2518 8.81336 12.3686 8.75682L12.4498 8.71767C12.5767 8.65743 12.7047 8.60045 12.8339 8.54609L12.898 8.5189C13.0186 8.4691 13.1512 8.41734 13.2859 8.36884L13.3562 8.3434C13.4776 8.29947 13.628 8.24966 13.7797 8.20312L13.8192 8.19138C14.2201 8.0709 14.6315 7.97543 15.0442 7.90779L15.1702 7.8656L15.3285 7.86517C15.4372 7.8506 15.5465 7.83776 15.6563 7.82667L15.7005 7.82254C15.8486 7.80819 15.9975 7.79753 16.1473 7.78949L16.2021 7.78775C16.3292 7.78166 16.4828 7.77731 16.6378 7.77731C16.7931 7.77731 16.9473 7.78166 17.0998 7.78862C17.2783 7.79753 17.4281 7.80819 17.5761 7.82254L17.6201 7.82667C17.7297 7.83755 17.8386 7.8506 17.9472 7.86517H18.1054L18.2323 7.90779C18.6454 7.97543 19.0566 8.0709 19.4572 8.19138L19.4968 8.20312C19.6487 8.24966 19.7989 8.29947 19.9473 8.35318L19.967 8.36014C20.1098 8.41146 20.2509 8.46584 20.3905 8.52412L20.4231 8.53782C20.5737 8.60132 20.7008 8.6583 20.8267 8.71767L20.9026 8.75421C21.0158 8.8088 21.1253 8.86425 21.2334 8.92166C21.2578 8.93384 21.3307 8.97255 21.3307 8.97255L21.3783 9.00039C21.4684 9.04954 21.5778 9.11217 21.6855 9.17698L21.7986 9.2457C21.8968 9.30616 22.0015 9.37315 22.1049 9.44274C22.1356 9.4634 22.1766 9.4908 22.2179 9.5195C22.3118 9.5841 22.4142 9.6576 22.5157 9.73285L22.5927 9.7907C22.7183 9.88638 22.8237 9.97033 22.928 10.0567L23.0052 10.1213C23.1082 10.2078 23.2222 10.3087 23.3339 10.4118L23.3705 10.4446C23.4936 10.5599 23.6137 10.6773 23.7313 10.7983L28.4998 15.7006L33.2682 10.7983C33.3846 10.6784 33.506 10.5603 33.6295 10.4451L33.6562 10.4205C33.7787 10.3081 33.8903 10.2091 34.0048 10.1123L34.0767 10.0525C34.1763 9.97033 34.2817 9.88638 34.3891 9.80418L34.4835 9.73328C34.5835 9.65869 34.6869 9.58431 34.7921 9.51255L34.8932 9.44361C35.0013 9.3714 35.1045 9.30551 35.2092 9.24092C35.2365 9.22352 35.2799 9.19721 35.3234 9.17133C35.4258 9.11 35.5343 9.04802 35.6439 8.98756L35.7435 8.93384C35.859 8.8723 35.9751 8.81336 36.0917 8.75682L36.1615 8.72311C36.2998 8.65743 36.428 8.60045 36.5575 8.54609L36.6211 8.51934C36.7407 8.46931 36.8737 8.41777 37.0089 8.36884L37.0787 8.34383C37.202 8.29903 37.3518 8.24966 37.503 8.20312L37.5428 8.19138C37.9015 8.08351 38.2683 7.99609 38.6374 7.92998L38.7641 7.8656L39.0522 7.86517C39.1576 7.85103 39.2635 7.83863 39.3699 7.82776L39.4188 7.82298C39.5686 7.80862 39.7192 7.79753 39.8708 7.78949L39.9256 7.78753C40.0525 7.78166 40.2063 7.77731 40.3611 7.77731C40.5164 7.77731 40.6704 7.78166 40.8229 7.78862C41.0021 7.79753 41.1516 7.80819 41.2997 7.82254L41.3526 7.82733C41.4588 7.83863 41.5647 7.85103 41.6703 7.86517H41.8272L41.9539 7.90758C42.3664 7.97521 42.7776 8.07025 43.1783 8.19073L43.2201 8.20312C43.3712 8.24966 43.5209 8.29903 43.6694 8.35253L43.6904 8.36014C43.8338 8.41146 43.9753 8.46671 44.1153 8.52499L44.1775 8.55087C44.2888 8.59784 44.4108 8.652 44.5322 8.70919L44.6128 8.7466L44.6463 8.764C44.7681 8.82337 44.8739 8.87752 44.9779 8.93297L45.0742 8.98517C45.1903 9.04889 45.2982 9.11043 45.4044 9.17393L45.4862 9.22243L45.5351 9.25397C45.6186 9.30551 45.7217 9.3714 45.8238 9.43969L45.9296 9.51146C46.0334 9.58301 46.1348 9.65564 46.2344 9.7298L46.3171 9.78983L46.3505 9.81701C46.4502 9.89334 46.5483 9.97186 46.6452 10.0519L46.7218 10.115C46.8341 10.2098 46.9475 10.3105 47.0589 10.4135L47.1078 10.4586C47.2146 10.5575 47.3348 10.6749 47.4522 10.7952C47.5725 10.9192 47.687 11.0429 47.7982 11.1693L47.8297 11.2054C47.9319 11.323 48.0281 11.4383 48.1218 11.5555L48.1629 11.6077C48.26 11.7319 48.3418 11.8402 48.4212 11.9502L48.4903 12.047C48.5637 12.1516 48.6346 12.2562 48.7035 12.3617L48.7767 12.477C48.8406 12.577 48.9056 12.6836 48.9677 12.7908L49.0367 12.9108C49.0955 13.0141 49.1562 13.1261 49.2148 13.2392C49.2197 13.2464 49.2819 13.3703 49.2819 13.3703C49.3273 13.4602 49.3861 13.5824 49.4426 13.7055L49.4667 13.759C49.5376 13.9179 49.593 14.0484 49.6455 14.1796L49.6683 14.2372C49.7229 14.3777 49.7749 14.5193 49.8242 14.6621L49.8418 14.7148C49.8849 14.8411 49.9332 14.9951 49.9784 15.1502L50.0023 15.2363C50.1246 15.6732 50.2193 16.1234 50.2845 16.5761L50.3073 16.7032L50.3076 16.7449C50.3207 16.8504 50.3329 16.9565 50.3429 17.0631L50.3467 17.0911L50.3499 17.1377C50.3632 17.2877 50.3738 17.4387 50.3807 17.5907C50.3888 17.7738 50.393 17.9315 50.393 18.0896C50.393 18.2507 50.3888 18.4082 50.3822 18.5652C50.374 18.7396 50.3636 18.8905 50.3505 19.0388L50.3439 19.1071C50.3336 19.2172 50.3215 19.3268 50.3076 19.4357V19.4618L50.2875 19.5871C50.22 20.0605 50.1204 20.5309 49.9909 20.9868L49.9788 21.0298C49.934 21.1853 49.8856 21.3391 49.8335 21.4917C49.7749 21.6631 49.7227 21.8053 49.6675 21.9461L49.6455 22.0019C49.5928 22.1339 49.5374 22.2651 49.4794 22.3943L49.4331 22.4963C49.3864 22.5987 49.3282 22.7203 49.2679 22.8397L49.2169 22.9384C49.1579 23.0528 49.0968 23.1643 49.0344 23.2755L48.9677 23.391C48.9056 23.4982 48.8406 23.6047 48.774 23.7093L48.7114 23.8081C48.634 23.9266 48.5625 24.0319 48.4893 24.136L48.4258 24.2252C48.3429 24.3405 48.2598 24.4509 48.1741 24.5595L48.1106 24.6399C48.0298 24.7413 47.9363 24.8537 47.8397 24.9642C47.8217 24.9857 47.8035 25.0064 47.7849 25.0272C47.6891 25.1366 47.5746 25.2604 47.4574 25.3813L44.406 28.5182L47.4547 31.6528C47.5732 31.7744 47.6882 31.899 47.8003 32.0265L47.8308 32.0617C47.9302 32.1754 48.0271 32.2914 48.1218 32.4101L48.1705 32.4723C48.2608 32.5871 48.3437 32.6972 48.4237 32.8085L48.484 32.8935C48.5625 33.0042 48.635 33.1112 48.7055 33.2195L48.7721 33.3233C48.8417 33.4335 48.906 33.5394 48.9688 33.6467L49.0327 33.7578C49.0961 33.87 49.1562 33.9809 49.2146 34.0929L49.2681 34.1977C49.3278 34.3158 49.3857 34.4363 49.4416 34.5579L49.4779 34.6381C49.5374 34.7714 49.593 34.9037 49.6461 35.0368L49.6713 35.1001C49.7223 35.2299 49.7749 35.3739 49.8249 35.5189C49.8813 35.6844 49.9272 35.8303 49.9693 35.9754L49.9803 36.0117C50.1083 36.4562 50.2077 36.9136 50.2762 37.3722L50.3073 37.5005L50.3076 37.5995C50.32 37.6991 50.3317 37.7994 50.3414 37.9003L50.3446 37.9242L50.3494 37.987C50.363 38.1391 50.3738 38.2922 50.3807 38.4457C50.3888 38.6277 50.393 38.7863 50.393 38.9455C50.393 39.1046 50.3888 39.2632 50.3822 39.4206C50.374 39.5959 50.3632 39.7479 50.3501 39.8982L50.3429 39.9717C50.3329 40.0787 50.3211 40.1848 50.3076 40.2908V40.4593L50.2652 40.5896C50.1976 41.0243 50.1017 41.4575 49.9803 41.879L49.9691 41.9166C49.9268 42.0612 49.882 42.2045 49.8335 42.3465L49.8185 42.3898C49.7754 42.5162 49.7223 42.661 49.6662 42.8039L49.6486 42.8483C49.592 42.9903 49.5365 43.1208 49.4788 43.2499L49.449 43.3165C49.3857 43.4548 49.3278 43.5753 49.2675 43.6947L49.2173 43.7925C49.1568 43.9089 49.0972 44.0192 49.0356 44.1279L48.9675 44.2458C48.9077 44.3486 48.8427 44.4554 48.7757 44.5615L48.7063 44.6698C48.635 44.7797 48.5625 44.8867 48.4882 44.9917L48.4195 45.0887C48.3437 45.1942 48.2608 45.3036 48.176 45.4121L48.1128 45.4919C48.0271 45.5993 47.9302 45.7152 47.8308 45.8294L47.7849 45.8818C47.6882 45.9919 47.5732 46.1169 47.4547 46.2381C47.3352 46.3611 47.2146 46.4792 47.0913 46.5936L47.0589 46.6232C46.947 46.7261 46.8347 46.8255 46.7207 46.922L46.6596 46.9723C46.5466 47.066 46.4413 47.1504 46.3336 47.2321L46.2486 47.2965C46.1375 47.3785 46.0351 47.452 45.9309 47.5236C45.8976 47.5473 45.8543 47.5764 45.8101 47.6051C45.7257 47.6621 45.6229 47.7284 45.5182 47.793L45.3995 47.8648C45.2978 47.9259 45.1903 47.9872 45.0814 48.0475L44.9716 48.1066C44.8633 48.1642 44.7467 48.2236 44.6291 48.2806L44.5614 48.3132C44.4231 48.3787 44.2949 48.4356 44.1655 48.4902L44.1016 48.5172C43.9827 48.5663 43.8492 48.6185 43.7147 48.6675L43.6443 48.6925C43.5273 48.7349 43.3866 48.7821 43.2434 48.8258L43.2153 48.8345C42.8445 48.9476 42.4639 49.0387 42.0817 49.107L41.955 49.1707L41.6711 49.1711C41.5654 49.1853 41.4598 49.1979 41.353 49.2086L41.3046 49.2133C41.1548 49.2275 41.0038 49.2388 40.8521 49.2466L40.7973 49.2488C40.6704 49.2547 40.5164 49.2588 40.3622 49.2588H40.3611Z" fill="white" />
    <path d="M10.767 32.9112C7.52452 36.2451 7.52452 41.6503 10.767 44.984C14.0097 48.3178 19.267 48.3178 22.5095 44.984L34.3711 32.7888C37.6135 29.4549 37.6135 24.0498 34.3711 20.7161C31.1286 17.3824 25.8713 17.3824 22.6286 20.7161L10.767 32.9112Z" fill="#FFD900" />
    <path d="M24.9413 38.9478C24.9413 34.2332 21.2239 30.411 16.6382 30.411C12.0525 30.411 8.33496 34.2332 8.33496 38.9478C8.33496 43.6625 12.0525 47.4844 16.6382 47.4844C21.2239 47.4844 24.9413 43.6625 24.9413 38.9478Z" fill="#FFD900" />
    <path d="M46.2327 32.9112L34.3712 20.7161C32.7498 19.0491 30.625 18.2158 28.4998 18.2158V38.825L34.4902 44.984C37.7329 48.3178 42.9902 48.3178 46.2327 44.984C49.4752 41.6503 49.4752 36.2451 46.2327 32.9112Z" fill="#179AFF" />
    <path d="M48.6645 38.9478C48.6645 34.2332 44.9472 30.411 40.3615 30.411C35.7756 30.411 32.0582 34.2332 32.0582 38.9478C32.0582 43.6625 35.7756 47.4844 40.3615 47.4844C44.9472 47.4844 48.6645 43.6625 48.6645 38.9478Z" fill="#179AFF" />
    <path d="M10.767 24.1276C7.52452 20.7939 7.52452 15.3888 10.767 12.0549C14.0097 8.72121 19.267 8.72121 22.5095 12.0551L34.3711 24.2503C37.6135 27.5839 37.6135 32.9891 34.3711 36.323C31.1286 39.6567 25.8713 39.6567 22.6286 36.323L10.767 24.1276Z" fill="#106100" />
    <path d="M46.2327 12.0551C42.9902 8.72138 37.7329 8.72138 34.4902 12.0551L28.4998 18.214V38.8232C30.625 38.8232 32.7498 37.9897 34.3712 36.3229L46.2327 24.1278C49.4752 20.7941 49.4752 15.389 46.2327 12.0551Z" fill="#013886" />
    <path d="M32.3734 31.1172H35.9901C36.7162 31.1172 37.3051 30.5116 37.3051 29.7645C37.3051 29.0175 36.7162 28.4119 35.9901 28.4119H32.3734C31.647 28.4119 31.0581 29.0175 31.0581 29.7645C31.0581 30.5116 31.647 31.1172 32.3734 31.1172Z" fill="white" />
    <path d="M23.2007 27.8645C24.2179 27.8645 25.0426 28.7127 25.0426 29.7588C25.0426 30.8051 24.2179 31.6533 23.2007 31.6533C22.1832 31.6533 21.3587 30.8051 21.3587 29.7588C21.3587 28.7127 22.1832 27.8645 23.2007 27.8645Z" fill="white" />
    <path d="M46.2327 24.1276C49.4754 20.7939 49.4754 15.3887 46.2327 12.0551V12.0548C42.9902 8.72116 37.733 8.72116 34.4902 12.0548L28.4998 18.214L34.2519 24.1276C37.5603 27.5291 42.9242 27.5291 46.2327 24.1276Z" fill="#E7232A" />
    <path d="M48.6645 18.0894C48.6645 13.3748 44.9472 9.55285 40.3615 9.55285C35.7756 9.55285 32.0582 13.3748 32.0582 18.0894C32.0582 22.8041 35.7756 26.626 40.3615 26.626C44.9472 26.626 48.6645 22.8041 48.6645 18.0894Z" fill="#E7232A" />
    <path d="M24.9413 18.0894C24.9413 13.3748 21.2239 9.55285 16.6382 9.55285C12.0525 9.55285 8.33496 13.3748 8.33496 18.0894C8.33496 22.8041 12.0525 26.626 16.6382 26.626C21.2239 26.626 24.9413 22.8041 24.9413 18.0894Z" fill="#34CE42" />
    <path d="M10.767 24.1276C7.52452 20.7939 7.52452 15.3887 10.767 12.0551V12.0548C14.0097 8.72116 19.2668 8.72116 22.5095 12.0548L28.4999 18.214L22.7479 24.1276C19.4396 27.5291 14.0755 27.5291 10.767 24.1276Z" fill="#34CE42" />
  </symbol>
  <symbol width="53" height="14" viewBox="0 0 53 14" fill="none" id="powered_nowgg">
    <g clip-path="url(#clip0_8269_54020)">
      <path d="M9.20733 1.77187C9.80777 2.34195 10.108 3.20115 10.108 4.34948V8.7302H6.80006V4.8962C6.80006 4.01889 6.47299 3.58023 5.81885 3.58023C5.64946 3.57391 5.48072 3.60382 5.32492 3.66778C5.16912 3.73173 5.03017 3.82813 4.91819 3.94996C4.68997 4.19879 4.57587 4.58896 4.57587 5.12049V8.72319H1.26611V1.05694H4.41294V1.81393C4.71864 1.52049 5.08624 1.29288 5.49118 1.1463C5.91735 0.994217 6.36905 0.918196 6.82386 0.922012C7.8124 0.918507 8.60688 1.20179 9.20733 1.77187Z" fill="white" />
      <path d="M11.9897 8.35197C11.3095 8.033 10.7344 7.54067 10.3275 6.92912C9.92336 6.31486 9.71473 5.60127 9.72708 4.87544C9.71498 4.15018 9.92361 3.43723 10.3275 2.82351C10.7352 2.21785 11.3107 1.73252 11.9897 1.42168C12.7424 1.08918 13.5616 0.916992 14.3906 0.916992C15.2196 0.916992 16.0387 1.08918 16.7914 1.42168C17.4665 1.73265 18.0372 2.21823 18.439 2.82351C18.8415 3.44033 19.0493 4.15528 19.0376 4.88245C19.0484 5.60492 18.8426 6.31522 18.4445 6.92912C18.0425 7.53775 17.4723 8.02784 16.7969 8.34496C16.0455 8.68198 15.2259 8.85668 14.3961 8.85668C13.5663 8.85668 12.7466 8.68198 11.9952 8.34496L11.9897 8.35197ZM15.3196 5.9969C15.5807 5.68118 15.7111 5.28439 15.6857 4.88245C15.7121 4.48306 15.5815 4.08867 15.3196 3.77675C15.1997 3.65365 15.0546 3.55548 14.8934 3.48835C14.7321 3.42123 14.5582 3.38659 14.3823 3.38659C14.2065 3.38659 14.0325 3.42123 13.8713 3.48835C13.7101 3.55548 13.565 3.65365 13.4451 3.77675C13.1831 4.08867 13.0525 4.48306 13.0789 4.88245C13.0536 5.28439 13.1839 5.68118 13.4451 5.9969C13.5633 6.12262 13.7079 6.22314 13.8694 6.29195C14.0308 6.36076 14.2056 6.39631 14.3823 6.39631C14.5591 6.39631 14.7338 6.36076 14.8953 6.29195C15.0568 6.22314 15.2013 6.12262 15.3196 5.9969Z" fill="white" />
      <path d="M32.0971 1.05762V5.28939C32.0971 6.43071 31.7359 7.31153 31.0134 7.93184C30.2909 8.55215 29.2804 8.86055 27.9819 8.85705C27.4814 8.8631 26.983 8.79452 26.5046 8.65378C26.0718 8.52286 25.6794 8.29173 25.3623 7.9809C25.0429 8.29068 24.6515 8.52366 24.22 8.66079C23.7415 8.80058 23.2428 8.86682 22.7427 8.85705C21.4332 8.85705 20.4202 8.54865 19.7038 7.93184C18.9875 7.31504 18.6287 6.43188 18.6274 5.28238V1.05762H21.9372V5.07912C21.9372 5.92021 22.2295 6.34076 22.8141 6.34076C23.3986 6.34076 23.6964 5.92021 23.7074 5.07912V1.05762H27.0172V5.07912C27.0172 5.92021 27.3143 6.34076 27.9087 6.34076C28.0361 6.35118 28.1642 6.33025 28.2809 6.27995C28.3975 6.22966 28.4987 6.15167 28.575 6.05339C28.7178 5.85713 28.7874 5.5277 28.7874 5.08613V1.05762H32.0971Z" fill="white" />
      <path d="M33.4097 8.3624C33.2908 8.25071 33.1969 8.11693 33.134 7.96928C33.071 7.82163 33.0402 7.66322 33.0435 7.50378C33.0384 7.34514 33.0683 7.18725 33.1314 7.04047C33.1945 6.8937 33.2893 6.76137 33.4097 6.65217C33.652 6.44235 33.9671 6.32617 34.2938 6.32617C34.6206 6.32617 34.9357 6.44235 35.178 6.65217C35.2987 6.76109 35.3938 6.89337 35.457 7.04022C35.5201 7.18706 35.5498 7.34509 35.5442 7.50378C35.5479 7.66328 35.5173 7.82181 35.4543 7.96952C35.3913 8.11723 35.2973 8.25096 35.178 8.3624C34.9412 8.5827 34.6241 8.70594 34.2938 8.70594C33.9636 8.70594 33.6465 8.5827 33.4097 8.3624Z" fill="white" />
      <path d="M43.9138 1.01496V6.50837C43.9138 7.72329 43.5848 8.6228 42.927 9.2069C42.2692 9.79099 41.3088 10.083 40.0457 10.083C39.4057 10.0882 38.7679 10.0116 38.1491 9.85524C37.6119 9.72816 37.107 9.49871 36.6645 9.18061L37.459 7.80507C37.7857 8.05176 38.1585 8.2367 38.5574 8.35003C38.988 8.48307 39.4375 8.55164 39.8901 8.55329C40.582 8.55329 41.0928 8.4026 41.4205 8.10471C41.7481 7.80682 41.9202 7.35823 41.9202 6.75369V6.47158C41.3979 7.02063 40.6717 7.29457 39.7418 7.2934C39.1377 7.29974 38.5414 7.16247 38.0064 6.89388C37.4905 6.63694 37.0585 6.24892 36.7579 5.77241C36.4442 5.2692 36.2844 4.69184 36.2966 4.10599C36.2842 3.51956 36.444 2.94158 36.7579 2.43781C37.0583 1.96372 37.4889 1.57763 38.0027 1.32161C38.5377 1.05302 39.134 0.915741 39.7381 0.922085C40.7376 0.922085 41.498 1.23691 42.0191 1.86657V1.01496H43.9138ZM41.4351 5.27652C41.6028 5.12799 41.735 4.94646 41.8226 4.74441C41.9101 4.54236 41.9509 4.32457 41.9422 4.10599C41.9509 3.88715 41.9101 3.66909 41.8226 3.46675C41.7351 3.26441 41.6029 3.08258 41.4351 2.93371C41.0788 2.62902 40.6128 2.4688 40.1354 2.48688C39.6559 2.46972 39.1879 2.6297 38.8283 2.93371C38.6594 3.08203 38.5261 3.26363 38.4376 3.46602C38.3492 3.6684 38.3076 3.88674 38.3157 4.10599C38.3076 4.32498 38.3492 4.54306 38.4377 4.74515C38.5262 4.94724 38.6595 5.12854 38.8283 5.27652C39.1879 5.58052 39.6559 5.74051 40.1354 5.72335C40.6128 5.74143 41.0788 5.5812 41.4351 5.27652Z" fill="white" />
      <path d="M52.1131 1.01533V6.50875C52.1131 7.71783 51.7842 8.61675 51.1264 9.20552C50.4686 9.79429 49.5075 10.0863 48.2432 10.0817C47.6036 10.0852 46.9664 10.0068 46.3485 9.84861C45.8107 9.72152 45.3052 9.49208 44.862 9.17398L45.6565 7.80544C45.9831 8.05228 46.3559 8.23723 46.7549 8.3504C47.1849 8.48354 47.6338 8.55211 48.0857 8.55367C48.7777 8.55367 49.2885 8.40297 49.618 8.10508C49.9475 7.80719 50.1104 7.3551 50.1104 6.75056V6.47195C49.5893 7.021 48.8631 7.29494 47.932 7.29377C47.3279 7.30012 46.7316 7.16284 46.1965 6.89425C45.6807 6.63732 45.2487 6.2493 44.9481 5.77279C44.6332 5.26993 44.4721 4.69255 44.4831 4.10636C44.4707 3.51993 44.6305 2.94195 44.9444 2.43819C45.245 1.96168 45.677 1.57366 46.1929 1.31672C46.7279 1.04814 47.3242 0.910858 47.9283 0.917203C48.9278 0.917203 49.6881 1.23203 50.2093 1.86169V1.01533H52.1131ZM49.6344 5.27689C49.8017 5.12806 49.9334 4.94644 50.0206 4.74443C50.1078 4.54242 50.1484 4.32478 50.1397 4.10636C50.1483 3.8877 50.1076 3.66983 50.0205 3.46755C49.9333 3.26527 49.8016 3.08333 49.6344 2.93408C49.2671 2.64534 48.8064 2.48751 48.331 2.48751C47.8557 2.48751 47.395 2.64534 47.0276 2.93408C46.8584 3.08211 46.7248 3.26366 46.6363 3.46611C46.5478 3.66857 46.5064 3.88705 46.5151 4.10636C46.5064 4.32542 46.5478 4.54363 46.6363 4.7458C46.7249 4.94797 46.8584 5.12921 47.0276 5.27689C47.395 5.56564 47.8557 5.72347 48.331 5.72347C48.8064 5.72347 49.2671 5.56564 49.6344 5.27689Z" fill="white" />
    </g>
    <defs>
      <clipPath id="clip0_8269_54020">
        <rect width="52" height="14" fill="white" transform="translate(0.5)" />
      </clipPath>
    </defs>
  </symbol>

</svg>  </div>
  <script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/dist/js/home-v2/common.js?v=74b3e229"></script>
  <script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/dist/js/download-page/script.js?v=ee6660bd">
  </script>
  <script>
var utmFallback = "download-en"

if (window.isAMobile) {
  setTimeout(function() {
    var onPcString = [].slice.call(document.querySelectorAll('.string-if-on-pc'))
    onPcString.forEach(function(string) {
      string.remove()
    })

      }, 100);
}
</script>
<script>
			var js_global = {
			'current_language_nonce': 'c5bc1a18da',
			'xhr_url': 'https://www.bluestacks.com/bs-cms/admin-ajax.php',
			'theme': 'https://www.bluestacks.com/wp-content/themes/bluestacks'
			}
			var available_translations = "[]"
			</script>	<!-- <script async src="/static/components/annoucementBar.js"></script> -->

<script>
// defining a forEach for nodelists * fallback
if (window.NodeList && !NodeList.prototype.forEach) {
	console.log('NodeList forEach Polyfiled :)')
	NodeList.prototype.forEach = Array.prototype.forEach;
}

window.currentLanguage = window.currentLanguage || 'ar'
var searchAutocompleteGlobalVars = {
	searchTerm: ''
};
</script>

<script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/search-suggest/public/js/bundle.js?v=1747912728"></script>
<script async
  src="https://www.bluestacks.com/wp-content/themes/bluestacks/dist/js/new-components/globalFunction.js?v=0bdaeef7"></script>
<script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/js/pre-cookie.js?v=1.1.66"></script>
<script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/dist/js/components/essentialScripts.js?v=36619808">
</script>
<template id="android-warning" style="display:none">
	<img src="https://cdn-www.bluestacks.com/bs-images/bs_logo2.png" alt="Bluestacks" width="37" height="32">
	<h5>BlueStacks <br>هي منصة خاصة بالحاسب الشخصي</h5>
	<p>
		بمجرد أن تكون على هاتفك، سنقوم بتوجيهك إلى صفحة Google Play.	</p>
	<p class="small">
		(في بعض الحالات يستخدم  BlueStacks الروابط التابعة عند التوصيل بـ Google Play	</p>
</template>

<template id="no-support-warning" style="display:none">
	<img src="https://cdn-www.bluestacks.com/bs-images/bs_logo2.png" alt="Bluestacks" width="37" height="32">
	<h5>
		عذرا، بلوستاكس هو فقط لكمبيوتر شخصي أو ماكنتوش	</h5>
</template>

<template id="if-is-windows-xp" style="display:none">
	<img src="https://cdn-www.bluestacks.com/bs-images/bs_logo2.png" alt="Bluestacks" width="37" height="32">
	<h5>BlueStacks</h5>
	<p class="xp">
		لا يتوفر بلو ستاكس 4 على نظام التشغيل ويندوز XP. يجب أن يكون لديك ويندوز 7 أو أعلى. ينصح بويندوس 10.	</p>
</template>

<template id="if-is-xbox" style="display:none">
	<img src="https://cdn-www.bluestacks.com/bs-images/xbox-not-supported-popup.jpg" alt="Bluestacks" height="100%" width="100%">
</template>

<div id="pop-up" class="pop-up">
	<button type="button" class="close-modal"></button>
 <div class="pop-up-content">
</div>
</div>

  <script async src="https://www.bluestacks.com/wp-content/themes/bluestacks/dist/js/new-components/newOnboardingDownload.js?v=6d8feae7"></script>

<script>
  let playInBrowserCTAHandlerScript = document.createElement('script');
  playInBrowserCTAHandlerScript.src = "https://www.bluestacks.com/wp-content/themes/bluestacks/dist/js/new-components/playInBrowser.js?v=c8af2e6d";
  playInBrowserCTAHandlerScript.async = true;
  document.body.appendChild(playInBrowserCTAHandlerScript);
</script><script type="application/ld+json">{"@context":"https:\/\/schema.org","@type":"FAQPage","mainEntity":[{"@type":"Question","name":"What is BlueStacks X?","acceptedAnswer":{"@type":"Answer","text":"Launched by BlueStacks, BlueStacks X (currently in Beta) is the world’s first cloud-based Android gaming platform. You can play Android games instantly on the cloud for free using the web app or you can install the PC client and get going."}},{"@type":"Question","name":"What devices and games does BlueStacks X mobile cloud gaming support?","acceptedAnswer":{"@type":"Answer","text":"You can play 200+ free Android cloud games with top RPG and strategy titles directly from the browser on any device (phone, tablet, PC, laptop, TV) or OS ."}},{"@type":"Question","name":"How can I download BlueStacks 5?","acceptedAnswer":{"@type":"Answer","text":"You can download BlueStacks 5 from the links on this page or from our website bluestacks.com. <br\/> Download the .exe file and complete the setup within minutes to start playing your favorite mobile games on PC."}},{"@type":"Question","name":"How to install BlueStacks?","acceptedAnswer":{"@type":"Answer","text":"<p>To install BlueStacks on your PC or Mac simply do the following<\/p>\n              <ul>\n                <li>Download the .exe\/.dmg file from bluestacks.com<\/li>\n                <li>Once the download is complete, just follow the instructions as they appear<\/li>\n                <li>After the first boot is complete, sign in with your Gmail account or add it later<\/li>\n              <\/ul>\n              <p>In case you don’t have a Gmail account, you can sign in with another one by following a few simple steps.<\/p>"}},{"@type":"Question","name":"How to update BlueStacks?","acceptedAnswer":{"@type":"Answer","text":"<p>Follow the steps below to update BlueStacks on your <strong>Windows<\/strong> PC<\/p>\n              <ul>\n                  <li>Open the App Player, click on the gear icon on the side toolbar for Settings<\/li>\n                  <li>Click on ‘About’ on the Settings window<\/li>\n                  <li>Click on ‘Check for update’<\/li>\n                  <li>If an update is available, you can download and install it by clicking on ‘Download now’  <\/li>\n              <\/ul>\n              <br\/>\n                <p>In case you are using a <strong>Mac<\/strong>, do the following<\/p>\n              <ul>\n                  <li>Open the App Player, click on the top left corner of your screen<\/li>\n                  <li>Click on ‘Check for Updates’<\/li>\n                <\/ul>\n                <br \/>\n              <p>To update to BlueStacks 5, simply download it from this page or visit bluestacks.com. Please note, we are presently working on making BlueStacks 5 available for macOS<\/p>"}},{"@type":"Question","name":"Is BlueStacks free to download?","acceptedAnswer":{"@type":"Answer","text":"Yes, the Android Emulator is free to download. Although there is a paid version as well, however, that doesn’t affect the gaming experience in any way."}},{"@type":"Question","name":"Why does BlueStacks need me to log in to my Google account?","acceptedAnswer":{"@type":"Answer","text":"Google account login is required to help install games from Google Play Store and for troubleshooting issues just the way it is on your phone."}}]}</script></body>
<svg xmlns="http://www.w3.org/2000/svg" style="display:none">
  <symbol width="19" height="19" viewBox="0 0 19 19" fill="none" id="windows-icon">
    <g clip-path="url(#clip_win_0)">
      <path
        d="M0.706055 3.42368L7.61293 2.48311L7.61591 9.14533L0.712326 9.18461L0.706055 3.42368ZM7.60963 9.91286L7.61491 16.5809L0.711402 15.6317L0.711006 9.86817L7.60963 9.91286ZM8.44688 2.35999L17.6047 1.02344V9.06056L8.44688 9.13318V2.35999ZM17.6068 9.97558L17.6047 17.9765L8.44682 16.6839L8.43401 9.96059L17.6068 9.97558Z"
        fill="#394566" />
    </g>
    <defs>
      <clipPath id="clip_win_0">
        <rect width="18" height="18" fill="white" transform="translate(0.15625 0.5)" />
      </clipPath>
    </defs>
  </symbol>
  <symbol width="19" height="19" viewBox="0 0 19 19" fill="none" id="windows-white-icon">
    <g clip-path="url(#clip_win_white_0)">
      <path
        d="M0.706055 3.42368L7.61293 2.48311L7.61591 9.14533L0.712326 9.18461L0.706055 3.42368ZM7.60963 9.91286L7.61491 16.5809L0.711402 15.6317L0.711006 9.86817L7.60963 9.91286ZM8.44688 2.35999L17.6047 1.02344V9.06056L8.44688 9.13318V2.35999ZM17.6068 9.97558L17.6047 17.9765L8.44682 16.6839L8.43401 9.96059L17.6068 9.97558Z"
        fill="#fff" />
    </g>
    <defs>
      <clipPath id="clip_win_white_0">
        <rect width="18" height="18" fill="white" transform="translate(0.15625 0.5)" />
      </clipPath>
    </defs>
  </symbol>
  <symbol width="18" height="25" viewBox="0 0 18 25" fill="none" id="mac-white-icon">
    <path fill-rule="evenodd" clip-rule="evenodd"
      d="M9.75686 4.06639C10.9356 2.50759 12.5743 2.5 12.5743 2.5C12.5743 2.5 12.818 3.96552 11.6471 5.3773C10.3967 6.8848 8.97553 6.63814 8.97553 6.63814C8.97553 6.63814 8.70867 5.45258 9.75686 4.06639ZM9.12545 7.66471C9.73187 7.66471 10.8573 6.82836 12.3222 6.82836C14.8438 6.82836 15.8358 8.62868 15.8358 8.62868C15.8358 8.62868 13.8956 9.62395 13.8956 12.039C13.8956 14.7633 16.3125 15.7023 16.3125 15.7023C16.3125 15.7023 14.623 20.4736 12.3409 20.4736C11.2928 20.4736 10.478 19.7649 9.37359 19.7649C8.2482 19.7649 7.1314 20.5 6.40395 20.5C4.32014 20.5001 1.6875 15.974 1.6875 12.3358C1.6875 8.75629 3.91585 6.8785 6.00595 6.8785C7.36468 6.8785 8.41908 7.66471 9.12545 7.66471Z"
      fill="#fff" />
  </symbol>
  <symbol width="18" height="25" viewBox="0 0 18 25" fill="none" id="mac-icon">
    <path fill-rule="evenodd" clip-rule="evenodd"
      d="M9.75686 4.06639C10.9356 2.50759 12.5743 2.5 12.5743 2.5C12.5743 2.5 12.818 3.96552 11.6471 5.3773C10.3967 6.8848 8.97553 6.63814 8.97553 6.63814C8.97553 6.63814 8.70867 5.45258 9.75686 4.06639ZM9.12545 7.66471C9.73187 7.66471 10.8573 6.82836 12.3222 6.82836C14.8438 6.82836 15.8358 8.62868 15.8358 8.62868C15.8358 8.62868 13.8956 9.62395 13.8956 12.039C13.8956 14.7633 16.3125 15.7023 16.3125 15.7023C16.3125 15.7023 14.623 20.4736 12.3409 20.4736C11.2928 20.4736 10.478 19.7649 9.37359 19.7649C8.2482 19.7649 7.1314 20.5 6.40395 20.5C4.32014 20.5001 1.6875 15.974 1.6875 12.3358C1.6875 8.75629 3.91585 6.8785 6.00595 6.8785C7.36468 6.8785 8.41908 7.66471 9.12545 7.66471Z"
      fill="#394566" />
  </symbol>
</svg>
<script>

const instaInfoPopup = document.querySelector('.insta-info');
const crossInstaIcon = document.querySelector('.cross-insta-icon');
const bsHeader = document.querySelector('.bsx-header-wrapper');




crossInstaIcon && crossInstaIcon.addEventListener('click', () => {
  instaInfoPopup && instaInfoPopup.classList.remove('show');
  bsHeader && bsHeader.classList.remove('hide');
})
let displayFullScreenPopup = function(){
  instaInfoPopup && instaInfoPopup.classList.toggle('show');
  if (instaInfoPopup && instaInfoPopup.classList.contains('show')) {
    bsHeader && bsHeader.classList.add('hide');
  } else {
    bsHeader && bsHeader.classList.remove('hide');
  }
}
getCountrySpecificGames();
function smoothScrollToAllVersions(event){
  event.preventDefault();
  const targetElement = document.getElementById("all-versions");
  targetElement.scrollIntoView({
    behavior: 'smooth',
    block: 'start'
  });
}

const downloadMobileButtonAndroid = document.querySelector('#download-bstk-btn-android');
    const downloadMobileButtonIphone = document.querySelector('#download-bstk-btn-mobile');
    downloadMobileButtonAndroid && downloadMobileButtonAndroid.addEventListener('click', () => {
      window.showApkDownloadPopup();
    })
    downloadMobileButtonIphone && downloadMobileButtonIphone.addEventListener('click', () => {
        instaInfoPopup && instaInfoPopup.classList.toggle('show');
      if (instaInfoPopup && instaInfoPopup.classList.contains('show')) {
        bsHeader && bsHeader.classList.add('hide');
      } else {
        bsHeader && bsHeader.classList.remove('hide');
      }
    })
</script>

</html>