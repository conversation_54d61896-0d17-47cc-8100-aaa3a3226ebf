# دليل تثبيت وتجربة التطبيق 📱

## 📍 مكان ملف APK:
```
app/build/outputs/apk/debug/app-debug.apk
```

## 🚀 خطوات التثبيت:

### 1. نقل ملف APK إلى الهاتف:
- **عبر USB**: انسخ ملف `app-debug.apk` إلى هاتفك
- **عبر البريد الإلكتروني**: أرسل الملف لنفسك وحمله على الهاتف
- **عبر Google Drive/Dropbox**: ارفع الملف وحمله على الهاتف

### 2. تفعيل تثبيت التطبيقات من مصادر غير معروفة:
1. اذهب إلى **الإعدادات** > **الأمان**
2. فعل **مصادر غير معروفة** أو **تثبيت التطبيقات من مصادر غير معروفة**
3. أو عند محاولة التثبيت، سيطلب منك تفعيل هذا الخيار

### 3. تثبيت التطبيق:
1. افتح مدير الملفات في هاتفك
2. ابحث عن ملف `app-debug.apk`
3. اضغط على الملف
4. اضغط **تثبيت**
5. انتظر حتى يكتمل التثبيت

## 🧪 تجربة التطبيق:

### ✅ ما يجب أن تراه:
1. **شاشة تسجيل الدخول** عند فتح التطبيق لأول مرة
2. **حقول الإيميل وكلمة المرور** باللغة العربية
3. **أزرار تسجيل الدخول وإنشاء حساب**

### 🔧 خطوات الاختبار:

#### إنشاء حساب جديد:
1. اضغط **"إنشاء حساب جديد"**
2. أدخل إيميل صحيح (مثل: <EMAIL>)
3. أدخل كلمة مرور (6 أحرف على الأقل)
4. أكد كلمة المرور
5. اضغط **"إنشاء الحساب"**
6. يجب أن تنتقل إلى الصفحة الرئيسية

#### تسجيل الدخول:
1. استخدم نفس الإيميل وكلمة المرور
2. اضغط **"تسجيل الدخول"**
3. يجب أن تنتقل إلى الصفحة الرئيسية

#### الصفحة الرئيسية:
- ستظهر رسالة **"مرحباً [اسم المستخدم]"**
- معلومات الحساب
- زر تسجيل الخروج في الأعلى

## ⚠️ متطلبات مهمة:

### 1. اتصال الإنترنت:
- التطبيق يحتاج إنترنت للاتصال بـ Firebase
- تأكد من وجود اتصال Wi-Fi أو بيانات الجوال

### 2. إعدادات Firebase:
- **مهم جداً**: يجب تفعيل Authentication في Firebase Console
- اذهب إلى [Firebase Console](https://console.firebase.google.com/)
- اختر مشروع `comweb22-98ea3`
- اذهب إلى Authentication > Sign-in method
- فعل **Email/Password**

## 🐛 حل المشاكل الشائعة:

### المشكلة: "لا يمكن تثبيت التطبيق"
**الحل:**
- تأكد من تفعيل "مصادر غير معروفة"
- تأكد من وجود مساحة كافية على الهاتف
- جرب إعادة تشغيل الهاتف

### المشكلة: "فشل تسجيل الدخول"
**الحل:**
- تحقق من اتصال الإنترنت
- تأكد من تفعيل Email/Password في Firebase Console
- تأكد من صحة الإيميل وكلمة المرور

### المشكلة: "التطبيق يتوقف"
**الحل:**
- تأكد من أن هاتفك يدعم Android 7.0 (API 24) أو أحدث
- جرب إعادة تثبيت التطبيق
- تأكد من اتصال الإنترنت

## 📋 معلومات التطبيق:

- **اسم التطبيق**: anime
- **Package Name**: com.web22.myapplication
- **الحد الأدنى لنظام Android**: 7.0 (API 24)
- **الحد الأقصى**: Android 14 (API 35)

## 🎯 الميزات المتاحة للاختبار:

✅ تسجيل دخول بالإيميل وكلمة المرور  
✅ إنشاء حساب جديد  
✅ صفحة رئيسية بترحيب شخصي  
✅ تسجيل خروج  
✅ واجهات عربية جميلة  
✅ معالجة الأخطاء  
⚠️ تسجيل دخول بـ Google (يحتاج إعداد إضافي)  

## 📞 للمساعدة:

إذا واجهت أي مشاكل:
1. تأكد من اتصال الإنترنت
2. تأكد من إعدادات Firebase
3. جرب إعادة تثبيت التطبيق

**ملف APK جاهز للتجربة! 🎉**

**مكان الملف**: `app/build/outputs/apk/debug/app-debug.apk`
