package com.web22.myapplication.data.models

data class User(
    val uid: String = "",
    val displayName: String = "",
    val email: String = "",
    val profileImageUrl: String = "",
    val isOnline: Boolean = false,
    val lastSeen: Long = 0L
) {
    // Constructor بدون معاملات للـ Firebase
    constructor() : this("", "", "", "", false, 0L)
}

data class FriendRequest(
    val id: String = "",
    val fromUserId: String = "",
    val toUserId: String = "",
    val fromUserName: String = "",
    val fromUserEmail: String = "",
    val timestamp: Long = 0L,
    val status: FriendRequestStatus = FriendRequestStatus.PENDING
) {
    constructor() : this("", "", "", "", "", 0L, FriendRequestStatus.PENDING)
}

enum class FriendRequestStatus {
    PENDING,
    ACCEPTED,
    REJECTED
}

data class Friendship(
    val id: String = "",
    val user1Id: String = "",
    val user2Id: String = "",
    val timestamp: Long = 0L
) {
    constructor() : this("", "", "", 0L)
}
