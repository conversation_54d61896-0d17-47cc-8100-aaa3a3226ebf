{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-72:/values-in/values-in.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\549dfb7dd035cf2830ebdc27ba100054\\transformed\\appcompat-1.1.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,898,989,1081,1176,1270,1371,1464,1559,1653,1744,1835,1920,2023,2128,2229,2333,2442,2550,2710,2809", "endColumns": "114,103,104,86,103,115,82,78,90,91,94,93,100,92,94,93,90,90,84,102,104,100,103,108,107,159,98,83", "endOffsets": "215,319,424,511,615,731,814,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1915,2018,2123,2224,2328,2437,2545,2705,2804,2888"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,324,429,516,620,736,819,898,989,1081,1176,1270,1371,1464,1559,1653,1744,1835,1920,2023,2128,2229,2333,2442,2550,2710,13627", "endColumns": "114,103,104,86,103,115,82,78,90,91,94,93,100,92,94,93,90,90,84,102,104,100,103,108,107,159,98,83", "endOffsets": "215,319,424,511,615,731,814,893,984,1076,1171,1265,1366,1459,1554,1648,1739,1830,1915,2018,2123,2224,2328,2437,2545,2705,2804,13706"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e71863d474cbeda869e988a1e7d78c0\\transformed\\credentials-1.2.0-rc01\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,164", "endColumns": "108,117", "endOffsets": "159,277"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2809,2918", "endColumns": "108,117", "endOffsets": "2913,3031"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ae8da29e7eccb1a8725377c96f852ddc\\transformed\\material3-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,289,401,517,615,721,844,991,1114,1264,1351,1455,1548,1652,1770,1890,1999,2139,2277,2406,2584,2706,2826,2949,3072,3166,3267,3387,3520,3622,3729,3836,3978,4125,4234,4334,4410,4506,4601,4719,4808,4893,4992,5072,5155,5254,5353,5450,5550,5637,5740,5839,5943,6060,6140,6245", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "169,284,396,512,610,716,839,986,1109,1259,1346,1450,1543,1647,1765,1885,1994,2134,2272,2401,2579,2701,2821,2944,3067,3161,3262,3382,3515,3617,3724,3831,3973,4120,4229,4329,4405,4501,4596,4714,4803,4888,4987,5067,5150,5249,5348,5445,5545,5632,5735,5834,5938,6055,6135,6240,6335"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7005,7124,7239,7351,7467,7565,7671,7794,7941,8064,8214,8301,8405,8498,8602,8720,8840,8949,9089,9227,9356,9534,9656,9776,9899,10022,10116,10217,10337,10470,10572,10679,10786,10928,11075,11184,11284,11360,11456,11551,11669,11758,11843,11942,12022,12105,12204,12303,12400,12500,12587,12690,12789,12893,13010,13090,13195", "endColumns": "118,114,111,115,97,105,122,146,122,149,86,103,92,103,117,119,108,139,137,128,177,121,119,122,122,93,100,119,132,101,106,106,141,146,108,99,75,95,94,117,88,84,98,79,82,98,98,96,99,86,102,98,103,116,79,104,94", "endOffsets": "7119,7234,7346,7462,7560,7666,7789,7936,8059,8209,8296,8400,8493,8597,8715,8835,8944,9084,9222,9351,9529,9651,9771,9894,10017,10111,10212,10332,10465,10567,10674,10781,10923,11070,11179,11279,11355,11451,11546,11664,11753,11838,11937,12017,12100,12199,12298,12395,12495,12582,12685,12784,12888,13005,13085,13190,13285"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7661eb1439e484e98fea3778baf664e3\\transformed\\play-services-base-18.5.0\\res\\values-in\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,456,578,680,831,954,1065,1167,1329,1430,1590,1712,1863,2003,2063,2119", "endColumns": "102,159,121,101,150,122,110,101,161,100,159,121,150,139,59,55,74", "endOffsets": "295,455,577,679,830,953,1064,1166,1328,1429,1589,1711,1862,2002,2062,2118,2193"}, "to": {"startLines": "40,41,42,43,44,45,46,47,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3938,4045,4209,4335,4441,4596,4723,4838,5076,5242,5347,5511,5637,5792,5936,6000,6060", "endColumns": "106,163,125,105,154,126,114,105,165,104,163,125,154,143,63,59,78", "endOffsets": "4040,4204,4330,4436,4591,4718,4833,4939,5237,5342,5506,5632,5787,5931,5995,6055,6134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e099880564deb176147b2205fdc84816\\transformed\\foundation-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,141", "endColumns": "85,89", "endOffsets": "136,226"}, "to": {"startLines": "137,138", "startColumns": "4,4", "startOffsets": "14374,14460", "endColumns": "85,89", "endOffsets": "14455,14545"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09ab614e376350ca0889dc2dbf11206f\\transformed\\browser-1.4.0\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,155,253,362", "endColumns": "99,97,108,100", "endOffsets": "150,248,357,458"}, "to": {"startLines": "58,62,63,64", "startColumns": "4,4,4,4", "startOffsets": "6139,6523,6621,6730", "endColumns": "99,97,108,100", "endOffsets": "6234,6616,6725,6826"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,150,252,349,446,552,670,785", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "145,247,344,441,547,665,780,881"}, "to": {"startLines": "31,32,33,34,35,36,37,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3036,3131,3233,3330,3427,3533,3651,14010", "endColumns": "94,101,96,96,105,117,114,100", "endOffsets": "3126,3228,3325,3422,3528,3646,3761,14106"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f43be022de2035b39234c04c60821b4e\\transformed\\ui-release\\res\\values-in\\values-in.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,195,277,375,475,561,644,735,822,907,989,1072,1144,1221,1298,1371,1449,1515", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,76,76,72,77,65,118", "endOffsets": "190,272,370,470,556,639,730,817,902,984,1067,1139,1216,1293,1366,1444,1510,1629"}, "to": {"startLines": "38,39,59,60,61,65,66,124,125,126,127,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3766,3856,6239,6337,6437,6831,6914,13290,13377,13462,13544,13711,13783,13860,13937,14111,14189,14255", "endColumns": "89,81,97,99,85,82,90,86,84,81,82,71,76,76,72,77,65,118", "endOffsets": "3851,3933,6332,6432,6518,6909,7000,13372,13457,13539,13622,13778,13855,13932,14005,14184,14250,14369"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a7d2fcfebb99ca0b7d2351010b4b169\\transformed\\play-services-basement-18.4.0\\res\\values-in\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "127", "endOffsets": "322"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "4944", "endColumns": "131", "endOffsets": "5071"}}]}]}