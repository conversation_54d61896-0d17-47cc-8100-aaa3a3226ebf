package com.toika.netwok.ui.screens

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.grid.GridCells
import androidx.compose.foundation.lazy.grid.LazyVerticalGrid
import androidx.compose.foundation.lazy.grid.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.toika.netwok.data.models.LiveStream
import com.toika.netwok.data.models.StreamViewer
import com.toika.netwok.streaming.LiveStreamManager
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ActiveStreamScreen(
    stream: LiveStream,
    onEndStream: () -> Unit,
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val streamManager = remember { LiveStreamManager.getInstance(context) }
    val scope = rememberCoroutineScope()

    // منع الشاشة من الإغلاق أثناء البث
    DisposableEffect(Unit) {
        val activity = context as? android.app.Activity
        activity?.window?.addFlags(android.view.WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)

        onDispose {
            activity?.window?.clearFlags(android.view.WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        }
    }
    
    var viewerCount by remember { mutableStateOf(0) }
    var viewers by remember { mutableStateOf<List<StreamViewer>>(emptyList()) }
    var showViewersList by remember { mutableStateOf(false) }
    var isMicEnabled by remember { mutableStateOf(true) }
    var isScreenSharing by remember { mutableStateOf(false) }
    var isEndingStream by remember { mutableStateOf(false) }
    var showEndStreamDialog by remember { mutableStateOf(false) }
    var screenSharePermissionGranted by remember { mutableStateOf(false) }
    var errorMessage by remember { mutableStateOf<String?>(null) }
    var showErrorDialog by remember { mutableStateOf(false) }
    
    // طلب صلاحية مشاركة الشاشة
    val screenShareLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            screenSharePermissionGranted = true
            // لا نعين isScreenSharing = true هنا، سننتظر نتيجة العملية

            // بدء مشاركة الشاشة مع resultCode الصحيح
            scope.launch {
                val shareResult = streamManager.startScreenShare(result.resultCode, result.data)
                shareResult.fold(
                    onSuccess = {
                        isScreenSharing = true // ✅ فقط عند النجاح
                        android.util.Log.d("ActiveStreamScreen", "Screen sharing started successfully")
                    },
                    onFailure = { error ->
                        isScreenSharing = false // ❌ عند الفشل
                        errorMessage = "فشل في بدء مشاركة الشاشة: ${error.message}"
                        showErrorDialog = true
                        android.util.Log.e("ActiveStreamScreen", "Failed to start screen sharing: ${error.message}")
                    }
                )
            }
        } else {
            isScreenSharing = false
            android.util.Log.e("ActiveStreamScreen", "Screen capture permission denied, resultCode: ${result.resultCode}")
        }
    }

    // طلب صلاحية الميكروفون فقط
    val micPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // طلب صلاحية مشاركة الشاشة
            val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            val intent = mediaProjectionManager.createScreenCaptureIntent()
            screenShareLauncher.launch(intent)
        }
    }

    // مراقبة المشاهدين وبدء مشاركة الشاشة تلقائياً
    LaunchedEffect(stream.streamId) {
        streamManager.onViewerCountChanged = { count ->
            viewerCount = count
        }

        streamManager.getViewers(stream.streamId) { viewersList ->
            viewers = viewersList
        }

        // بدء الكاميرا تلقائياً عند دخول الشاشة (مجموعة فيديو)
        // لا نبدأ مشاركة الشاشة تلقائياً، بل ننتظر المستخدم

        // نظام heartbeat للحفاظ على البث نشط
        while (true) {
            try {
                val database = com.google.firebase.database.FirebaseDatabase.getInstance()
                database.getReference("live_streams")
                    .child(stream.streamId)
                    .child("lastHeartbeat")
                    .setValue(System.currentTimeMillis())

                database.getReference("live_streams")
                    .child(stream.streamId)
                    .child("isActive")
                    .setValue(true)

                android.util.Log.d("ActiveStreamScreen", "💓 Heartbeat sent for stream: ${stream.streamId}")
            } catch (e: Exception) {
                android.util.Log.e("ActiveStreamScreen", "❌ Heartbeat failed: ${e.message}")
            }

            kotlinx.coroutines.delay(30000) // كل 30 ثانية
        }
    }

    // مراقبة حالة مشاركة الشاشة
    DisposableEffect(Unit) {
        val receiver = object : android.content.BroadcastReceiver() {
            override fun onReceive(context: android.content.Context?, intent: android.content.Intent?) {
                when (intent?.action) {
                    com.toika.netwok.streaming.ScreenCaptureService.ACTION_SCREEN_SHARE_STARTED -> {
                        isScreenSharing = true
                        android.util.Log.d("ActiveStreamScreen", "Screen sharing confirmed started")
                    }
                    com.toika.netwok.streaming.ScreenCaptureService.ACTION_SCREEN_SHARE_FAILED -> {
                        isScreenSharing = false
                        val error = intent.getStringExtra("error_message") ?: "خطأ غير معروف"
                        errorMessage = "فشل في مشاركة الشاشة: $error"
                        showErrorDialog = true
                        android.util.Log.e("ActiveStreamScreen", "Screen sharing failed: $error")
                    }
                }
            }
        }

        val filter = android.content.IntentFilter().apply {
            addAction(com.toika.netwok.streaming.ScreenCaptureService.ACTION_SCREEN_SHARE_STARTED)
            addAction(com.toika.netwok.streaming.ScreenCaptureService.ACTION_SCREEN_SHARE_FAILED)
        }

        context.registerReceiver(receiver, filter, android.content.Context.RECEIVER_NOT_EXPORTED)

        onDispose {
            context.unregisterReceiver(receiver)
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        // عرض مجموعة الفيديو
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            // شبكة الفيديو للمشاركين
            Column(
                modifier = Modifier.fillMaxSize(),
                verticalArrangement = Arrangement.SpaceEvenly
            ) {
                // فيديو المضيف (أنت)
                Card(
                    modifier = Modifier
                        .fillMaxWidth()
                        .weight(1f)
                        .padding(8.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Gray.copy(alpha = 0.3f)
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = if (isScreenSharing) "🖥️" else "📹",
                                fontSize = 60.sp,
                                color = Color.White
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = if (isScreenSharing) "مشاركة الشاشة" else stream.hostName,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Bold,
                                color = Color.White
                            )

                            Text(
                                text = if (isScreenSharing) "نشطة" else "المضيف",
                                fontSize = 12.sp,
                                color = Color.White.copy(alpha = 0.8f)
                            )
                        }
                    }
                }

                // شبكة المشاهدين
                if (viewers.isNotEmpty()) {
                    LazyVerticalGrid(
                        columns = GridCells.Fixed(2),
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f)
                            .padding(8.dp),
                        verticalArrangement = Arrangement.spacedBy(8.dp),
                        horizontalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        items(viewers.take(4)) { viewer -> // أقصى 4 مشاهدين
                            Card(
                                colors = CardDefaults.cardColors(
                                    containerColor = Color.Blue.copy(alpha = 0.3f)
                                )
                            ) {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .aspectRatio(1f),
                                    contentAlignment = Alignment.Center
                                ) {
                                    Column(
                                        horizontalAlignment = Alignment.CenterHorizontally
                                    ) {
                                        Text(
                                            text = "👤",
                                            fontSize = 40.sp,
                                            color = Color.White
                                        )

                                        Spacer(modifier = Modifier.height(4.dp))

                                        Text(
                                            text = viewer.viewerName,
                                            fontSize = 12.sp,
                                            fontWeight = FontWeight.Medium,
                                            color = Color.White,
                                            maxLines = 1
                                        )
                                    }
                                }
                            }
                        }
                    }
                } else {
                    // لا يوجد مشاهدين
                    Box(
                        modifier = Modifier
                            .fillMaxWidth()
                            .weight(1f),
                        contentAlignment = Alignment.Center
                    ) {
                        Column(
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "👥",
                                fontSize = 48.sp,
                                color = Color.White.copy(alpha = 0.5f)
                            )

                            Spacer(modifier = Modifier.height(8.dp))

                            Text(
                                text = "في انتظار انضمام الأصدقاء...",
                                fontSize = 16.sp,
                                color = Color.White.copy(alpha = 0.7f)
                            )

                            Text(
                                text = "شارك كود الغرفة: ${stream.streamId.take(8).uppercase()}",
                                fontSize = 14.sp,
                                color = Color.White.copy(alpha = 0.6f)
                            )
                        }
                    }
                }
            }
        }
        
        // طبقة تحكم علوية
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // شريط علوي
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // زر الرجوع (لا ينهي البث)
                IconButton(
                    onClick = {
                        // الرجوع بدون إنهاء البث - البث يبقى نشط
                        onBackClick()
                    },
                    modifier = Modifier
                        .background(
                            Color.Black.copy(alpha = 0.5f),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "رجوع",
                        tint = Color.White
                    )
                }
                
                // عداد المشاهدين
                Card(
                    modifier = Modifier
                        .background(
                            Color.Red,
                            RoundedCornerShape(20.dp)
                        )
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Transparent
                    )
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = "🔴 LIVE",
                            color = Color.White,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "👥 $viewerCount",
                            color = Color.White,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
                
                // زر قائمة المشاهدين
                IconButton(
                    onClick = { showViewersList = true },
                    modifier = Modifier
                        .background(
                            Color.Black.copy(alpha = 0.5f),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.Person,
                        contentDescription = "المشاهدين",
                        tint = Color.White
                    )
                }
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            // معلومات البث
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stream.title,
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    if (stream.description.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = stream.description,
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 14.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "كود الغرفة: ${stream.streamId.take(8).uppercase()}",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // أزرار التحكم
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // زر الميكروفون
                IconButton(
                    onClick = {
                        isMicEnabled = !isMicEnabled
                        streamManager.toggleAudio(isMicEnabled)
                    },
                    modifier = Modifier
                        .background(
                            if (isMicEnabled) Color.White.copy(alpha = 0.2f)
                            else Color.Red.copy(alpha = 0.8f),
                            CircleShape
                        )
                        .size(56.dp)
                ) {
                    Text(
                        text = if (isMicEnabled) "🎤" else "🔇",
                        fontSize = 24.sp
                    )
                }
                
                // زر مشاركة الشاشة
                IconButton(
                    onClick = {
                        if (isScreenSharing) {
                            // إيقاف مشاركة الشاشة
                            isScreenSharing = false
                            scope.launch {
                                streamManager.stopScreenShare()
                            }
                        } else {
                            // طلب صلاحية الميكروفون أولاً
                            micPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
                        }
                    },
                    modifier = Modifier
                        .background(
                            if (isScreenSharing) Color.Green.copy(alpha = 0.8f)
                            else Color.White.copy(alpha = 0.2f),
                            CircleShape
                        )
                        .size(56.dp)
                ) {
                    Text(
                        text = if (isScreenSharing) "🖥️" else "📱",
                        fontSize = 24.sp
                    )
                }

                // زر إعدادات البث
                IconButton(
                    onClick = {
                        // إعدادات البث (جودة، إلخ)
                    },
                    modifier = Modifier
                        .background(
                            Color.White.copy(alpha = 0.2f),
                            CircleShape
                        )
                        .size(56.dp)
                ) {
                    Text(
                        text = "⚙️",
                        fontSize = 24.sp
                    )
                }

                // زر إنهاء البث
                IconButton(
                    onClick = { showEndStreamDialog = true },
                    modifier = Modifier
                        .background(
                            Color.Red.copy(alpha = 0.8f),
                            CircleShape
                        )
                        .size(56.dp)
                ) {
                    Text(
                        text = "📞",
                        fontSize = 24.sp,
                        color = Color.White
                    )
                }
            }
        }
    }
    
    // حوار قائمة المشاهدين
    if (showViewersList) {
        AlertDialog(
            onDismissRequest = { showViewersList = false },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "👥",
                        fontSize = 24.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("المشاهدين ($viewerCount)")
                }
            },
            text = {
                Column {
                    if (viewers.isEmpty()) {
                        Text("لا يوجد مشاهدين حالياً")
                    } else {
                        viewers.forEach { viewer ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    Icons.Default.Person,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(viewer.viewerName)
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showViewersList = false }) {
                    Text("إغلاق")
                }
            }
        )
    }
    
    // حوار إنهاء البث
    if (showEndStreamDialog) {
        AlertDialog(
            onDismissRequest = { showEndStreamDialog = false },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Warning,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.error
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("إنهاء البث")
                }
            },
            text = {
                Text("هل أنت متأكد من إنهاء البث المباشر؟ سيتم قطع الاتصال مع جميع المشاهدين.")
            },
            confirmButton = {
                Button(
                    onClick = {
                        scope.launch {
                            isEndingStream = true
                            streamManager.endLiveStream()
                            onEndStream()
                        }
                    },
                    enabled = !isEndingStream,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    if (isEndingStream) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text(if (isEndingStream) "جاري الإنهاء..." else "إنهاء البث")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showEndStreamDialog = false },
                    enabled = !isEndingStream
                ) {
                    Text("إلغاء")
                }
            }
        )
    }

    // حوار عرض الأخطاء
    if (showErrorDialog && errorMessage != null) {
        AlertDialog(
            onDismissRequest = {
                showErrorDialog = false
                errorMessage = null
            },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "❌",
                        fontSize = 24.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("خطأ في مشاركة الشاشة")
                }
            },
            text = {
                Column {
                    Text(errorMessage!!)
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "تأكد من منح جميع الصلاحيات المطلوبة وحاول مرة أخرى.",
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontSize = 14.sp
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showErrorDialog = false
                        errorMessage = null
                    }
                ) {
                    Text("حسناً")
                }
            }
        )
    }
}
