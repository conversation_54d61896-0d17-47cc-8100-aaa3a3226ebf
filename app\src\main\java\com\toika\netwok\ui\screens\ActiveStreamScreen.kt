package com.toika.netwok.ui.screens

import android.Manifest
import android.app.Activity
import android.content.Context
import android.content.Intent
import android.media.projection.MediaProjectionManager
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Warning
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalLifecycleOwner
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.ContextCompat
import com.toika.netwok.data.models.LiveStream
import com.toika.netwok.data.models.StreamViewer
import com.toika.netwok.streaming.LiveStreamManager
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ActiveStreamScreen(
    stream: LiveStream,
    onEndStream: () -> Unit,
    onBackClick: () -> Unit
) {
    val context = LocalContext.current
    val lifecycleOwner = LocalLifecycleOwner.current
    val streamManager = remember { LiveStreamManager.getInstance(context) }
    val scope = rememberCoroutineScope()
    
    var viewerCount by remember { mutableStateOf(0) }
    var viewers by remember { mutableStateOf<List<StreamViewer>>(emptyList()) }
    var showViewersList by remember { mutableStateOf(false) }
    var isMicEnabled by remember { mutableStateOf(true) }
    var isScreenSharing by remember { mutableStateOf(false) }
    var isEndingStream by remember { mutableStateOf(false) }
    var showEndStreamDialog by remember { mutableStateOf(false) }
    var screenSharePermissionGranted by remember { mutableStateOf(false) }
    
    // طلب صلاحية مشاركة الشاشة
    val screenShareLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == Activity.RESULT_OK) {
            screenSharePermissionGranted = true
            isScreenSharing = true
            // بدء مشاركة الشاشة
            scope.launch {
                streamManager.startScreenShare(result.data)
            }
        }
    }

    // طلب صلاحية الميكروفون فقط
    val micPermissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            // طلب صلاحية مشاركة الشاشة
            val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
            val intent = mediaProjectionManager.createScreenCaptureIntent()
            screenShareLauncher.launch(intent)
        }
    }

    // مراقبة المشاهدين
    LaunchedEffect(stream.streamId) {
        streamManager.onViewerCountChanged = { count ->
            viewerCount = count
        }

        streamManager.getViewers(stream.streamId) { viewersList ->
            viewers = viewersList
        }

        // طلب صلاحية الميكروفون عند بدء البث
        micPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        // عرض مشاركة الشاشة
        if (isScreenSharing) {
            // عرض مشاركة الشاشة النشطة
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "🖥️",
                        fontSize = 80.sp,
                        color = Color.White
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "مشاركة الشاشة نشطة",
                        fontSize = 24.sp,
                        fontWeight = FontWeight.Bold,
                        color = Color.White
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "يتم مشاركة شاشتك مع المشاهدين",
                        fontSize = 16.sp,
                        color = Color.White.copy(alpha = 0.8f)
                    )
                }
            }
        } else {
            // في انتظار بدء مشاركة الشاشة
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .background(Color.Black),
                contentAlignment = Alignment.Center
            ) {
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    CircularProgressIndicator(
                        color = Color.White,
                        modifier = Modifier.size(48.dp)
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    Text(
                        text = "جاري تحضير مشاركة الشاشة...",
                        fontSize = 18.sp,
                        color = Color.White
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    Text(
                        text = "يرجى منح الصلاحيات المطلوبة",
                        fontSize = 14.sp,
                        color = Color.White.copy(alpha = 0.7f)
                    )
                }
            }
        }
        
        // طبقة تحكم علوية
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // شريط علوي
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // زر الرجوع
                IconButton(
                    onClick = onBackClick,
                    modifier = Modifier
                        .background(
                            Color.Black.copy(alpha = 0.5f),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "رجوع",
                        tint = Color.White
                    )
                }
                
                // عداد المشاهدين
                Card(
                    modifier = Modifier
                        .background(
                            Color.Red,
                            RoundedCornerShape(20.dp)
                        )
                        .padding(horizontal = 12.dp, vertical = 6.dp),
                    colors = CardDefaults.cardColors(
                        containerColor = Color.Transparent
                    )
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                    ) {
                        Text(
                            text = "🔴 LIVE",
                            color = Color.White,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        Text(
                            text = "👥 $viewerCount",
                            color = Color.White,
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Bold
                        )
                    }
                }
                
                // زر قائمة المشاهدين
                IconButton(
                    onClick = { showViewersList = true },
                    modifier = Modifier
                        .background(
                            Color.Black.copy(alpha = 0.5f),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.Person,
                        contentDescription = "المشاهدين",
                        tint = Color.White
                    )
                }
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            // معلومات البث
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stream.title,
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    if (stream.description.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = stream.description,
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 14.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "كود الغرفة: ${stream.streamId.take(8).uppercase()}",
                        color = Color.White.copy(alpha = 0.8f),
                        fontSize = 12.sp
                    )
                }
            }
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // أزرار التحكم
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // زر الميكروفون
                IconButton(
                    onClick = {
                        isMicEnabled = !isMicEnabled
                        streamManager.toggleAudio(isMicEnabled)
                    },
                    modifier = Modifier
                        .background(
                            if (isMicEnabled) Color.White.copy(alpha = 0.2f)
                            else Color.Red.copy(alpha = 0.8f),
                            CircleShape
                        )
                        .size(56.dp)
                ) {
                    Text(
                        text = if (isMicEnabled) "🎤" else "🔇",
                        fontSize = 24.sp
                    )
                }
                
                // زر مشاركة الشاشة
                IconButton(
                    onClick = {
                        if (isScreenSharing) {
                            // إيقاف مشاركة الشاشة
                            isScreenSharing = false
                            scope.launch {
                                streamManager.stopScreenShare()
                            }
                        } else {
                            // بدء مشاركة الشاشة
                            val mediaProjectionManager = context.getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
                            val intent = mediaProjectionManager.createScreenCaptureIntent()
                            screenShareLauncher.launch(intent)
                        }
                    },
                    modifier = Modifier
                        .background(
                            if (isScreenSharing) Color.Green.copy(alpha = 0.8f)
                            else Color.White.copy(alpha = 0.2f),
                            CircleShape
                        )
                        .size(56.dp)
                ) {
                    Text(
                        text = if (isScreenSharing) "🖥️" else "📱",
                        fontSize = 24.sp
                    )
                }

                // زر إعدادات البث
                IconButton(
                    onClick = {
                        // إعدادات البث (جودة، إلخ)
                    },
                    modifier = Modifier
                        .background(
                            Color.White.copy(alpha = 0.2f),
                            CircleShape
                        )
                        .size(56.dp)
                ) {
                    Text(
                        text = "⚙️",
                        fontSize = 24.sp
                    )
                }

                // زر إنهاء البث
                IconButton(
                    onClick = { showEndStreamDialog = true },
                    modifier = Modifier
                        .background(
                            Color.Red.copy(alpha = 0.8f),
                            CircleShape
                        )
                        .size(56.dp)
                ) {
                    Text(
                        text = "📞",
                        fontSize = 24.sp,
                        color = Color.White
                    )
                }
            }
        }
    }
    
    // حوار قائمة المشاهدين
    if (showViewersList) {
        AlertDialog(
            onDismissRequest = { showViewersList = false },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "👥",
                        fontSize = 24.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("المشاهدين ($viewerCount)")
                }
            },
            text = {
                Column {
                    if (viewers.isEmpty()) {
                        Text("لا يوجد مشاهدين حالياً")
                    } else {
                        viewers.forEach { viewer ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    Icons.Default.Person,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(viewer.viewerName)
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showViewersList = false }) {
                    Text("إغلاق")
                }
            }
        )
    }
    
    // حوار إنهاء البث
    if (showEndStreamDialog) {
        AlertDialog(
            onDismissRequest = { showEndStreamDialog = false },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        Icons.Default.Warning,
                        contentDescription = null,
                        tint = MaterialTheme.colorScheme.error
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("إنهاء البث")
                }
            },
            text = {
                Text("هل أنت متأكد من إنهاء البث المباشر؟ سيتم قطع الاتصال مع جميع المشاهدين.")
            },
            confirmButton = {
                Button(
                    onClick = {
                        scope.launch {
                            isEndingStream = true
                            streamManager.endLiveStream()
                            onEndStream()
                        }
                    },
                    enabled = !isEndingStream,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = MaterialTheme.colorScheme.error
                    )
                ) {
                    if (isEndingStream) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = Color.White
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text(if (isEndingStream) "جاري الإنهاء..." else "إنهاء البث")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showEndStreamDialog = false },
                    enabled = !isEndingStream
                ) {
                    Text("إلغاء")
                }
            }
        )
    }
}
