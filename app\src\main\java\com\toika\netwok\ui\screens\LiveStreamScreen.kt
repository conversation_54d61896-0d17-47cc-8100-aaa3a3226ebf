package com.toika.netwok.ui.screens

import android.Manifest
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Share
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import kotlinx.coroutines.launch
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.toika.netwok.streaming.LiveStreamManager
import com.toika.netwok.data.models.LiveStream

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LiveStreamScreen(
    onBackClick: () -> Unit,
    onStreamStarted: (LiveStream) -> Unit = {}
) {
    val context = LocalContext.current
    val streamManager = remember { LiveStreamManager(context) }
    val scope = rememberCoroutineScope()

    var showStartStreamDialog by remember { mutableStateOf(false) }
    var streamTitle by remember { mutableStateOf("") }
    var streamDescription by remember { mutableStateOf("") }
    var isPrivateStream by remember { mutableStateOf(false) }
    var isLoading by remember { mutableStateOf(false) }
    var hasPermissions by remember { mutableStateOf(false) }
    var activeStreams by remember { mutableStateOf<List<LiveStream>>(emptyList()) }

    val snackbarHostState = remember { SnackbarHostState() }

    // طلب الصلاحيات
    val permissionLauncher = rememberLauncherForActivityResult(
        ActivityResultContracts.RequestMultiplePermissions()
    ) { permissions ->
        hasPermissions = permissions[Manifest.permission.CAMERA] == true &&
                permissions[Manifest.permission.RECORD_AUDIO] == true
    }

    // تحميل البثوث النشطة
    LaunchedEffect(Unit) {
        permissionLauncher.launch(
            arrayOf(
                Manifest.permission.CAMERA,
                Manifest.permission.RECORD_AUDIO
            )
        )

        streamManager.getActiveStreams { streams ->
            activeStreams = streams
        }
    }

    Scaffold(
        snackbarHost = { SnackbarHost(snackbarHostState) }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp)
        ) {
        // شريط علوي
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "رجوع",
                    tint = MaterialTheme.colorScheme.primary
                )
            }

            Spacer(modifier = Modifier.width(8.dp))

            Text(
                text = "البث المباشر",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary,
                modifier = Modifier.weight(1f)
            )

            // زر ابدأ بث مباشر
            Button(
                onClick = { showStartStreamDialog = true },
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Icon(
                    Icons.Default.PlayArrow,
                    contentDescription = null,
                    modifier = Modifier.size(18.dp)
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("ابدأ بث")
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // المحتوى الرئيسي - حالة فارغة
        Column(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // أيقونة البث
            Text(
                text = "📺",
                fontSize = 80.sp
            )
            
            Spacer(modifier = Modifier.height(24.dp))
            
            Text(
                text = "لا توجد بث من الأصدقاء",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.onSurface,
                textAlign = TextAlign.Center
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            Text(
                text = "عندما يبدأ أصدقاؤك البث المباشر، ستظهر هنا",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = 32.dp)
            )
            
            Spacer(modifier = Modifier.height(32.dp))
            
            // معلومات إضافية
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(20.dp),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "💡 نصيحة",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Text(
                        text = "أضف أصدقاء جدد لمشاهدة بثهم المباشر والتفاعل معهم",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        textAlign = TextAlign.Center
                    )
                }
            }
        }
    }

    // نافذة حوار بدء البث
    if (showStartStreamDialog) {
        AlertDialog(
            onDismissRequest = {
                showStartStreamDialog = false
                streamTitle = ""
            },
            title = {
                Text(
                    text = "ابدأ بث مباشر",
                    fontWeight = FontWeight.Bold
                )
            },
            text = {
                Column {
                    Text(
                        text = "أدخل اسم البث:",
                        fontSize = 16.sp,
                        modifier = Modifier.padding(bottom = 8.dp)
                    )

                    OutlinedTextField(
                        value = streamTitle,
                        onValueChange = { streamTitle = it },
                        label = { Text("اسم البث") },
                        placeholder = { Text("مثال: بث مباشر مع الأصدقاء") },
                        modifier = Modifier.fillMaxWidth(),
                        singleLine = true
                    )

                    Spacer(modifier = Modifier.height(12.dp))

                    OutlinedTextField(
                        value = streamDescription,
                        onValueChange = { streamDescription = it },
                        label = { Text("وصف البث (اختياري)") },
                        placeholder = { Text("أدخل وصف البث") },
                        modifier = Modifier.fillMaxWidth(),
                        maxLines = 2
                    )

                    Spacer(modifier = Modifier.height(16.dp))

                    // خيار البث الخاص
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Column {
                            Text(
                                text = "بث خاص",
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                            Text(
                                text = "فقط الأصدقاء يمكنهم المشاهدة",
                                fontSize = 12.sp,
                                color = MaterialTheme.colorScheme.onSurfaceVariant
                            )
                        }
                        Switch(
                            checked = isPrivateStream,
                            onCheckedChange = { isPrivateStream = it }
                        )
                    }
                }
            },
            confirmButton = {
                Button(
                    onClick = {
                        if (!hasPermissions) {
                            scope.launch {
                                snackbarHostState.showSnackbar(
                                    message = "يرجى منح صلاحيات الكاميرا والميكروفون",
                                    duration = SnackbarDuration.Short
                                )
                            }
                            return@Button
                        }

                        if (streamTitle.isBlank()) {
                            scope.launch {
                                snackbarHostState.showSnackbar(
                                    message = "يرجى إدخال اسم البث",
                                    duration = SnackbarDuration.Short
                                )
                            }
                            return@Button
                        }

                        scope.launch {
                            isLoading = true

                            val result = streamManager.startLiveStream(
                                title = streamTitle,
                                description = streamDescription,
                                isPrivate = isPrivateStream
                            )

                            result.fold(
                                onSuccess = { stream ->
                                    showStartStreamDialog = false
                                    streamTitle = ""
                                    streamDescription = ""
                                    isPrivateStream = false
                                    onStreamStarted(stream)
                                    snackbarHostState.showSnackbar(
                                        message = "تم بدء البث بنجاح!",
                                        duration = SnackbarDuration.Short
                                    )
                                },
                                onFailure = { error ->
                                    snackbarHostState.showSnackbar(
                                        message = error.message ?: "فشل في بدء البث",
                                        duration = SnackbarDuration.Short
                                    )
                                }
                            )

                            isLoading = false
                        }
                    },
                    enabled = streamTitle.isNotBlank() && !isLoading && hasPermissions
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(16.dp),
                            color = MaterialTheme.colorScheme.onPrimary
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                    }
                    Text(if (isLoading) "جاري البدء..." else "ابدأ البث")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = {
                        showStartStreamDialog = false
                        streamTitle = ""
                    }
                ) {
                    Text("إلغاء")
                }
            }
        )
    }
    }
}
