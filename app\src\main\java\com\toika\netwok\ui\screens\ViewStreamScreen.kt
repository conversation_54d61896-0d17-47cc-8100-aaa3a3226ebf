package com.toika.netwok.ui.screens

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.toika.netwok.data.models.LiveStream
import com.toika.netwok.data.models.StreamMessage
import com.toika.netwok.data.models.StreamViewer
import com.toika.netwok.streaming.LiveStreamManager
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ViewStreamScreen(
    stream: LiveStream,
    onBackClick: () -> Unit,
    onLeaveStream: () -> Unit
) {
    val context = LocalContext.current
    val streamManager = remember { LiveStreamManager.getInstance(context) }
    val scope = rememberCoroutineScope()
    
    var viewerCount by remember { mutableStateOf(stream.viewerCount) }
    var viewers by remember { mutableStateOf<List<StreamViewer>>(emptyList()) }
    var messages by remember { mutableStateOf<List<StreamMessage>>(emptyList()) }
    var showViewersList by remember { mutableStateOf(false) }
    var showChat by remember { mutableStateOf(false) }
    var newMessage by remember { mutableStateOf("") }
    var isJoined by remember { mutableStateOf(false) }
    
    // انضمام للبث عند فتح الشاشة مع Firebase الحقيقي
    LaunchedEffect(stream.streamId) {
        val result = streamManager.joinStream(stream.streamId)
        result.fold(
            onSuccess = { joinedStream ->
                isJoined = true

                // مراقبة المشاهدين من Firebase
                streamManager.onViewerCountChanged = { count ->
                    viewerCount = count
                }

                streamManager.getViewers(stream.streamId) { viewersList ->
                    viewers = viewersList
                }

                // مراقبة الرسائل من Firebase
                streamManager.onNewMessage = { message ->
                    messages = messages + message
                }

                // بدء مراقبة التحديثات المباشرة
                streamManager.startViewerCountMonitoring(stream.streamId)
            },
            onFailure = { error ->
                // عرض رسالة الخطأ
                scope.launch {
                    // يمكن إضافة SnackBar هنا لعرض الخطأ
                }
                onBackClick()
            }
        )
    }
    
    // مغادرة البث عند إغلاق الشاشة
    DisposableEffect(Unit) {
        onDispose {
            if (isJoined) {
                scope.launch {
                    streamManager.leaveStream(stream.streamId)
                }
            }
        }
    }
    
    Box(modifier = Modifier.fillMaxSize()) {
        // منطقة عرض البث
        Box(
            modifier = Modifier
                .fillMaxSize()
                .background(Color.Black)
        ) {
            // محاكاة عرض البث (في المستقبل سيكون فيديو حقيقي)
            Column(
                modifier = Modifier.fillMaxSize(),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = "🖥️",
                    fontSize = 120.sp,
                    color = Color.White
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Text(
                    text = "مشاهدة البث المباشر",
                    fontSize = 24.sp,
                    fontWeight = FontWeight.Bold,
                    color = Color.White
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "يتم عرض شاشة ${stream.hostName}",
                    fontSize = 16.sp,
                    color = Color.White.copy(alpha = 0.8f)
                )
            }
        }
        
        // طبقة التحكم العلوية
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(16.dp)
        ) {
            // شريط علوي
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // زر الرجوع
                IconButton(
                    onClick = {
                        scope.launch {
                            if (isJoined) {
                                streamManager.leaveStream(stream.streamId)
                            }
                            onLeaveStream()
                            onBackClick()
                        }
                    },
                    modifier = Modifier
                        .background(
                            Color.Black.copy(alpha = 0.5f),
                            CircleShape
                        )
                ) {
                    Icon(
                        Icons.Default.ArrowBack,
                        contentDescription = "مغادرة البث",
                        tint = Color.White
                    )
                }
                
                // معلومات البث
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    // شارة LIVE
                    Card(
                        modifier = Modifier
                            .background(
                                Color.Red,
                                RoundedCornerShape(20.dp)
                            )
                            .padding(horizontal = 12.dp, vertical = 6.dp),
                        colors = CardDefaults.cardColors(
                            containerColor = Color.Transparent
                        )
                    ) {
                        Row(
                            verticalAlignment = Alignment.CenterVertically,
                            modifier = Modifier.padding(horizontal = 8.dp, vertical = 4.dp)
                        ) {
                            Text(
                                text = "🔴 LIVE",
                                color = Color.White,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold
                            )
                            
                            Spacer(modifier = Modifier.width(8.dp))
                            
                            Text(
                                text = "👥 $viewerCount",
                                color = Color.White,
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Bold
                            )
                        }
                    }
                    
                    Spacer(modifier = Modifier.height(4.dp))
                    
                    Text(
                        text = stream.hostName,
                        color = Color.White,
                        fontSize = 14.sp,
                        fontWeight = FontWeight.Medium
                    )
                }
                
                // أزرار التحكم
                Row {
                    // زر قائمة المشاهدين
                    IconButton(
                        onClick = { showViewersList = true },
                        modifier = Modifier
                            .background(
                                Color.Black.copy(alpha = 0.5f),
                                CircleShape
                            )
                    ) {
                        Icon(
                            Icons.Default.Person,
                            contentDescription = "المشاهدين",
                            tint = Color.White
                        )
                    }
                    
                    // زر الدردشة
                    IconButton(
                        onClick = { showChat = !showChat },
                        modifier = Modifier
                            .background(
                                if (showChat) Color.Blue.copy(alpha = 0.8f)
                                else Color.Black.copy(alpha = 0.5f),
                                CircleShape
                            )
                    ) {
                        Text(
                            text = "💬",
                            fontSize = 20.sp,
                            color = Color.White
                        )
                    }
                }
            }
            
            Spacer(modifier = Modifier.weight(1f))
            
            // معلومات البث
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.7f)
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = stream.title,
                        color = Color.White,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    if (stream.description.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = stream.description,
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 14.sp
                        )
                    }
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Text(
                            text = "كود الغرفة: ${stream.streamId.take(8).uppercase()}",
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 12.sp
                        )
                        
                        val duration = (System.currentTimeMillis() - stream.startTime) / 1000 / 60
                        Text(
                            text = "المدة: ${duration}m",
                            color = Color.White.copy(alpha = 0.8f),
                            fontSize = 12.sp
                        )
                    }
                }
            }
        }
        
        // نافذة الدردشة
        if (showChat) {
            Card(
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(16.dp)
                    .width(300.dp)
                    .height(400.dp),
                colors = CardDefaults.cardColors(
                    containerColor = Color.Black.copy(alpha = 0.9f)
                )
            ) {
                Column(
                    modifier = Modifier.fillMaxSize()
                ) {
                    // عنوان الدردشة
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(12.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "💬 الدردشة",
                            color = Color.White,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Bold
                        )
                        
                        TextButton(
                            onClick = { showChat = false }
                        ) {
                            Text("✕", color = Color.White)
                        }
                    }
                    
                    Divider(color = Color.White.copy(alpha = 0.3f))
                    
                    // قائمة الرسائل
                    LazyColumn(
                        modifier = Modifier
                            .weight(1f)
                            .padding(8.dp),
                        reverseLayout = true
                    ) {
                        items(messages.reversed()) { message ->
                            MessageItem(message = message)
                        }
                    }
                    
                    Divider(color = Color.White.copy(alpha = 0.3f))
                    
                    // حقل إرسال الرسالة
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        OutlinedTextField(
                            value = newMessage,
                            onValueChange = { newMessage = it },
                            placeholder = { 
                                Text(
                                    "اكتب رسالة...", 
                                    color = Color.White.copy(alpha = 0.6f)
                                ) 
                            },
                            modifier = Modifier.weight(1f),
                            colors = OutlinedTextFieldDefaults.colors(
                                focusedTextColor = Color.White,
                                unfocusedTextColor = Color.White,
                                focusedBorderColor = Color.White,
                                unfocusedBorderColor = Color.White.copy(alpha = 0.5f)
                            ),
                            singleLine = true
                        )
                        
                        Spacer(modifier = Modifier.width(8.dp))
                        
                        IconButton(
                            onClick = {
                                if (newMessage.isNotBlank()) {
                                    scope.launch {
                                        streamManager.sendMessage(stream.streamId, newMessage)
                                        newMessage = ""
                                    }
                                }
                            },
                            enabled = newMessage.isNotBlank()
                        ) {
                            Icon(
                                Icons.Default.Send,
                                contentDescription = "إرسال",
                                tint = if (newMessage.isNotBlank()) Color.Blue else Color.Gray
                            )
                        }
                    }
                }
            }
        }
    }
    
    // حوار قائمة المشاهدين
    if (showViewersList) {
        AlertDialog(
            onDismissRequest = { showViewersList = false },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "👥",
                        fontSize = 24.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("المشاهدين ($viewerCount)")
                }
            },
            text = {
                LazyColumn {
                    if (viewers.isEmpty()) {
                        item {
                            Text("لا يوجد مشاهدين حالياً")
                        }
                    } else {
                        items(viewers) { viewer ->
                            Row(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .padding(vertical = 4.dp),
                                verticalAlignment = Alignment.CenterVertically
                            ) {
                                Icon(
                                    Icons.Default.Person,
                                    contentDescription = null,
                                    modifier = Modifier.size(20.dp)
                                )
                                Spacer(modifier = Modifier.width(8.dp))
                                Text(viewer.viewerName)
                            }
                        }
                    }
                }
            },
            confirmButton = {
                TextButton(onClick = { showViewersList = false }) {
                    Text("إغلاق")
                }
            }
        )
    }
}

@Composable
private fun MessageItem(message: StreamMessage) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = Color.White.copy(alpha = 0.1f)
        )
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            Text(
                text = message.senderName,
                color = Color.Blue,
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold
            )
            Text(
                text = message.message,
                color = Color.White,
                fontSize = 14.sp
            )
        }
    }
}
