package com.toika.netwok.ui.screens

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import com.google.firebase.auth.FirebaseAuth
import com.toika.netwok.auth.AuthenticationManager

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onSignOut: () -> Unit,
    onNavigateToFriends: () -> Unit,
    onNavigateToProfile: () -> Unit,
    onNavigateToActiveStream: (String) -> Unit = {},
    onNavigateToViewStream: (String) -> Unit = {}
) {
    val context = LocalContext.current
    val authManager = remember { AuthenticationManager(context) }
    val scope = rememberCoroutineScope()

    var showSignOutDialog by remember { mutableStateOf(false) }
    var roomCode by remember { mutableStateOf("") }
    var userFromDatabase by remember { mutableStateOf<com.toika.netwok.data.models.User?>(null) }
    var showRoomNotFoundDialog by remember { mutableStateOf(false) }

    // الحصول على معلومات المستخدم من قاعدة البيانات
    LaunchedEffect(Unit) {
        scope.launch {
            userFromDatabase = authManager.getUserFromDatabase()
        }
    }

    // الحصول على اسم المستخدم
    val displayName = userFromDatabase?.displayName ?: "المستخدم"
    
    Column(
        modifier = Modifier.fillMaxSize()
    ) {
        // المحتوى الرئيسي
        Column(
            modifier = Modifier
                .weight(1f)
                .padding(24.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center
        ) {
            // أيقونة الفيديو
            Card(
                modifier = Modifier
                    .size(120.dp)
                    .clip(CircleShape),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer
                )
            ) {
                Box(
                    modifier = Modifier.fillMaxSize(),
                    contentAlignment = Alignment.Center
                ) {
                    Text(
                        text = "📹",
                        fontSize = 60.sp
                    )
                }
            }

            Spacer(modifier = Modifier.height(32.dp))

            // رسالة الترحيب
            Text(
                text = "مرحباً، $displayName",
                fontSize = 28.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(16.dp))

            Text(
                text = "انضم إلى غرفة فيديو",
                fontSize = 18.sp,
                fontWeight = FontWeight.Medium,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(32.dp))

            // حقل كود الغرفة
            OutlinedTextField(
                value = roomCode,
                onValueChange = { roomCode = it },
                label = { Text("كود الغرفة") },
                placeholder = { Text("أدخل كود الغرفة") },
                modifier = Modifier.fillMaxWidth(),
                singleLine = true,
                keyboardOptions = KeyboardOptions(keyboardType = KeyboardType.Text),
                leadingIcon = {
                    Text(
                        text = "🔑",
                        fontSize = 20.sp
                    )
                }
            )

            Spacer(modifier = Modifier.height(24.dp))

            // زر الانضمام
            Button(
                onClick = {
                    if (roomCode.isNotBlank()) {
                        // البحث عن الغرفة في Firebase
                        scope.launch {
                            searchForRoom(
                                roomCode = roomCode.trim(),
                                onRoomFound = { stream ->
                                    // الانضمام للغرفة والانتقال لشاشة المشاهدة
                                    scope.launch {
                                        joinRoom(stream)
                                        onNavigateToViewStream(stream.streamId)
                                    }
                                },
                                onRoomNotFound = {
                                    showRoomNotFoundDialog = true
                                }
                            )
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                enabled = roomCode.isNotBlank(),
                colors = ButtonDefaults.buttonColors(
                    containerColor = MaterialTheme.colorScheme.primary
                )
            ) {
                Text(
                    text = "📹",
                    fontSize = 24.sp
                )

                Spacer(modifier = Modifier.width(12.dp))

                Text(
                    text = "انضمام للغرفة",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // نص توضيحي
            Text(
                text = "اطلب من صديقك مشاركة كود الغرفة معك",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )

            Spacer(modifier = Modifier.height(32.dp))

            // فاصل
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Divider(modifier = Modifier.weight(1f))
                Text(
                    text = "أو",
                    modifier = Modifier.padding(horizontal = 16.dp),
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontSize = 14.sp
                )
                Divider(modifier = Modifier.weight(1f))
            }

            Spacer(modifier = Modifier.height(24.dp))

            // زر ابدأ بث
            OutlinedButton(
                onClick = {
                    // بدء البث والانتقال لشاشة البث النشط
                    scope.launch {
                        val streamId = startNewStream()
                        if (streamId != null) {
                            onNavigateToActiveStream(streamId)
                        }
                    }
                },
                modifier = Modifier
                    .fillMaxWidth()
                    .height(56.dp),
                colors = ButtonDefaults.outlinedButtonColors(
                    containerColor = MaterialTheme.colorScheme.primaryContainer,
                    contentColor = MaterialTheme.colorScheme.primary
                ),
                border = ButtonDefaults.outlinedButtonBorder.copy(
                    width = 2.dp,
                    brush = androidx.compose.ui.graphics.SolidColor(MaterialTheme.colorScheme.primary)
                )
            ) {
                Text(
                    text = "📺",
                    fontSize = 24.sp
                )

                Spacer(modifier = Modifier.width(12.dp))

                Text(
                    text = "إنشاء مجموعة فيديو",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Medium
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // نص توضيحي للمجموعة
            Text(
                text = "أنشئ مجموعة فيديو وادع أصدقائك",
                fontSize = 14.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant,
                textAlign = TextAlign.Center
            )
        }

        // شريط التنقل السفلي
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surface
            ),
            shape = RoundedCornerShape(topStart = 16.dp, topEnd = 16.dp)
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                horizontalArrangement = Arrangement.SpaceEvenly
            ) {
                // زر الأصدقاء
                BottomNavButton(
                    icon = "👥",
                    title = "الأصدقاء",
                    onClick = onNavigateToFriends
                )

                // زر الإعدادات
                BottomNavButton(
                    icon = "⚙️",
                    title = "الإعدادات",
                    onClick = onNavigateToProfile
                )
            }
        }
    }

    // حوار الغرفة غير موجودة
    if (showRoomNotFoundDialog) {
        AlertDialog(
            onDismissRequest = { showRoomNotFoundDialog = false },
            title = {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "❌",
                        fontSize = 24.sp
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text("الغرفة غير موجودة")
                }
            },
            text = {
                Column {
                    Text("كود الغرفة الذي أدخلته غير صحيح أو الغرفة غير متاحة.")
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = "تأكد من الكود وحاول مرة أخرى.",
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                }
            },
            confirmButton = {
                TextButton(
                    onClick = { showRoomNotFoundDialog = false }
                ) {
                    Text("حسناً")
                }
            }
        )
    }

    // حوار تأكيد تسجيل الخروج
    if (showSignOutDialog) {
        AlertDialog(
            onDismissRequest = { showSignOutDialog = false },
            title = {
                Text("تسجيل الخروج")
            },
            text = {
                Text("هل أنت متأكد من أنك تريد تسجيل الخروج؟")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        authManager.signOut()
                        showSignOutDialog = false
                        onSignOut()
                    }
                ) {
                    Text("نعم")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showSignOutDialog = false }
                ) {
                    Text("إلغاء")
                }
            }
        )
    }
}

@Composable
fun BottomNavButton(
    icon: String,
    title: String,
    onClick: () -> Unit
) {
    Column(
        horizontalAlignment = Alignment.CenterHorizontally,
        modifier = Modifier
            .clickable { onClick() }
            .padding(8.dp)
    ) {
        Text(
            text = icon,
            fontSize = 24.sp
        )

        Spacer(modifier = Modifier.height(4.dp))

        Text(
            text = title,
            fontSize = 12.sp,
            color = MaterialTheme.colorScheme.onSurface
        )
    }
}

// دالة البحث عن الغرفة في Firebase
suspend fun searchForRoom(
    roomCode: String,
    onRoomFound: (com.toika.netwok.data.models.LiveStream) -> Unit,
    onRoomNotFound: () -> Unit
) {
    try {
        android.util.Log.d("HomeScreen", "🔍 Searching for room with code: $roomCode")
        val database = com.google.firebase.database.FirebaseDatabase.getInstance()

        // البحث في جميع البثوث (بدون فلترة أولاً)
        val streamsSnapshot = database.getReference("live_streams")
            .get()
            .await()

        android.util.Log.d("HomeScreen", "📊 Found ${streamsSnapshot.childrenCount} total streams")

        var roomFound = false

        for (streamSnapshot in streamsSnapshot.children) {
            val stream = streamSnapshot.getValue(com.toika.netwok.data.models.LiveStream::class.java)
            if (stream != null) {
                android.util.Log.d("HomeScreen", "🔍 Checking stream: ${stream.streamId}")
                android.util.Log.d("HomeScreen", "📋 Stream details: isActive=${stream.isActive}, title=${stream.title}")

                // التحقق من أن البث نشط أولاً
                if (stream.isActive) {
                    // التحقق من كود الغرفة (أول 8 أحرف من streamId)
                    val streamRoomCode = stream.streamId.take(8).uppercase()
                    val inputCode = roomCode.uppercase().trim()

                    android.util.Log.d("HomeScreen", "🔑 Comparing codes: '$streamRoomCode' vs '$inputCode'")

                    if (streamRoomCode == inputCode) {
                        android.util.Log.d("HomeScreen", "✅ Room found! Stream: ${stream.streamId}")
                        roomFound = true
                        onRoomFound(stream)
                        return
                    }
                } else {
                    android.util.Log.d("HomeScreen", "⏸️ Stream ${stream.streamId} is not active")
                }
            } else {
                android.util.Log.d("HomeScreen", "❌ Failed to parse stream data")
            }
        }

        if (!roomFound) {
            android.util.Log.d("HomeScreen", "❌ Room not found for code: $roomCode")
            android.util.Log.d("HomeScreen", "🔄 Trying alternative search method...")

            // طريقة بديلة: البحث المباشر بالكود
            tryDirectSearch(roomCode, onRoomFound, onRoomNotFound)
        }

    } catch (e: Exception) {
        android.util.Log.e("HomeScreen", "💥 Error searching for room: ${e.message}")
        e.printStackTrace()
        onRoomNotFound()
    }
}

// دالة البحث البديلة
suspend fun tryDirectSearch(
    roomCode: String,
    onRoomFound: (com.toika.netwok.data.models.LiveStream) -> Unit,
    onRoomNotFound: () -> Unit
) {
    try {
        android.util.Log.d("HomeScreen", "🔍 Direct search for code: $roomCode")
        val database = com.google.firebase.database.FirebaseDatabase.getInstance()

        // البحث في جميع الغرف النشطة بطريقة مختلفة
        val activeStreamsRef = database.getReference("live_streams")

        // استخدام listener للبحث المباشر
        activeStreamsRef.addListenerForSingleValueEvent(object : com.google.firebase.database.ValueEventListener {
            override fun onDataChange(snapshot: com.google.firebase.database.DataSnapshot) {
                android.util.Log.d("HomeScreen", "📊 Direct search found ${snapshot.childrenCount} streams")

                var found = false
                for (child in snapshot.children) {
                    try {
                        val streamData = child.getValue(com.toika.netwok.data.models.LiveStream::class.java)
                        if (streamData != null && streamData.isActive) {
                            val code = streamData.streamId.take(8).uppercase()
                            android.util.Log.d("HomeScreen", "🔑 Direct check: '$code' vs '${roomCode.uppercase()}'")

                            if (code == roomCode.uppercase().trim()) {
                                android.util.Log.d("HomeScreen", "✅ Direct search found room!")
                                found = true
                                onRoomFound(streamData)
                                return
                            }
                        }
                    } catch (e: Exception) {
                        android.util.Log.e("HomeScreen", "Error parsing stream in direct search: ${e.message}")
                    }
                }

                if (!found) {
                    android.util.Log.d("HomeScreen", "❌ Direct search also failed")
                    onRoomNotFound()
                }
            }

            override fun onCancelled(error: com.google.firebase.database.DatabaseError) {
                android.util.Log.e("HomeScreen", "Direct search cancelled: ${error.message}")
                onRoomNotFound()
            }
        })

    } catch (e: Exception) {
        android.util.Log.e("HomeScreen", "Error in direct search: ${e.message}")
        onRoomNotFound()
    }
}

// دالة الانضمام للغرفة
suspend fun joinRoom(stream: com.toika.netwok.data.models.LiveStream) {
    try {
        val currentUser = com.google.firebase.auth.FirebaseAuth.getInstance().currentUser
        if (currentUser != null) {
            // إضافة المستخدم كمشاهد
            val viewer = com.toika.netwok.data.models.StreamViewer(
                viewerId = currentUser.uid,
                viewerName = currentUser.displayName ?: "مشاهد",
                joinedAt = System.currentTimeMillis()
            )

            val database = com.google.firebase.database.FirebaseDatabase.getInstance()
            database.getReference("live_streams")
                .child(stream.streamId)
                .child("viewers")
                .child(currentUser.uid)
                .setValue(viewer)
                .await()

            android.util.Log.d("HomeScreen", "Successfully joined room: ${stream.streamId}")
        }
    } catch (e: Exception) {
        android.util.Log.e("HomeScreen", "Error joining room: ${e.message}")
    }
}

// دالة بدء بث جديد
suspend fun startNewStream(): String? {
    return try {
        val currentUser = com.google.firebase.auth.FirebaseAuth.getInstance().currentUser
        if (currentUser != null) {
            // إنشاء بث جديد
            val streamId = generateStreamId()
            val stream = com.toika.netwok.data.models.LiveStream(
                streamId = streamId,
                hostId = currentUser.uid,
                hostName = currentUser.displayName ?: "مضيف",
                title = "مجموعة فيديو",
                description = "مكالمة فيديو جماعية",
                isActive = true,
                startTime = System.currentTimeMillis(),
                viewerCount = 0,
                isPrivate = false,
                chatEnabled = true,
                recordingEnabled = false
            )

            // حفظ البث في Firebase
            val database = com.google.firebase.database.FirebaseDatabase.getInstance()
            database.getReference("live_streams")
                .child(streamId)
                .setValue(stream)
                .await()

            // حفظ البث في LiveStreamManager أيضاً
            val context = com.toika.netwok.MyApplication.instance
            val streamManager = com.toika.netwok.streaming.LiveStreamManager.getInstance(context)
            streamManager.setCurrentStream(stream)

            android.util.Log.d("HomeScreen", "✅ New stream created successfully!")
            android.util.Log.d("HomeScreen", "📋 Stream ID: $streamId")
            android.util.Log.d("HomeScreen", "🔑 Room Code: ${streamId.take(8).uppercase()}")
            android.util.Log.d("HomeScreen", "👤 Host: ${currentUser.displayName ?: "مضيف"}")
            android.util.Log.d("HomeScreen", "🔴 Is Active: true")

            streamId // إرجاع streamId للتنقل
        } else {
            null
        }
    } catch (e: Exception) {
        android.util.Log.e("HomeScreen", "Error creating stream: ${e.message}")
        null
    }
}

// دالة إنشاء معرف البث
fun generateStreamId(): String {
    val chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
    return (1..16)
        .map { chars.random() }
        .joinToString("")
}
