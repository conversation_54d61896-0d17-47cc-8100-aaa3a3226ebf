/ Header Record For PersistentHashMapValueStorage< ;app/src/main/java/com/web22/myapplication/FirebaseHelper.kt: 9app/src/main/java/com/web22/myapplication/MainActivity.kt; :app/src/main/java/com/web22/myapplication/MyApplication.ktH Gapp/src/main/java/com/web22/myapplication/auth/AuthenticationManager.ktA @app/src/main/java/com/web22/myapplication/data/FriendsManager.kt> =app/src/main/java/com/web22/myapplication/data/models/User.ktF Eapp/src/main/java/com/web22/myapplication/navigation/AppNavigation.ktH Gapp/src/main/java/com/web22/myapplication/ui/screens/AddFriendScreen.ktF Eapp/src/main/java/com/web22/myapplication/ui/screens/FriendsScreen.ktE Dapp/src/main/java/com/web22/myapplication/ui/screens/GroupsScreen.ktC Bapp/src/main/java/com/web22/myapplication/ui/screens/HomeScreen.ktI Happ/src/main/java/com/web22/myapplication/ui/screens/LiveStreamScreen.ktD Capp/src/main/java/com/web22/myapplication/ui/screens/LoginScreen.ktF Eapp/src/main/java/com/web22/myapplication/ui/screens/ProfileScreen.ktG Fapp/src/main/java/com/web22/myapplication/ui/screens/RegisterScreen.kt< ;app/src/main/java/com/web22/myapplication/ui/theme/Color.kt< ;app/src/main/java/com/web22/myapplication/ui/theme/Theme.kt; :app/src/main/java/com/web22/myapplication/ui/theme/Type.kt