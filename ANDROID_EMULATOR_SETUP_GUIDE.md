# 📱 دليل تنزيل وإعداد محاكي Android سريع وجديد

## 🚀 **الطريقة الأسرع - استخدام Android Studio:**

### 1. **تنزيل Android Studio:**
- اذهب إلى: https://developer.android.com/studio
- اضغط "Download Android Studio"
- اختر النسخة المناسبة لنظام التشغيل
- حجم التنزيل: ~1GB

### 2. **تثبيت Android Studio:**
- شغل ملف التثبيت
- اتبع خطوات التثبيت
- اختر "Standard Installation"
- انتظر تنزيل SDK Components

### 3. **إنشاء محاكي جديد:**
```
1. افتح Android Studio
2. اضغط "More Actions" → "AVD Manager"
3. اضغط "Create Virtual Device"
4. اختر جهاز: Pixel 7 Pro (مُوصى به)
5. اختر نظام: API 34 (Android 14) - الأحدث
6. اضغ<PERSON> "Download" إذا لم يكن مثبت
7. اضغط "Next" → "Finish"
```

### 4. **إعدادات المحاكي للسرعة:**
```
في Advanced Settings:
- RAM: 4096 MB (أو أكثر)
- VM Heap: 512 MB
- Internal Storage: 8 GB
- Graphics: Hardware - GLES 2.0
- Multi-Core CPU: 4 cores
```

## ⚡ **الطريقة البديلة - BlueStacks (أسرع للاختبار):**

### 1. **تنزيل BlueStacks:**
- اذهب إلى: https://www.bluestacks.com
- اضغط "Download BlueStacks"
- حجم التنزيل: ~500MB
- أسرع في التشغيل من AVD

### 2. **تثبيت BlueStacks:**
- شغل ملف التثبيت
- اتبع خطوات التثبيت
- سيبدأ تلقائياً بعد التثبيت

### 3. **إعداد BlueStacks:**
```
1. افتح BlueStacks
2. اذهب للإعدادات (Settings)
3. Performance:
   - CPU: 4 cores
   - RAM: 4 GB
   - Graphics Mode: DirectX
4. اضغط "Save" و "Restart"
```

## 📲 **تثبيت التطبيق في المحاكي:**

### **الطريقة 1 - السحب والإفلات:**
```
1. افتح المحاكي
2. اذهب لمجلد: app/build/outputs/apk/debug/
3. اسحب ملف app-debug.apk
4. أفلته في نافذة المحاكي
5. انتظر التثبيت
```

### **الطريقة 2 - ADB Command:**
```
1. افتح Command Prompt
2. اذهب لمجلد المشروع:
   cd "D:\kotln project"
3. شغل الأمر:
   adb install app/build/outputs/apk/debug/app-debug.apk
```

### **الطريقة 3 - من داخل المحاكي:**
```
1. افتح المحاكي
2. افتح متصفح Chrome
3. اذهب لرابط التنزيل (إذا كان متاح)
4. نزل APK وثبته
```

## 🔧 **حل مشاكل المحاكي الشائعة:**

### **المحاكي بطيء:**
```
1. تأكد من تفعيل Hardware Acceleration:
   - Intel: Intel HAXM
   - AMD: AMD Hypervisor
2. زيد RAM للمحاكي
3. استخدم Graphics: Hardware
4. أغلق البرامج الأخرى
```

### **المحاكي لا يبدأ:**
```
1. تأكد من تفعيل Virtualization في BIOS
2. أغلق Hyper-V في Windows:
   - Control Panel → Programs → Windows Features
   - ألغِ تحديد Hyper-V
3. أعد تشغيل الكمبيوتر
```

### **التطبيق لا يثبت:**
```
1. تأكد من تفعيل "Unknown Sources":
   - Settings → Security → Unknown Sources
2. جرب تثبيت APK مختلف
3. امسح cache المحاكي
```

## 🎯 **المحاكيات المُوصى بها:**

### **للتطوير:**
1. **Android Studio AVD** - الأفضل للتطوير
2. **Genymotion** - سريع ومتقدم
3. **Android-x86** - للأجهزة القديمة

### **للاختبار السريع:**
1. **BlueStacks** - الأسرع والأسهل
2. **NoxPlayer** - جيد للألعاب
3. **LDPlayer** - خفيف وسريع

## 📋 **خطوات الاختبار:**

### **بعد تثبيت التطبيق:**
```
1. افتح التطبيق
2. سجل دخول أو أنشئ حساب
3. جرب إنشاء مجموعة فيديو
4. اكتب كود الغرفة
5. افتح محاكي ثاني (إذا أمكن)
6. جرب الانضمام بالكود
7. راقب Logcat للـ logs
```

### **مراقبة Logs:**
```
1. افتح Android Studio
2. اذهب لـ View → Tool Windows → Logcat
3. اختر المحاكي من القائمة
4. فلتر بـ "HomeScreen" أو "toika"
5. راقب الرسائل أثناء الاختبار
```

## 🔥 **نصائح للسرعة:**

### **إعدادات Windows:**
```
1. أغلق برامج غير ضرورية
2. زيد Virtual Memory:
   - Control Panel → System → Advanced → Performance Settings
   - Virtual Memory → Change → Custom Size
3. استخدم SSD إذا متاح
```

### **إعدادات المحاكي:**
```
1. استخدم أقل دقة ممكنة (720p)
2. أغلق الرسوم المتحركة
3. استخدم Cold Boot بدلاً من Quick Boot
4. امسح cache بانتظام
```

## 📱 **APK الحالي:**
```
📍 المكان: app/build/outputs/apk/debug/app-debug.apk
📊 الحجم: ~16 MB
🔧 الميزات: مجموعة فيديو، بحث الغرف، تشخيص متقدم
```

## 🎯 **الهدف:**
- تشغيل التطبيق في محاكي سريع
- اختبار ميزة البحث عن الغرف
- مراقبة logs للتشخيص
- التأكد من عمل جميع الميزات

**🚀 ابدأ بـ BlueStacks للاختبار السريع أو Android Studio AVD للتطوير المتقدم!**
