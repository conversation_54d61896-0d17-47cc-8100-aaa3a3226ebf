package com.toika.netwok.auth

import android.content.Context
import android.content.Intent
import android.util.Log
import com.toika.netwok.R
import androidx.activity.result.ActivityResultLauncher
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.database.FirebaseDatabase
import com.toika.netwok.data.models.User
import kotlinx.coroutines.tasks.await

class AuthenticationManager(private val context: Context) {

    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val database = FirebaseDatabase.getInstance()
    private var googleSignInClient: GoogleSignInClient

    // متغير لحفظ اسم المستخدم مؤقتاً
    private var pendingUsername: String = ""
    
    companion object {
        private const val TAG = "AuthenticationManager"
        private const val RC_SIGN_IN = 9001
    }
    
    init {
        try {
            // الحصول على default_web_client_id من google-services.json
            val webClientId = context.getString(
                context.resources.getIdentifier(
                    "default_web_client_id",
                    "string",
                    context.packageName
                )
            )

            Log.d(TAG, "Web Client ID found: ${webClientId.take(20)}...")

            // إعداد Google Sign-In Options
            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestIdToken(webClientId)
                .requestEmail()
                .build()

            googleSignInClient = GoogleSignIn.getClient(context, gso)
            Log.d(TAG, "Google Sign-In Client initialized successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to initialize Google Sign-In: ${e.message}")

            // Fallback: إنشاء client بدون ID token (للاختبار فقط)
            val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                .requestEmail()
                .build()

            googleSignInClient = GoogleSignIn.getClient(context, gso)
            Log.w(TAG, "Google Sign-In initialized without ID token - limited functionality")
        }
    }
    
    // تسجيل دخول بالإيميل وكلمة المرور
    suspend fun signInWithEmailAndPassword(email: String, password: String): AuthResult {
        return try {
            val result = auth.signInWithEmailAndPassword(email, password).await()
            Log.d(TAG, "تم تسجيل الدخول بنجاح: ${result.user?.email}")

            // حفظ/تحديث معلومات المستخدم في قاعدة البيانات
            result.user?.let { firebaseUser ->
                saveUserToDatabase(firebaseUser)
            }

            AuthResult.Success(result.user)
        } catch (e: Exception) {
            Log.e(TAG, "فشل تسجيل الدخول: ${e.message}")
            AuthResult.Error(e.message ?: "حدث خطأ في تسجيل الدخول")
        }
    }
    
    // إنشاء حساب جديد بالإيميل وكلمة المرور مع التحقق من البريد
    suspend fun createUserWithEmailAndPassword(email: String, password: String, username: String = ""): AuthResult {
        return try {
            // حفظ اسم المستخدم مؤقتاً
            pendingUsername = username

            val result = auth.createUserWithEmailAndPassword(email, password).await()
            Log.d(TAG, "تم إنشاء الحساب بنجاح: ${result.user?.email}")

            // إرسال رابط التحقق من البريد الإلكتروني
            result.user?.let { firebaseUser ->
                sendEmailVerification(firebaseUser)
                // لا نحفظ في قاعدة البيانات حتى يتم التحقق من البريد
            }

            AuthResult.Success(result.user)
        } catch (e: Exception) {
            Log.e(TAG, "فشل إنشاء الحساب: ${e.message}")
            AuthResult.Error(e.message ?: "حدث خطأ في إنشاء الحساب")
        }
    }

    // إرسال رابط التحقق من البريد الإلكتروني
    private suspend fun sendEmailVerification(user: FirebaseUser): Boolean {
        return try {
            user.sendEmailVerification().await()
            Log.d(TAG, "تم إرسال رابط التحقق إلى: ${user.email}")
            true
        } catch (e: Exception) {
            Log.e(TAG, "فشل إرسال رابط التحقق: ${e.message}")
            false
        }
    }

    // التحقق من حالة التحقق من البريد الإلكتروني
    suspend fun checkEmailVerification(): Boolean {
        return try {
            val user = getCurrentUser()
            if (user != null) {
                user.reload().await()
                val isVerified = user.isEmailVerified
                Log.d(TAG, "حالة التحقق من البريد: $isVerified")

                // إذا تم التحقق، احفظ المستخدم في قاعدة البيانات
                if (isVerified) {
                    saveUserToDatabase(user)
                }

                isVerified
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "خطأ في التحقق من حالة البريد: ${e.message}")
            false
        }
    }

    // إعادة إرسال رابط التحقق
    suspend fun resendEmailVerification(): Boolean {
        return try {
            val user = getCurrentUser()
            if (user != null && !user.isEmailVerified) {
                sendEmailVerification(user)
            } else {
                false
            }
        } catch (e: Exception) {
            Log.e(TAG, "فشل إعادة إرسال رابط التحقق: ${e.message}")
            false
        }
    }
    
    // الحصول على Intent لتسجيل الدخول بـ Google
    fun getGoogleSignInIntent(): Intent {
        return googleSignInClient.signInIntent
    }
    
    // معالجة نتيجة تسجيل الدخول بـ Google
    suspend fun handleGoogleSignInResult(data: Intent?): AuthResult {
        return try {
            val task = GoogleSignIn.getSignedInAccountFromIntent(data)
            val account = task.getResult(ApiException::class.java)

            Log.d(TAG, "Google Sign-In successful for: ${account.email}")
            Log.d(TAG, "Display name: ${account.displayName}")
            Log.d(TAG, "ID Token available: ${account.idToken != null}")

            if (account.idToken != null) {
                // إذا كان ID Token متوفر، استخدم Firebase Auth
                firebaseAuthWithGoogle(account)
            } else {
                // إذا لم يكن ID Token متوفر، حاول إعادة تكوين Google Sign-In
                Log.w(TAG, "No ID Token available, trying to reconfigure Google Sign-In")
                try {
                    // إعادة تكوين Google Sign-In Client
                    val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
                        .requestIdToken(context.getString(R.string.default_web_client_id))
                        .requestEmail()
                        .build()

                    val newGoogleSignInClient = GoogleSignIn.getClient(context, gso)

                    // محاولة الحصول على ID Token مرة أخرى
                    val refreshedAccount = GoogleSignIn.getLastSignedInAccount(context)
                    if (refreshedAccount?.idToken != null) {
                        firebaseAuthWithGoogle(refreshedAccount)
                    } else {
                        // إذا فشل كل شيء، قم بتسجيل الدخول كمستخدم Google فقط
                        Log.i(TAG, "Using Google account without Firebase integration")
                        AuthResult.Success(null) // المستخدم مسجل دخول بـ Google فقط
                    }
                } catch (e: Exception) {
                    Log.e(TAG, "Failed to reconfigure Google Sign-In: ${e.message}")
                    AuthResult.Error("فشل في إعداد تسجيل الدخول بـ Google")
                }
            }
        } catch (e: ApiException) {
            Log.e(TAG, "Google Sign-In failed with code: ${e.statusCode}")
            Log.e(TAG, "Error message: ${e.message}")
            when (e.statusCode) {
                12501 -> AuthResult.Error("تم إلغاء تسجيل الدخول")
                12502 -> AuthResult.Error("خطأ في الشبكة")
                12500 -> AuthResult.Error("خطأ داخلي في Google Sign-In")
                else -> AuthResult.Error("فشل تسجيل الدخول بـ Google: ${e.message}")
            }
        }
    }
    
    // تسجيل الدخول إلى Firebase باستخدام Google
    private suspend fun firebaseAuthWithGoogle(account: GoogleSignInAccount): AuthResult {
        return try {
            val credential = GoogleAuthProvider.getCredential(account.idToken, null)
            val result = auth.signInWithCredential(credential).await()
            Log.d(TAG, "تم تسجيل الدخول بـ Google بنجاح: ${result.user?.email}")
            AuthResult.Success(result.user)
        } catch (e: Exception) {
            Log.e(TAG, "فشل المصادقة مع Firebase: ${e.message}")
            AuthResult.Error(e.message ?: "حدث خطأ في المصادقة")
        }
    }
    
    // تسجيل الخروج
    fun signOut() {
        auth.signOut()
        googleSignInClient.signOut()
        Log.d(TAG, "تم تسجيل الخروج")
    }
    
    // الحصول على المستخدم الحالي
    fun getCurrentUser(): FirebaseUser? = auth.currentUser
    
    // التحقق من حالة تسجيل الدخول
    fun isUserSignedIn(): Boolean {
        val firebaseUser = getCurrentUser()
        val googleUser = GoogleSignIn.getLastSignedInAccount(context)

        Log.d(TAG, "Checking sign-in status:")
        Log.d(TAG, "Firebase user: ${firebaseUser?.email}")
        Log.d(TAG, "Google user: ${googleUser?.email}")

        val isSignedIn = firebaseUser != null || googleUser != null
        Log.d(TAG, "User is signed in: $isSignedIn")

        return isSignedIn
    }

    // الحصول على معلومات المستخدم (Firebase أو Google)
    fun getUserInfo(): UserInfo? {
        val firebaseUser = getCurrentUser()
        if (firebaseUser != null) {
            return UserInfo(
                email = firebaseUser.email ?: "",
                displayName = firebaseUser.displayName ?: firebaseUser.email?.substringBefore("@") ?: "المستخدم",
                isEmailVerified = firebaseUser.isEmailVerified,
                source = "Firebase"
            )
        }

        val googleUser = GoogleSignIn.getLastSignedInAccount(context)
        if (googleUser != null) {
            return UserInfo(
                email = googleUser.email ?: "",
                displayName = googleUser.displayName ?: googleUser.email?.substringBefore("@") ?: "المستخدم",
                isEmailVerified = true, // Google accounts are always verified
                source = "Google"
            )
        }

        return null
    }

    // حفظ معلومات المستخدم في قاعدة البيانات
    private suspend fun saveUserToDatabase(firebaseUser: FirebaseUser) {
        try {
            // استخدام اسم المستخدم المحفوظ مؤقتاً أو الاسم من Firebase أو الإيميل
            val displayName = when {
                pendingUsername.isNotBlank() -> pendingUsername
                firebaseUser.displayName?.isNotBlank() == true -> firebaseUser.displayName!!
                else -> firebaseUser.email?.substringBefore("@") ?: "مستخدم"
            }

            val user = User(
                uid = firebaseUser.uid,
                displayName = displayName,
                email = firebaseUser.email ?: "",
                profileImageUrl = firebaseUser.photoUrl?.toString() ?: "",
                isOnline = true,
                lastSeen = System.currentTimeMillis()
            )

            database.getReference("users")
                .child(firebaseUser.uid)
                .setValue(user)
                .await()

            Log.d(TAG, "User saved to database: ${firebaseUser.uid} with name: $displayName")

            // تنظيف اسم المستخدم المؤقت بعد الحفظ
            pendingUsername = ""
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save user to database: ${e.message}")
        }
    }
}

// نتيجة المصادقة
sealed class AuthResult {
    data class Success(val user: FirebaseUser?) : AuthResult()
    data class Error(val message: String) : AuthResult()
}

// معلومات المستخدم
data class UserInfo(
    val email: String,
    val displayName: String,
    val isEmailVerified: Boolean,
    val source: String // "Firebase" أو "Google"
)
