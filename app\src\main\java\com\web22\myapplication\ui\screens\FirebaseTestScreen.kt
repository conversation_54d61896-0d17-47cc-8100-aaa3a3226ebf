package com.web22.myapplication.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.FirebaseDatabase
import com.web22.myapplication.data.FriendsManager
import com.web22.myapplication.data.models.User
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun FirebaseTestScreen(
    onBackClick: () -> Unit
) {
    val scope = rememberCoroutineScope()
    val friendsManager = remember { FriendsManager() }
    
    var testResults by remember { mutableStateOf<List<String>>(emptyList()) }
    var isRunningTests by remember { mutableStateOf(false) }
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(16.dp)
            .verticalScroll(rememberScrollState())
    ) {
        // شريط علوي
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "رجوع",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "اختبار Firebase",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // معلومات المشروع
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "معلومات المشروع",
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "Package: com.web22.myapplication",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                Text(
                    text = "SHA-1: 14:AF:50:8C:FA:06:20:81:20:7F:8E:3F:EF:99:67:71:F3:0E:59:BB",
                    fontSize = 12.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
                
                Text(
                    text = "المستخدم الحالي: ${FirebaseAuth.getInstance().currentUser?.email ?: "غير مسجل"}",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // زر تشغيل الاختبارات
        Button(
            onClick = {
                scope.launch {
                    isRunningTests = true
                    testResults = emptyList()
                    runFirebaseTests { result ->
                        testResults = testResults + result
                    }
                    isRunningTests = false
                }
            },
            modifier = Modifier.fillMaxWidth(),
            enabled = !isRunningTests
        ) {
            if (isRunningTests) {
                CircularProgressIndicator(
                    modifier = Modifier.size(20.dp),
                    color = MaterialTheme.colorScheme.onPrimary
                )
                Spacer(modifier = Modifier.width(8.dp))
            }
            Text("تشغيل اختبارات Firebase")
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // نتائج الاختبارات
        if (testResults.isNotEmpty()) {
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(
                    containerColor = MaterialTheme.colorScheme.surfaceVariant
                )
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "نتائج الاختبارات:",
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold,
                        color = MaterialTheme.colorScheme.onSurfaceVariant
                    )
                    
                    Spacer(modifier = Modifier.height(8.dp))
                    
                    testResults.forEach { result ->
                        Text(
                            text = result,
                            fontSize = 14.sp,
                            color = when {
                                result.contains("✅") -> MaterialTheme.colorScheme.primary
                                result.contains("❌") -> MaterialTheme.colorScheme.error
                                else -> MaterialTheme.colorScheme.onSurfaceVariant
                            },
                            modifier = Modifier.padding(vertical = 2.dp)
                        )
                    }
                }
            }
        }
        
        Spacer(modifier = Modifier.height(16.dp))
        
        // معلومات إضافية
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(16.dp)
            ) {
                Text(
                    text = "💡 كيفية استخدام هذا الاختبار:",
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Text(
                    text = "1. اضغط 'تشغيل اختبارات Firebase'\n" +
                            "2. انتظر النتائج\n" +
                            "3. إذا رأيت ✅ فكل شيء يعمل\n" +
                            "4. إذا رأيت ❌ فهناك مشكلة تحتاج حل\n" +
                            "5. اتبع الإرشادات لحل المشاكل",
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }
        }
    }
}

suspend fun runFirebaseTests(onResult: (String) -> Unit) {
    onResult("🔍 بدء اختبارات Firebase...")
    
    // اختبار 1: Firebase Auth
    try {
        val auth = FirebaseAuth.getInstance()
        val currentUser = auth.currentUser
        if (currentUser != null) {
            onResult("✅ Firebase Auth: مُتصل (${currentUser.email})")
        } else {
            onResult("❌ Firebase Auth: غير مُتصل")
        }
    } catch (e: Exception) {
        onResult("❌ Firebase Auth: خطأ - ${e.message}")
    }
    
    // اختبار 2: Firebase Database Connection
    try {
        val database = FirebaseDatabase.getInstance()
        onResult("✅ Firebase Database: متصل")
        
        // اختبار كتابة بسيط
        val testRef = database.getReference("test")
        testRef.setValue("test_value").await()
        onResult("✅ Firebase Database: الكتابة تعمل")
        
        // اختبار قراءة
        val snapshot = testRef.get().await()
        if (snapshot.exists()) {
            onResult("✅ Firebase Database: القراءة تعمل")
        } else {
            onResult("❌ Firebase Database: القراءة لا تعمل")
        }
        
        // حذف البيانات التجريبية
        testRef.removeValue().await()
        onResult("✅ Firebase Database: الحذف يعمل")
        
    } catch (e: Exception) {
        onResult("❌ Firebase Database: خطأ - ${e.message}")
    }
    
    // اختبار 3: Users Collection
    try {
        val database = FirebaseDatabase.getInstance()
        val usersRef = database.getReference("users")
        val snapshot = usersRef.limitToFirst(1).get().await()
        onResult("✅ Users Collection: يمكن الوصول إليها")
    } catch (e: Exception) {
        onResult("❌ Users Collection: خطأ - ${e.message}")
    }
    
    // اختبار 4: إنشاء مستخدم تجريبي
    try {
        val currentUser = FirebaseAuth.getInstance().currentUser
        if (currentUser != null) {
            val database = FirebaseDatabase.getInstance()
            val userRef = database.getReference("users").child(currentUser.uid)
            
            val testUser = User(
                uid = currentUser.uid,
                displayName = "مستخدم اختبار",
                email = currentUser.email ?: "",
                isOnline = true,
                lastSeen = System.currentTimeMillis()
            )
            
            userRef.setValue(testUser).await()
            onResult("✅ إنشاء مستخدم: نجح")
        } else {
            onResult("❌ إنشاء مستخدم: المستخدم غير مسجل")
        }
    } catch (e: Exception) {
        onResult("❌ إنشاء مستخدم: خطأ - ${e.message}")
    }
    
    onResult("🎯 انتهت الاختبارات!")
}
