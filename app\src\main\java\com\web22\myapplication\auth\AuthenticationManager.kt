package com.web22.myapplication.auth

import android.content.Context
import android.content.Intent
import android.util.Log
import androidx.activity.result.ActivityResultLauncher
import com.google.android.gms.auth.api.signin.GoogleSignIn
import com.google.android.gms.auth.api.signin.GoogleSignInAccount
import com.google.android.gms.auth.api.signin.GoogleSignInClient
import com.google.android.gms.auth.api.signin.GoogleSignInOptions
import com.google.android.gms.common.api.ApiException
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.FirebaseUser
import com.google.firebase.auth.GoogleAuthProvider
import kotlinx.coroutines.tasks.await

class AuthenticationManager(private val context: Context) {
    
    private val auth: FirebaseAuth = FirebaseAuth.getInstance()
    private val googleSignInClient: GoogleSignInClient
    
    companion object {
        private const val TAG = "AuthenticationManager"
        private const val RC_SIGN_IN = 9001
    }
    
    init {
        // إعداد Google Sign-In Options
        val gso = GoogleSignInOptions.Builder(GoogleSignInOptions.DEFAULT_SIGN_IN)
            .requestIdToken("************-webappclientid123456789.apps.googleusercontent.com")
            .requestEmail()
            .build()

        googleSignInClient = GoogleSignIn.getClient(context, gso)
        Log.d(TAG, "Google Sign-In Client initialized successfully")
    }
    
    // تسجيل دخول بالإيميل وكلمة المرور
    suspend fun signInWithEmailAndPassword(email: String, password: String): AuthResult {
        return try {
            val result = auth.signInWithEmailAndPassword(email, password).await()
            Log.d(TAG, "تم تسجيل الدخول بنجاح: ${result.user?.email}")
            AuthResult.Success(result.user)
        } catch (e: Exception) {
            Log.e(TAG, "فشل تسجيل الدخول: ${e.message}")
            AuthResult.Error(e.message ?: "حدث خطأ في تسجيل الدخول")
        }
    }
    
    // إنشاء حساب جديد بالإيميل وكلمة المرور
    suspend fun createUserWithEmailAndPassword(email: String, password: String): AuthResult {
        return try {
            val result = auth.createUserWithEmailAndPassword(email, password).await()
            Log.d(TAG, "تم إنشاء الحساب بنجاح: ${result.user?.email}")
            AuthResult.Success(result.user)
        } catch (e: Exception) {
            Log.e(TAG, "فشل إنشاء الحساب: ${e.message}")
            AuthResult.Error(e.message ?: "حدث خطأ في إنشاء الحساب")
        }
    }
    
    // الحصول على Intent لتسجيل الدخول بـ Google
    fun getGoogleSignInIntent(): Intent {
        return googleSignInClient.signInIntent
    }
    
    // معالجة نتيجة تسجيل الدخول بـ Google
    suspend fun handleGoogleSignInResult(data: Intent?): AuthResult {
        return try {
            val task = GoogleSignIn.getSignedInAccountFromIntent(data)
            val account = task.getResult(ApiException::class.java)
            firebaseAuthWithGoogle(account)
        } catch (e: ApiException) {
            Log.e(TAG, "فشل تسجيل الدخول بـ Google: ${e.message}")
            AuthResult.Error("فشل تسجيل الدخول بـ Google")
        }
    }
    
    // تسجيل الدخول إلى Firebase باستخدام Google
    private suspend fun firebaseAuthWithGoogle(account: GoogleSignInAccount): AuthResult {
        return try {
            val credential = GoogleAuthProvider.getCredential(account.idToken, null)
            val result = auth.signInWithCredential(credential).await()
            Log.d(TAG, "تم تسجيل الدخول بـ Google بنجاح: ${result.user?.email}")
            AuthResult.Success(result.user)
        } catch (e: Exception) {
            Log.e(TAG, "فشل المصادقة مع Firebase: ${e.message}")
            AuthResult.Error(e.message ?: "حدث خطأ في المصادقة")
        }
    }
    
    // تسجيل الخروج
    fun signOut() {
        auth.signOut()
        googleSignInClient.signOut()
        Log.d(TAG, "تم تسجيل الخروج")
    }
    
    // الحصول على المستخدم الحالي
    fun getCurrentUser(): FirebaseUser? = auth.currentUser
    
    // التحقق من حالة تسجيل الدخول
    fun isUserSignedIn(): Boolean = getCurrentUser() != null
}

// نتيجة المصادقة
sealed class AuthResult {
    data class Success(val user: FirebaseUser?) : AuthResult()
    data class Error(val message: String) : AuthResult()
}
