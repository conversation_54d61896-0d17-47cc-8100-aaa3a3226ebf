{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-72:/values-nb/values-nb.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7661eb1439e484e98fea3778baf664e3\\transformed\\play-services-base-18.5.0\\res\\values-nb\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,450,572,677,829,955,1071,1170,1320,1423,1580,1704,1842,2014,2077,2135", "endColumns": "101,154,121,104,151,125,115,98,149,102,156,123,137,171,62,57,73", "endOffsets": "294,449,571,676,828,954,1070,1169,1319,1422,1579,1703,1841,2013,2076,2134,2208"}, "to": {"startLines": "40,41,42,43,44,45,46,47,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3869,3975,4134,4260,4369,4525,4655,4775,5008,5162,5269,5430,5558,5700,5876,5943,6005", "endColumns": "105,158,125,108,155,129,119,102,153,106,160,127,141,175,66,61,77", "endOffsets": "3970,4129,4255,4364,4520,4650,4770,4873,5157,5264,5425,5553,5695,5871,5938,6000,6078"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f43be022de2035b39234c04c60821b4e\\transformed\\ui-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,279,376,476,564,640,728,817,899,979,1061,1131,1205,1276,1346,1423,1490", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "193,274,371,471,559,635,723,812,894,974,1056,1126,1200,1271,1341,1418,1485,1605"}, "to": {"startLines": "38,39,59,60,61,65,66,124,125,126,127,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3695,3788,6193,6290,6390,6788,6864,13092,13181,13263,13343,13504,13574,13648,13719,13890,13967,14034", "endColumns": "92,80,96,99,87,75,87,88,81,79,81,69,73,70,69,76,66,119", "endOffsets": "3783,3864,6285,6385,6473,6859,6947,13176,13258,13338,13420,13569,13643,13714,13784,13962,14029,14149"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e099880564deb176147b2205fdc84816\\transformed\\foundation-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,87", "endOffsets": "140,228"}, "to": {"startLines": "137,138", "startColumns": "4,4", "startOffsets": "14154,14244", "endColumns": "89,87", "endOffsets": "14239,14327"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\549dfb7dd035cf2830ebdc27ba100054\\transformed\\appcompat-1.1.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,869,960,1052,1146,1240,1341,1434,1529,1627,1718,1809,1886,1989,2087,2183,2287,2386,2487,2640,2737", "endColumns": "102,94,113,85,99,112,76,75,90,91,93,93,100,92,94,97,90,90,76,102,97,95,103,98,100,152,96,78", "endOffsets": "203,298,412,498,598,711,788,864,955,1047,1141,1235,1336,1429,1524,1622,1713,1804,1881,1984,2082,2178,2282,2381,2482,2635,2732,2811"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,208,303,417,503,603,716,793,869,960,1052,1146,1240,1341,1434,1529,1627,1718,1809,1886,1989,2087,2183,2287,2386,2487,2640,13425", "endColumns": "102,94,113,85,99,112,76,75,90,91,93,93,100,92,94,97,90,90,76,102,97,95,103,98,100,152,96,78", "endOffsets": "203,298,412,498,598,711,788,864,955,1047,1141,1235,1336,1429,1524,1622,1713,1804,1881,1984,2082,2178,2282,2381,2482,2635,2732,13499"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e71863d474cbeda869e988a1e7d78c0\\transformed\\credentials-1.2.0-rc01\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,166", "endColumns": "110,120", "endOffsets": "161,282"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2737,2848", "endColumns": "110,120", "endOffsets": "2843,2964"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,149,251,348,447,555,661,781", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "144,246,343,442,550,656,776,877"}, "to": {"startLines": "31,32,33,34,35,36,37,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2969,3063,3165,3262,3361,3469,3575,13789", "endColumns": "93,101,96,98,107,105,119,100", "endOffsets": "3058,3160,3257,3356,3464,3570,3690,13885"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ae8da29e7eccb1a8725377c96f852ddc\\transformed\\material3-release\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,172,287,394,507,606,700,811,955,1077,1227,1311,1411,1500,1594,1701,1819,1924,2051,2173,2306,2473,2600,2716,2837,2958,3048,3146,3265,3396,3497,3607,3710,3844,3985,4090,4188,4268,4362,4453,4562,4646,4730,4841,4921,5005,5106,5205,5296,5396,5484,5589,5691,5796,5913,5993,6096", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,108,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "167,282,389,502,601,695,806,950,1072,1222,1306,1406,1495,1589,1696,1814,1919,2046,2168,2301,2468,2595,2711,2832,2953,3043,3141,3260,3391,3492,3602,3705,3839,3980,4085,4183,4263,4357,4448,4557,4641,4725,4836,4916,5000,5101,5200,5291,5391,5479,5584,5686,5791,5908,5988,6091,6190"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6952,7069,7184,7291,7404,7503,7597,7708,7852,7974,8124,8208,8308,8397,8491,8598,8716,8821,8948,9070,9203,9370,9497,9613,9734,9855,9945,10043,10162,10293,10394,10504,10607,10741,10882,10987,11085,11165,11259,11350,11459,11543,11627,11738,11818,11902,12003,12102,12193,12293,12381,12486,12588,12693,12810,12890,12993", "endColumns": "116,114,106,112,98,93,110,143,121,149,83,99,88,93,106,117,104,126,121,132,166,126,115,120,120,89,97,118,130,100,109,102,133,140,104,97,79,93,90,108,83,83,110,79,83,100,98,90,99,87,104,101,104,116,79,102,98", "endOffsets": "7064,7179,7286,7399,7498,7592,7703,7847,7969,8119,8203,8303,8392,8486,8593,8711,8816,8943,9065,9198,9365,9492,9608,9729,9850,9940,10038,10157,10288,10389,10499,10602,10736,10877,10982,11080,11160,11254,11345,11454,11538,11622,11733,11813,11897,11998,12097,12188,12288,12376,12481,12583,12688,12805,12885,12988,13087"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a7d2fcfebb99ca0b7d2351010b4b169\\transformed\\play-services-basement-18.4.0\\res\\values-nb\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "125", "endOffsets": "320"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "4878", "endColumns": "129", "endOffsets": "5003"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09ab614e376350ca0889dc2dbf11206f\\transformed\\browser-1.4.0\\res\\values-nb\\values-nb.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,165,266,378", "endColumns": "109,100,111,96", "endOffsets": "160,261,373,470"}, "to": {"startLines": "58,62,63,64", "startColumns": "4,4,4,4", "startOffsets": "6083,6478,6579,6691", "endColumns": "109,100,111,96", "endOffsets": "6188,6574,6686,6783"}}]}]}