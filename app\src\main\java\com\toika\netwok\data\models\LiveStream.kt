package com.toika.netwok.data.models

data class LiveStream(
    val streamId: String = "",
    val hostId: String = "",
    val hostName: String = "",
    val hostProfileImage: String = "",
    val title: String = "",
    val description: String = "",
    val thumbnailUrl: String = "",
    val isActive: Boolean = false,
    val startTime: Long = 0,
    val endTime: Long = 0,
    val viewerCount: Int = 0,
    val maxViewers: Int = 0,
    val tags: List<String> = emptyList(),
    val isPrivate: Boolean = false,
    val allowedViewers: List<String> = emptyList(), // للبث الخاص
    val streamUrl: String = "",
    val chatEnabled: Boolean = true,
    val recordingEnabled: Boolean = false,
    val createdAt: Long = System.currentTimeMillis(),
    val updatedAt: Long = System.currentTimeMillis()
)

data class StreamViewer(
    val viewerId: String = "",
    val viewerName: String = "",
    val viewerProfileImage: String = "",
    val joinedAt: Long = 0,
    val isActive: Boolean = true
)

data class StreamMessage(
    val messageId: String = "",
    val streamId: String = "",
    val senderId: String = "",
    val senderName: String = "",
    val senderProfileImage: String = "",
    val message: String = "",
    val timestamp: Long = 0,
    val messageType: MessageType = MessageType.TEXT
)

enum class MessageType {
    TEXT,
    EMOJI,
    SYSTEM,
    JOIN,
    LEAVE
}

data class StreamStats(
    val streamId: String = "",
    val currentViewers: Int = 0,
    val totalViews: Int = 0,
    val peakViewers: Int = 0,
    val duration: Long = 0,
    val messagesCount: Int = 0,
    val likesCount: Int = 0,
    val sharesCount: Int = 0
)
