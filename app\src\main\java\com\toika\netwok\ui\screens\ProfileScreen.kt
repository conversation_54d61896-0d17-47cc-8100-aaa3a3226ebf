package com.toika.netwok.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import kotlinx.coroutines.launch
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.firebase.auth.FirebaseAuth
import com.toika.netwok.auth.AuthenticationManager

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ProfileScreen(
    onBackClick: () -> Unit,
    onSignOut: () -> Unit
) {
    val context = LocalContext.current
    val authManager = remember { AuthenticationManager(context) }
    val scope = rememberCoroutineScope()

    var showSignOutDialog by remember { mutableStateOf(false) }
    var showEditDialog by remember { mutableStateOf(false) }
    var showSecurityDialog by remember { mutableStateOf(false) }
    var showNotificationDialog by remember { mutableStateOf(false) }
    var showLanguageDialog by remember { mutableStateOf(false) }
    var showThemeDialog by remember { mutableStateOf(false) }
    var userFromDatabase by remember { mutableStateOf<com.toika.netwok.data.models.User?>(null) }
    var newDisplayName by remember { mutableStateOf("") }
    var notificationsEnabled by remember { mutableStateOf(true) }
    var selectedLanguage by remember { mutableStateOf("العربية") }
    var isDarkTheme by remember { mutableStateOf(false) }

    // الحصول على معلومات المستخدم من قاعدة البيانات
    LaunchedEffect(Unit) {
        scope.launch {
            userFromDatabase = authManager.getUserFromDatabase()
        }
    }

    // الحصول على معلومات المستخدم
    val displayName = userFromDatabase?.displayName ?: "المستخدم"
    val userEmail = userFromDatabase?.email ?: "غير متوفر"
    val userInfo = authManager.getUserInfo()
    val isEmailVerified = userInfo?.isEmailVerified ?: false
    val userSource = userInfo?.source ?: "غير معروف"
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // شريط علوي
        Row(
            modifier = Modifier.fillMaxWidth(),
            verticalAlignment = Alignment.CenterVertically
        ) {
            IconButton(onClick = onBackClick) {
                Icon(
                    Icons.Default.ArrowBack,
                    contentDescription = "رجوع",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.width(8.dp))
            
            Text(
                text = "الملف الشخصي",
                fontSize = 24.sp,
                fontWeight = FontWeight.Bold,
                color = MaterialTheme.colorScheme.primary
            )
            
            Spacer(modifier = Modifier.weight(1f))
            
            IconButton(onClick = { showEditDialog = true }) {
                Icon(
                    Icons.Default.Edit,
                    contentDescription = "تعديل",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // معلومات المستخدم الأساسية
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                // صورة الملف الشخصي
                Card(
                    modifier = Modifier
                        .size(80.dp)
                        .clip(CircleShape),
                    colors = CardDefaults.cardColors(
                        containerColor = MaterialTheme.colorScheme.surface
                    )
                ) {
                    Box(
                        modifier = Modifier.fillMaxSize(),
                        contentAlignment = Alignment.Center
                    ) {
                        Icon(
                            Icons.Default.Person,
                            contentDescription = "الملف الشخصي",
                            modifier = Modifier.size(40.dp),
                            tint = MaterialTheme.colorScheme.primary
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                Text(
                    text = displayName,
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onPrimaryContainer
                )

                Text(
                    text = userEmail,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onPrimaryContainer.copy(alpha = 0.8f)
                )
            }
        }
        
        Spacer(modifier = Modifier.height(24.dp))
        
        // قائمة الإعدادات
        Text(
            text = "الإعدادات",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(horizontal = 8.dp)
        )
        
        Spacer(modifier = Modifier.height(12.dp))
        
        // عناصر الإعدادات
        SettingItem(
            iconText = "👤",
            title = "تعديل الملف الشخصي",
            subtitle = "تغيير الاسم والصورة",
            onClick = { showEditDialog = true }
        )
        
        SettingItem(
            iconText = "🔒",
            title = "الأمان والخصوصية",
            subtitle = "كلمة المرور والأمان",
            onClick = { /* فتح إعدادات الأمان */ }
        )

        SettingItem(
            iconText = "🔔",
            title = "الإشعارات",
            subtitle = "إدارة الإشعارات",
            onClick = { /* فتح إعدادات الإشعارات */ }
        )

        SettingItem(
            iconText = "🌐",
            title = "اللغة",
            subtitle = "العربية",
            onClick = { /* فتح إعدادات اللغة */ }
        )

        SettingItem(
            iconText = "🌙",
            title = "المظهر",
            subtitle = "فاتح/داكن",
            onClick = { /* فتح إعدادات المظهر */ }
        )

        SettingItem(
            iconText = "❓",
            title = "المساعدة والدعم",
            subtitle = "الأسئلة الشائعة",
            onClick = { /* فتح المساعدة */ }
        )
        
        SettingItem(
            iconText = "ℹ️",
            title = "حول التطبيق",
            subtitle = "الإصدار 1.0",
            onClick = { /* فتح معلومات التطبيق */ }
        )

        Spacer(modifier = Modifier.height(16.dp))
        
        // زر تسجيل الخروج
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.errorContainer
            )
        ) {
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.ExitToApp,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.onErrorContainer
                )
                
                Spacer(modifier = Modifier.width(16.dp))
                
                Column(modifier = Modifier.weight(1f)) {
                    Text(
                        text = "تسجيل الخروج",
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium,
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                    
                    Text(
                        text = "الخروج من الحساب الحالي",
                        fontSize = 14.sp,
                        color = MaterialTheme.colorScheme.onErrorContainer.copy(alpha = 0.8f)
                    )
                }
                
                TextButton(
                    onClick = { showSignOutDialog = true }
                ) {
                    Text(
                        "خروج",
                        color = MaterialTheme.colorScheme.onErrorContainer
                    )
                }
            }
        }
    }
    
    // حوار تأكيد تسجيل الخروج
    if (showSignOutDialog) {
        AlertDialog(
            onDismissRequest = { showSignOutDialog = false },
            title = { Text("تسجيل الخروج") },
            text = { Text("هل أنت متأكد من أنك تريد تسجيل الخروج؟") },
            confirmButton = {
                TextButton(
                    onClick = {
                        authManager.signOut()
                        showSignOutDialog = false
                        onSignOut()
                    }
                ) {
                    Text("نعم")
                }
            },
            dismissButton = {
                TextButton(onClick = { showSignOutDialog = false }) {
                    Text("إلغاء")
                }
            }
        )
    }
    
    // حوار تعديل الملف الشخصي
    if (showEditDialog) {
        AlertDialog(
            onDismissRequest = { showEditDialog = false },
            title = { Text("تعديل الملف الشخصي") },
            text = { Text("هذه الميزة ستكون متاحة قريباً!") },
            confirmButton = {
                TextButton(onClick = { showEditDialog = false }) {
                    Text("حسناً")
                }
            }
        )
    }
}

@Composable
fun SettingItem(
    iconText: String? = null,
    icon: androidx.compose.ui.graphics.vector.ImageVector? = null,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(vertical = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // أيقونة أو نص
            if (iconText != null) {
                Text(
                    text = iconText,
                    fontSize = 24.sp
                )
            } else if (icon != null) {
                Icon(
                    icon,
                    contentDescription = null,
                    tint = MaterialTheme.colorScheme.primary
                )
            }
            
            Spacer(modifier = Modifier.width(16.dp))
            
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    color = MaterialTheme.colorScheme.onSurface
                )
                
                Text(
                    text = subtitle,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            Text(
                text = "◀",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
