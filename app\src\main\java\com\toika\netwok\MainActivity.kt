package com.toika.netwok

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.enableEdgeToEdge
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.ui.Modifier
import com.toika.netwok.ui.theme.AnimeTheme
import com.toika.netwok.navigation.AppNavigation
import com.google.firebase.analytics.FirebaseAnalytics

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // Test Firebase connection
        FirebaseHelper.testFirebaseConnection()

        // Log app start event
        val analytics = FirebaseAnalytics.getInstance(this)
        FirebaseHelper.logEvent(analytics, "app_start", mapOf("screen" to "main"))

        enableEdgeToEdge()
        setContent {
            AnimeTheme {
                Surface(
                    modifier = Modifier.fillMaxSize(),
                    color = MaterialTheme.colorScheme.background
                ) {
                    AppNavigation()
                }
            }
        }
    }
}
