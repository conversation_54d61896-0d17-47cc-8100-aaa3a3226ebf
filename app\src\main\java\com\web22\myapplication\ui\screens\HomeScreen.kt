package com.web22.myapplication.ui.screens

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ExitToApp
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.firebase.auth.FirebaseAuth
import com.web22.myapplication.auth.AuthenticationManager

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun HomeScreen(
    onSignOut: () -> Unit,
    onNavigateToFriends: () -> Unit,
    onNavigateToGroups: () -> Unit,
    onNavigateToProfile: () -> Unit
) {
    val context = LocalContext.current
    val authManager = remember { AuthenticationManager(context) }
    val currentUser = FirebaseAuth.getInstance().currentUser
    
    var showSignOutDialog by remember { mutableStateOf(false) }
    
    // الحصول على اسم المستخدم أو الإيميل
    val displayName = currentUser?.displayName?.takeIf { it.isNotBlank() } 
        ?: currentUser?.email?.substringBefore("@") 
        ?: "المستخدم"
    
    Column(
        modifier = Modifier
            .fillMaxSize()
            .padding(24.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        // شريط علوي مع زر تسجيل الخروج
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.End
        ) {
            IconButton(
                onClick = { showSignOutDialog = true }
            ) {
                Icon(
                    Icons.Default.ExitToApp,
                    contentDescription = "تسجيل الخروج",
                    tint = MaterialTheme.colorScheme.primary
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // أيقونة المستخدم
        Card(
            modifier = Modifier
                .size(120.dp)
                .clip(CircleShape),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.primaryContainer
            )
        ) {
            Box(
                modifier = Modifier.fillMaxSize(),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    Icons.Default.Person,
                    contentDescription = "المستخدم",
                    modifier = Modifier.size(60.dp),
                    tint = MaterialTheme.colorScheme.onPrimaryContainer
                )
            }
        }
        
        Spacer(modifier = Modifier.height(32.dp))
        
        // رسالة الترحيب
        Text(
            text = "مرحباً",
            fontSize = 28.sp,
            fontWeight = FontWeight.Light,
            color = MaterialTheme.colorScheme.onSurfaceVariant
        )
        
        Text(
            text = displayName,
            fontSize = 36.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.primary,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(16.dp))
        
        Text(
            text = "نتمنى لك يوماً سعيداً! 🌟",
            fontSize = 18.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center
        )
        
        Spacer(modifier = Modifier.height(48.dp))

        // القائمة الرئيسية - الأزرار الثلاثة
        Text(
            text = "القائمة الرئيسية",
            fontSize = 20.sp,
            fontWeight = FontWeight.Bold,
            color = MaterialTheme.colorScheme.onSurface,
            modifier = Modifier.padding(horizontal = 8.dp)
        )

        Spacer(modifier = Modifier.height(16.dp))

        // زر قائمة الأصدقاء
        MenuButton(
            icon = "👥",
            title = "قائمة الأصدقاء",
            subtitle = "إدارة الأصدقاء والمتابعين",
            onClick = onNavigateToFriends
        )

        Spacer(modifier = Modifier.height(12.dp))

        // زر قائمة المجموعات
        MenuButton(
            icon = "👨‍👩‍👧‍👦",
            title = "قائمة المجموعات",
            subtitle = "المجموعات والدردشات الجماعية",
            onClick = onNavigateToGroups
        )

        Spacer(modifier = Modifier.height(12.dp))

        // زر الإعدادات (البروفايل)
        MenuButton(
            icon = "⚙️",
            title = "الإعدادات",
            subtitle = "الملف الشخصي والإعدادات",
            onClick = onNavigateToProfile
        )

        Spacer(modifier = Modifier.height(32.dp))

        // معلومات الحساب
        Card(
            modifier = Modifier.fillMaxWidth(),
            colors = CardDefaults.cardColors(
                containerColor = MaterialTheme.colorScheme.surfaceVariant
            )
        ) {
            Column(
                modifier = Modifier.padding(20.dp)
            ) {
                Text(
                    text = "معلومات الحساب",
                    fontSize = 20.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
                
                Spacer(modifier = Modifier.height(16.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "البريد الإلكتروني:",
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = currentUser?.email ?: "غير متوفر",
                        color = MaterialTheme.colorScheme.primary
                    )
                }
                
                Spacer(modifier = Modifier.height(8.dp))
                
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween
                ) {
                    Text(
                        text = "حالة التحقق:",
                        fontWeight = FontWeight.Medium
                    )
                    Text(
                        text = if (currentUser?.isEmailVerified == true) "مُتحقق ✓" else "غير مُتحقق",
                        color = if (currentUser?.isEmailVerified == true) 
                            MaterialTheme.colorScheme.primary 
                        else 
                            MaterialTheme.colorScheme.error
                    )
                }
            }
        }
        
        Spacer(modifier = Modifier.weight(1f))
        
        // رسالة في الأسفل
        Text(
            text = "تم تسجيل الدخول بنجاح! 🎉",
            fontSize = 16.sp,
            color = MaterialTheme.colorScheme.onSurfaceVariant,
            textAlign = TextAlign.Center,
            modifier = Modifier.padding(bottom = 16.dp)
        )
    }
    
    // حوار تأكيد تسجيل الخروج
    if (showSignOutDialog) {
        AlertDialog(
            onDismissRequest = { showSignOutDialog = false },
            title = {
                Text("تسجيل الخروج")
            },
            text = {
                Text("هل أنت متأكد من أنك تريد تسجيل الخروج؟")
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        authManager.signOut()
                        showSignOutDialog = false
                        onSignOut()
                    }
                ) {
                    Text("نعم")
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showSignOutDialog = false }
                ) {
                    Text("إلغاء")
                }
            }
        )
    }
}

@Composable
fun MenuButton(
    icon: String,
    title: String,
    subtitle: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(
            containerColor = MaterialTheme.colorScheme.surface
        ),
        onClick = onClick
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(20.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // أيقونة
            Text(
                text = icon,
                fontSize = 32.sp,
                modifier = Modifier.size(48.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            // النص
            Column(modifier = Modifier.weight(1f)) {
                Text(
                    text = title,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold,
                    color = MaterialTheme.colorScheme.onSurface
                )

                Text(
                    text = subtitle,
                    fontSize = 14.sp,
                    color = MaterialTheme.colorScheme.onSurfaceVariant
                )
            }

            // سهم
            Text(
                text = "◀",
                fontSize = 16.sp,
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
