  Activity android.app  Application android.app  
AnimeTheme android.app.Activity  
AppNavigation android.app.Activity  FirebaseAnalytics android.app.Activity  FirebaseHelper android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  	RESULT_OK android.app.Activity  Surface android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  logEvent android.app.Activity  mapOf android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  testFirebaseConnection android.app.Activity  to android.app.Activity  FirebaseAnalytics android.app.Application  FirebaseApp android.app.Application  FirebaseAuth android.app.Application  FirebaseDatabase android.app.Application  FirebaseStorage android.app.Application  onCreate android.app.Application  ClipData android.content  ClipboardManager android.content  Context android.content  Intent android.content  newPlainText android.content.ClipData  setPrimaryClip  android.content.ClipboardManager  
AnimeTheme android.content.Context  
AppNavigation android.content.Context  CLIPBOARD_SERVICE android.content.Context  FirebaseAnalytics android.content.Context  FirebaseApp android.content.Context  FirebaseAuth android.content.Context  FirebaseDatabase android.content.Context  FirebaseHelper android.content.Context  FirebaseStorage android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  	getString android.content.Context  getSystemService android.content.Context  logEvent android.content.Context  mapOf android.content.Context  packageName android.content.Context  	resources android.content.Context  
setContent android.content.Context  testFirebaseConnection android.content.Context  to android.content.Context  
AnimeTheme android.content.ContextWrapper  
AppNavigation android.content.ContextWrapper  FirebaseAnalytics android.content.ContextWrapper  FirebaseApp android.content.ContextWrapper  FirebaseAuth android.content.ContextWrapper  FirebaseDatabase android.content.ContextWrapper  FirebaseHelper android.content.ContextWrapper  FirebaseStorage android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  logEvent android.content.ContextWrapper  mapOf android.content.ContextWrapper  
setContent android.content.ContextWrapper  testFirebaseConnection android.content.ContextWrapper  to android.content.ContextWrapper  
getIdentifier android.content.res.Resources  Uri android.net  toString android.net.Uri  Build 
android.os  Bundle 
android.os  	getString android.os.BaseBundle  
putBoolean android.os.BaseBundle  	putDouble android.os.BaseBundle  putInt android.os.BaseBundle  putLong android.os.BaseBundle  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  	getString android.os.Bundle  
putBoolean android.os.Bundle  	putDouble android.os.Bundle  putInt android.os.Bundle  putLong android.os.Bundle  	putString android.os.Bundle  Log android.util  d android.util.Log  e android.util.Log  i android.util.Log  w android.util.Log  
EMAIL_ADDRESS android.util.Patterns  
AnimeTheme  android.view.ContextThemeWrapper  
AppNavigation  android.view.ContextThemeWrapper  FirebaseAnalytics  android.view.ContextThemeWrapper  FirebaseHelper  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  logEvent  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  testFirebaseConnection  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
AnimeTheme #androidx.activity.ComponentActivity  
AppNavigation #androidx.activity.ComponentActivity  FirebaseAnalytics #androidx.activity.ComponentActivity  FirebaseHelper #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  logEvent #androidx.activity.ComponentActivity  mapOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  testFirebaseConnection #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResult androidx.activity.result  ActivityResultLauncher androidx.activity.result  data 'androidx.activity.result.ActivityResult  
resultCode 'androidx.activity.result.ActivityResult  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  AnimatedContentScope androidx.compose.animation  AddFriendScreen /androidx.compose.animation.AnimatedContentScope  EmailVerificationScreen /androidx.compose.animation.AnimatedContentScope  FirebaseTestScreen /androidx.compose.animation.AnimatedContentScope  
FriendsScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  LiveStreamScreen /androidx.compose.animation.AnimatedContentScope  LoginScreen /androidx.compose.animation.AnimatedContentScope  
ProfileScreen /androidx.compose.animation.AnimatedContentScope  RegisterScreen /androidx.compose.animation.AnimatedContentScope  Routes /androidx.compose.animation.AnimatedContentScope  ScrollState androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Activity "androidx.compose.foundation.layout  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  	ArrowBack "androidx.compose.foundation.layout  
AuthResult "androidx.compose.foundation.layout  BottomNavButton "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  ClipData "androidx.compose.foundation.layout  ClipboardManager "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  	ExitToApp "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FirebaseAuth "androidx.compose.foundation.layout  FirebaseDatabase "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
FriendItem "androidx.compose.foundation.layout  
FriendRequest "androidx.compose.foundation.layout  FriendRequestItem "androidx.compose.foundation.layout  Group "androidx.compose.foundation.layout  	GroupItem "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  LaunchedEffect "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  
PaddingValues "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  RadioButton "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  Scaffold "androidx.compose.foundation.layout  SettingItem "androidx.compose.foundation.layout  SnackbarDuration "androidx.compose.foundation.layout  SnackbarHost "androidx.compose.foundation.layout  SnackbarHostState "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Switch "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  User "androidx.compose.foundation.layout  VisualTransformation "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  buttonColors "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  delay "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  ifEmpty "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  	onFailure "androidx.compose.foundation.layout  	onSuccess "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  runFirebaseTests "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  sumOf "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceBetween .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Error -androidx.compose.foundation.layout.AuthResult  Success -androidx.compose.foundation.layout.AuthResult  	Alignment +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  Email +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Person +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  BottomNavButton .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  ClipData .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Context .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  Email .androidx.compose.foundation.layout.ColumnScope  	ExitToApp .androidx.compose.foundation.layout.ColumnScope  FirebaseAuth .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
FriendItem .androidx.compose.foundation.layout.ColumnScope  FriendRequestItem .androidx.compose.foundation.layout.ColumnScope  	GroupItem .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Lock .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  	PlayArrow .androidx.compose.foundation.layout.ColumnScope  RadioButton .androidx.compose.foundation.layout.ColumnScope  Refresh .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Search .androidx.compose.foundation.layout.ColumnScope  SettingItem .androidx.compose.foundation.layout.ColumnScope  Share .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Switch .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  VisualTransformation .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  android .androidx.compose.foundation.layout.ColumnScope  buttonColors .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  	clickable .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  contains .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  ifEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  	onFailure .androidx.compose.foundation.layout.ColumnScope  	onSuccess .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  plus .androidx.compose.foundation.layout.ColumnScope  runFirebaseTests .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  sumOf .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  BottomNavButton +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  ButtonDefaults +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  ClipData +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Context +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  	ExitToApp +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Person +androidx.compose.foundation.layout.RowScope  	PlayArrow +androidx.compose.foundation.layout.RowScope  RadioButton +androidx.compose.foundation.layout.RowScope  Refresh +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Share +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Switch +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  align +androidx.compose.foundation.layout.RowScope  buttonColors +androidx.compose.foundation.layout.RowScope  
cardColors +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  ifEmpty +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  sumOf +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  vector ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  ImageVector Fandroidx.compose.foundation.layout.androidx.compose.ui.graphics.vector  toika &androidx.compose.foundation.layout.com  netwok ,androidx.compose.foundation.layout.com.toika  data 3androidx.compose.foundation.layout.com.toika.netwok  models 8androidx.compose.foundation.layout.com.toika.netwok.data  User ?androidx.compose.foundation.layout.com.toika.netwok.data.models  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  
FriendItem .androidx.compose.foundation.lazy.LazyItemScope  FriendRequestItem .androidx.compose.foundation.lazy.LazyItemScope  	GroupItem .androidx.compose.foundation.lazy.LazyItemScope  launch .androidx.compose.foundation.lazy.LazyItemScope  
FriendItem .androidx.compose.foundation.lazy.LazyListScope  FriendRequestItem .androidx.compose.foundation.lazy.LazyListScope  	GroupItem .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  launch .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Email ,androidx.compose.material.icons.Icons.Filled  	ExitToApp ,androidx.compose.material.icons.Icons.Filled  Lock ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  	PlayArrow ,androidx.compose.material.icons.Icons.Filled  Refresh ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Share ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  Arrangement &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  	Exception &androidx.compose.material.icons.filled  	ExitToApp &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  LaunchedEffect &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  OutlinedTextField &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  	PlayArrow &androidx.compose.material.icons.filled  RadioButton &androidx.compose.material.icons.filled  Refresh &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  SettingItem &androidx.compose.material.icons.filled  Share &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Switch &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  	clickable &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  com &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  
isNotBlank &androidx.compose.material.icons.filled  launch &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  rememberCoroutineScope &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  compose /androidx.compose.material.icons.filled.androidx  ui 7androidx.compose.material.icons.filled.androidx.compose  graphics :androidx.compose.material.icons.filled.androidx.compose.ui  vector Candroidx.compose.material.icons.filled.androidx.compose.ui.graphics  ImageVector Jandroidx.compose.material.icons.filled.androidx.compose.ui.graphics.vector  toika *androidx.compose.material.icons.filled.com  netwok 0androidx.compose.material.icons.filled.com.toika  data 7androidx.compose.material.icons.filled.com.toika.netwok  models <androidx.compose.material.icons.filled.com.toika.netwok.data  User Candroidx.compose.material.icons.filled.com.toika.netwok.data.models  Activity androidx.compose.material3  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  	ArrowBack androidx.compose.material3  
AuthResult androidx.compose.material3  BottomNavButton androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ClipData androidx.compose.material3  ClipboardManager androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Context androidx.compose.material3  Edit androidx.compose.material3  	Exception androidx.compose.material3  	ExitToApp androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FirebaseAuth androidx.compose.material3  FirebaseDatabase androidx.compose.material3  
FontWeight androidx.compose.material3  
FriendItem androidx.compose.material3  
FriendRequest androidx.compose.material3  FriendRequestItem androidx.compose.material3  Group androidx.compose.material3  	GroupItem androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Int androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  LaunchedEffect androidx.compose.material3  
LazyColumn androidx.compose.material3  List androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  Person androidx.compose.material3  RadioButton androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Scaffold androidx.compose.material3  SettingItem androidx.compose.material3  SnackbarDuration androidx.compose.material3  SnackbarHost androidx.compose.material3  SnackbarHostState androidx.compose.material3  SnackbarResult androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  Switch androidx.compose.material3  System androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  Toast androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  User androidx.compose.material3  VisualTransformation androidx.compose.material3  align androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  buttonColors androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  clip androidx.compose.material3  collectAsState androidx.compose.material3  com androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  delay androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  heightIn androidx.compose.material3  ifEmpty androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  	onFailure androidx.compose.material3  	onSuccess androidx.compose.material3  outlinedButtonColors androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  runFirebaseTests androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  sumOf androidx.compose.material3  trim androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  Error %androidx.compose.material3.AuthResult  Success %androidx.compose.material3.AuthResult  buttonColors )androidx.compose.material3.ButtonDefaults  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onError &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSecondaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  secondaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  Short +androidx.compose.material3.SnackbarDuration  showSnackbar ,androidx.compose.material3.SnackbarHostState  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  vector 7androidx.compose.material3.androidx.compose.ui.graphics  ImageVector >androidx.compose.material3.androidx.compose.ui.graphics.vector  toika androidx.compose.material3.com  netwok $androidx.compose.material3.com.toika  data +androidx.compose.material3.com.toika.netwok  models 0androidx.compose.material3.com.toika.netwok.data  User 7androidx.compose.material3.com.toika.netwok.data.models  Activity androidx.compose.runtime  ActivityResultContracts androidx.compose.runtime  AddFriendScreen androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  	ArrowBack androidx.compose.runtime  
AuthResult androidx.compose.runtime  BottomNavButton androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  ClipData androidx.compose.runtime  ClipboardManager androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Context androidx.compose.runtime  Edit androidx.compose.runtime  EmailVerificationScreen androidx.compose.runtime  	Exception androidx.compose.runtime  	ExitToApp androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FirebaseAuth androidx.compose.runtime  FirebaseDatabase androidx.compose.runtime  FirebaseTestScreen androidx.compose.runtime  
FontWeight androidx.compose.runtime  
FriendItem androidx.compose.runtime  
FriendRequest androidx.compose.runtime  FriendRequestItem androidx.compose.runtime  
FriendsScreen androidx.compose.runtime  Group androidx.compose.runtime  	GroupItem androidx.compose.runtime  
HomeScreen androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Int androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  LaunchedEffect androidx.compose.runtime  
LazyColumn androidx.compose.runtime  List androidx.compose.runtime  LiveStreamScreen androidx.compose.runtime  LoginScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  NavHostController androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  Person androidx.compose.runtime  
ProfileScreen androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RadioButton androidx.compose.runtime  RegisterScreen androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Routes androidx.compose.runtime  Row androidx.compose.runtime  Scaffold androidx.compose.runtime  SettingItem androidx.compose.runtime  SnackbarDuration androidx.compose.runtime  SnackbarHost androidx.compose.runtime  SnackbarHostState androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Switch androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  Toast androidx.compose.runtime  Unit androidx.compose.runtime  User androidx.compose.runtime  VisualTransformation androidx.compose.runtime  align androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  buttonColors androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  clip androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  contains androidx.compose.runtime  delay androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  heightIn androidx.compose.runtime  ifEmpty androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  	onFailure androidx.compose.runtime  	onSuccess androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  runFirebaseTests androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  sumOf androidx.compose.runtime  trim androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  Error #androidx.compose.runtime.AuthResult  Success #androidx.compose.runtime.AuthResult  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  vector 5androidx.compose.runtime.androidx.compose.ui.graphics  ImageVector <androidx.compose.runtime.androidx.compose.ui.graphics.vector  toika androidx.compose.runtime.com  netwok "androidx.compose.runtime.com.toika  data )androidx.compose.runtime.com.toika.netwok  models .androidx.compose.runtime.com.toika.netwok.data  User 5androidx.compose.runtime.com.toika.netwok.data.models  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  heightIn &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Text +androidx.compose.ui.text.input.KeyboardType  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  Text 5androidx.compose.ui.text.input.KeyboardType.Companion  	Companion 3androidx.compose.ui.text.input.VisualTransformation  None 3androidx.compose.ui.text.input.VisualTransformation  None =androidx.compose.ui.text.input.VisualTransformation.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  
AnimeTheme #androidx.core.app.ComponentActivity  
AppNavigation #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  FirebaseAnalytics #androidx.core.app.ComponentActivity  FirebaseHelper #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  logEvent #androidx.core.app.ComponentActivity  mapOf #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  testFirebaseConnection #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  	arguments %androidx.navigation.NavBackStackEntry  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  AddFriendScreen #androidx.navigation.NavGraphBuilder  EmailVerificationScreen #androidx.navigation.NavGraphBuilder  FirebaseTestScreen #androidx.navigation.NavGraphBuilder  
FriendsScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  LiveStreamScreen #androidx.navigation.NavGraphBuilder  LoginScreen #androidx.navigation.NavGraphBuilder  
ProfileScreen #androidx.navigation.NavGraphBuilder  RegisterScreen #androidx.navigation.NavGraphBuilder  Routes #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  Routes %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  GoogleSignIn &com.google.android.gms.auth.api.signin  GoogleSignInAccount &com.google.android.gms.auth.api.signin  GoogleSignInClient &com.google.android.gms.auth.api.signin  GoogleSignInOptions &com.google.android.gms.auth.api.signin  	getClient 3com.google.android.gms.auth.api.signin.GoogleSignIn  getLastSignedInAccount 3com.google.android.gms.auth.api.signin.GoogleSignIn  getSignedInAccountFromIntent 3com.google.android.gms.auth.api.signin.GoogleSignIn  displayName :com.google.android.gms.auth.api.signin.GoogleSignInAccount  email :com.google.android.gms.auth.api.signin.GoogleSignInAccount  idToken :com.google.android.gms.auth.api.signin.GoogleSignInAccount  signInIntent 9com.google.android.gms.auth.api.signin.GoogleSignInClient  signOut 9com.google.android.gms.auth.api.signin.GoogleSignInClient  Builder :com.google.android.gms.auth.api.signin.GoogleSignInOptions  DEFAULT_SIGN_IN :com.google.android.gms.auth.api.signin.GoogleSignInOptions  build Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestEmail Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestIdToken Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  ApiException !com.google.android.gms.common.api  message .com.google.android.gms.common.api.ApiException  
statusCode .com.google.android.gms.common.api.ApiException  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  await !com.google.android.gms.tasks.Task  	getResult !com.google.android.gms.tasks.Task  FirebaseApp com.google.firebase  
initializeApp com.google.firebase.FirebaseApp  FirebaseAnalytics com.google.firebase.analytics  getInstance /com.google.firebase.analytics.FirebaseAnalytics  logEvent /com.google.firebase.analytics.FirebaseAnalytics  AuthCredential com.google.firebase.auth  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  GoogleAuthProvider com.google.firebase.auth  user #com.google.firebase.auth.AuthResult  createUserWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  currentUser %com.google.firebase.auth.FirebaseAuth  getInstance %com.google.firebase.auth.FirebaseAuth  sendPasswordResetEmail %com.google.firebase.auth.FirebaseAuth  signInWithCredential %com.google.firebase.auth.FirebaseAuth  signInWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  signOut %com.google.firebase.auth.FirebaseAuth  displayName %com.google.firebase.auth.FirebaseUser  email %com.google.firebase.auth.FirebaseUser  isEmailVerified %com.google.firebase.auth.FirebaseUser  let %com.google.firebase.auth.FirebaseUser  photoUrl %com.google.firebase.auth.FirebaseUser  reload %com.google.firebase.auth.FirebaseUser  sendEmailVerification %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  
getCredential +com.google.firebase.auth.GoogleAuthProvider  isEmailVerified !com.google.firebase.auth.UserInfo  Boolean com.google.firebase.database  DataSnapshot com.google.firebase.database  
DatabaseError com.google.firebase.database  DatabaseReference com.google.firebase.database  	Exception com.google.firebase.database  FRIENDSHIPS_PATH com.google.firebase.database  FRIEND_REQUESTS_PATH com.google.firebase.database  FirebaseAuth com.google.firebase.database  FirebaseDatabase com.google.firebase.database  Flow com.google.firebase.database  
FriendRequest com.google.firebase.database  FriendRequestStatus com.google.firebase.database  
Friendship com.google.firebase.database  List com.google.firebase.database  Log com.google.firebase.database  Query com.google.firebase.database  Result com.google.firebase.database  String com.google.firebase.database  System com.google.firebase.database  TAG com.google.firebase.database  
USERS_PATH com.google.firebase.database  Unit com.google.firebase.database  User com.google.firebase.database  ValueEventListener com.google.firebase.database  auth com.google.firebase.database  await com.google.firebase.database  callbackFlow com.google.firebase.database  close com.google.firebase.database  database com.google.firebase.database  	emptyList com.google.firebase.database  failure com.google.firebase.database  getFriendsDetails com.google.firebase.database  java com.google.firebase.database  
mutableListOf com.google.firebase.database  substringBefore com.google.firebase.database  success com.google.firebase.database  trySend com.google.firebase.database  children )com.google.firebase.database.DataSnapshot  exists )com.google.firebase.database.DataSnapshot  getValue )com.google.firebase.database.DataSnapshot  ref )com.google.firebase.database.DataSnapshot  value )com.google.firebase.database.DataSnapshot  message *com.google.firebase.database.DatabaseError  addValueEventListener .com.google.firebase.database.DatabaseReference  child .com.google.firebase.database.DatabaseReference  get .com.google.firebase.database.DatabaseReference  key .com.google.firebase.database.DatabaseReference  limitToFirst .com.google.firebase.database.DatabaseReference  orderByChild .com.google.firebase.database.DatabaseReference  push .com.google.firebase.database.DatabaseReference  removeEventListener .com.google.firebase.database.DatabaseReference  removeValue .com.google.firebase.database.DatabaseReference  setValue .com.google.firebase.database.DatabaseReference  getInstance -com.google.firebase.database.FirebaseDatabase  getReference -com.google.firebase.database.FirebaseDatabase  setPersistenceEnabled -com.google.firebase.database.FirebaseDatabase  addValueEventListener "com.google.firebase.database.Query  equalTo "com.google.firebase.database.Query  get "com.google.firebase.database.Query  limitToFirst "com.google.firebase.database.Query  orderByChild "com.google.firebase.database.Query  removeEventListener "com.google.firebase.database.Query  FirebaseStorage com.google.firebase.storage  StorageReference com.google.firebase.storage  getInstance +com.google.firebase.storage.FirebaseStorage  getReference +com.google.firebase.storage.FirebaseStorage  path ,com.google.firebase.storage.StorageReference  
AnimeTheme com.toika.netwok  Any com.toika.netwok  
AppNavigation com.toika.netwok  Application com.toika.netwok  Boolean com.toika.netwok  Bundle com.toika.netwok  ComponentActivity com.toika.netwok  Double com.toika.netwok  	Exception com.toika.netwok  FirebaseAnalytics com.toika.netwok  FirebaseApp com.toika.netwok  FirebaseAuth com.toika.netwok  FirebaseDatabase com.toika.netwok  FirebaseHelper com.toika.netwok  FirebaseStorage com.toika.netwok  Int com.toika.netwok  Log com.toika.netwok  Long com.toika.netwok  MainActivity com.toika.netwok  Map com.toika.netwok  
MaterialTheme com.toika.netwok  Modifier com.toika.netwok  
MyApplication com.toika.netwok  R com.toika.netwok  String com.toika.netwok  Surface com.toika.netwok  System com.toika.netwok  Unit com.toika.netwok  android com.toika.netwok  
component1 com.toika.netwok  
component2 com.toika.netwok  fillMaxSize com.toika.netwok  forEach com.toika.netwok  logEvent com.toika.netwok  mapOf com.toika.netwok  testFirebaseConnection com.toika.netwok  to com.toika.netwok  FirebaseAuth com.toika.netwok.FirebaseHelper  FirebaseDatabase com.toika.netwok.FirebaseHelper  FirebaseStorage com.toika.netwok.FirebaseHelper  Log com.toika.netwok.FirebaseHelper  System com.toika.netwok.FirebaseHelper  TAG com.toika.netwok.FirebaseHelper  android com.toika.netwok.FirebaseHelper  
component1 com.toika.netwok.FirebaseHelper  
component2 com.toika.netwok.FirebaseHelper  getCurrentUser com.toika.netwok.FirebaseHelper  getStorageReference com.toika.netwok.FirebaseHelper  logEvent com.toika.netwok.FirebaseHelper  mapOf com.toika.netwok.FirebaseHelper  testFirebaseConnection com.toika.netwok.FirebaseHelper  to com.toika.netwok.FirebaseHelper  writeToDatabase com.toika.netwok.FirebaseHelper  
AnimeTheme com.toika.netwok.MainActivity  
AppNavigation com.toika.netwok.MainActivity  FirebaseAnalytics com.toika.netwok.MainActivity  FirebaseHelper com.toika.netwok.MainActivity  
MaterialTheme com.toika.netwok.MainActivity  Modifier com.toika.netwok.MainActivity  Surface com.toika.netwok.MainActivity  enableEdgeToEdge com.toika.netwok.MainActivity  fillMaxSize com.toika.netwok.MainActivity  logEvent com.toika.netwok.MainActivity  mapOf com.toika.netwok.MainActivity  
setContent com.toika.netwok.MainActivity  testFirebaseConnection com.toika.netwok.MainActivity  to com.toika.netwok.MainActivity  FirebaseAnalytics com.toika.netwok.MyApplication  FirebaseApp com.toika.netwok.MyApplication  FirebaseAuth com.toika.netwok.MyApplication  FirebaseDatabase com.toika.netwok.MyApplication  FirebaseStorage com.toika.netwok.MyApplication  default_web_client_id com.toika.netwok.R.string  ApiException com.toika.netwok.auth  
AuthResult com.toika.netwok.auth  AuthenticationManager com.toika.netwok.auth  Boolean com.toika.netwok.auth  Context com.toika.netwok.auth  	Exception com.toika.netwok.auth  FirebaseAuth com.toika.netwok.auth  FirebaseDatabase com.toika.netwok.auth  FirebaseUser com.toika.netwok.auth  GoogleAuthProvider com.toika.netwok.auth  GoogleSignIn com.toika.netwok.auth  GoogleSignInAccount com.toika.netwok.auth  GoogleSignInClient com.toika.netwok.auth  GoogleSignInOptions com.toika.netwok.auth  Intent com.toika.netwok.auth  Log com.toika.netwok.auth  R com.toika.netwok.auth  String com.toika.netwok.auth  System com.toika.netwok.auth  TAG com.toika.netwok.auth  User com.toika.netwok.auth  UserInfo com.toika.netwok.auth  await com.toika.netwok.auth  
isNotBlank com.toika.netwok.auth  java com.toika.netwok.auth  let com.toika.netwok.auth  substringBefore com.toika.netwok.auth  take com.toika.netwok.auth  
AuthResult  com.toika.netwok.auth.AuthResult  Error  com.toika.netwok.auth.AuthResult  FirebaseUser  com.toika.netwok.auth.AuthResult  String  com.toika.netwok.auth.AuthResult  Success  com.toika.netwok.auth.AuthResult  message &com.toika.netwok.auth.AuthResult.Error  ApiException +com.toika.netwok.auth.AuthenticationManager  
AuthResult +com.toika.netwok.auth.AuthenticationManager  Boolean +com.toika.netwok.auth.AuthenticationManager  Context +com.toika.netwok.auth.AuthenticationManager  	Exception +com.toika.netwok.auth.AuthenticationManager  FirebaseAuth +com.toika.netwok.auth.AuthenticationManager  FirebaseDatabase +com.toika.netwok.auth.AuthenticationManager  FirebaseUser +com.toika.netwok.auth.AuthenticationManager  GoogleAuthProvider +com.toika.netwok.auth.AuthenticationManager  GoogleSignIn +com.toika.netwok.auth.AuthenticationManager  GoogleSignInAccount +com.toika.netwok.auth.AuthenticationManager  GoogleSignInClient +com.toika.netwok.auth.AuthenticationManager  GoogleSignInOptions +com.toika.netwok.auth.AuthenticationManager  Intent +com.toika.netwok.auth.AuthenticationManager  Log +com.toika.netwok.auth.AuthenticationManager  R +com.toika.netwok.auth.AuthenticationManager  String +com.toika.netwok.auth.AuthenticationManager  System +com.toika.netwok.auth.AuthenticationManager  TAG +com.toika.netwok.auth.AuthenticationManager  User +com.toika.netwok.auth.AuthenticationManager  UserInfo +com.toika.netwok.auth.AuthenticationManager  auth +com.toika.netwok.auth.AuthenticationManager  await +com.toika.netwok.auth.AuthenticationManager  checkEmailVerification +com.toika.netwok.auth.AuthenticationManager  context +com.toika.netwok.auth.AuthenticationManager  createUserWithEmailAndPassword +com.toika.netwok.auth.AuthenticationManager  database +com.toika.netwok.auth.AuthenticationManager  firebaseAuthWithGoogle +com.toika.netwok.auth.AuthenticationManager  getCurrentUser +com.toika.netwok.auth.AuthenticationManager  getGoogleSignInIntent +com.toika.netwok.auth.AuthenticationManager  getUserFromDatabase +com.toika.netwok.auth.AuthenticationManager  getUserInfo +com.toika.netwok.auth.AuthenticationManager  googleSignInClient +com.toika.netwok.auth.AuthenticationManager  handleGoogleSignInResult +com.toika.netwok.auth.AuthenticationManager  
isNotBlank +com.toika.netwok.auth.AuthenticationManager  isUserSignedIn +com.toika.netwok.auth.AuthenticationManager  java +com.toika.netwok.auth.AuthenticationManager  let +com.toika.netwok.auth.AuthenticationManager  pendingUsername +com.toika.netwok.auth.AuthenticationManager  resendEmailVerification +com.toika.netwok.auth.AuthenticationManager  saveUserToDatabase +com.toika.netwok.auth.AuthenticationManager  sendEmailVerification +com.toika.netwok.auth.AuthenticationManager  sendPasswordResetEmail +com.toika.netwok.auth.AuthenticationManager  signInWithEmailAndPassword +com.toika.netwok.auth.AuthenticationManager  signOut +com.toika.netwok.auth.AuthenticationManager  substringBefore +com.toika.netwok.auth.AuthenticationManager  take +com.toika.netwok.auth.AuthenticationManager  ApiException 5com.toika.netwok.auth.AuthenticationManager.Companion  
AuthResult 5com.toika.netwok.auth.AuthenticationManager.Companion  FirebaseAuth 5com.toika.netwok.auth.AuthenticationManager.Companion  FirebaseDatabase 5com.toika.netwok.auth.AuthenticationManager.Companion  GoogleAuthProvider 5com.toika.netwok.auth.AuthenticationManager.Companion  GoogleSignIn 5com.toika.netwok.auth.AuthenticationManager.Companion  GoogleSignInOptions 5com.toika.netwok.auth.AuthenticationManager.Companion  Log 5com.toika.netwok.auth.AuthenticationManager.Companion  R 5com.toika.netwok.auth.AuthenticationManager.Companion  System 5com.toika.netwok.auth.AuthenticationManager.Companion  TAG 5com.toika.netwok.auth.AuthenticationManager.Companion  User 5com.toika.netwok.auth.AuthenticationManager.Companion  UserInfo 5com.toika.netwok.auth.AuthenticationManager.Companion  await 5com.toika.netwok.auth.AuthenticationManager.Companion  
isNotBlank 5com.toika.netwok.auth.AuthenticationManager.Companion  java 5com.toika.netwok.auth.AuthenticationManager.Companion  let 5com.toika.netwok.auth.AuthenticationManager.Companion  substringBefore 5com.toika.netwok.auth.AuthenticationManager.Companion  take 5com.toika.netwok.auth.AuthenticationManager.Companion  isEmailVerified com.toika.netwok.auth.UserInfo  source com.toika.netwok.auth.UserInfo  Boolean com.toika.netwok.data  DataSnapshot com.toika.netwok.data  
DatabaseError com.toika.netwok.data  	Exception com.toika.netwok.data  FRIENDSHIPS_PATH com.toika.netwok.data  FRIEND_REQUESTS_PATH com.toika.netwok.data  FirebaseAuth com.toika.netwok.data  FirebaseDatabase com.toika.netwok.data  Flow com.toika.netwok.data  
FriendRequest com.toika.netwok.data  FriendRequestStatus com.toika.netwok.data  FriendsManager com.toika.netwok.data  
Friendship com.toika.netwok.data  List com.toika.netwok.data  Log com.toika.netwok.data  Result com.toika.netwok.data  String com.toika.netwok.data  System com.toika.netwok.data  TAG com.toika.netwok.data  
USERS_PATH com.toika.netwok.data  Unit com.toika.netwok.data  User com.toika.netwok.data  ValueEventListener com.toika.netwok.data  auth com.toika.netwok.data  await com.toika.netwok.data  callbackFlow com.toika.netwok.data  close com.toika.netwok.data  database com.toika.netwok.data  	emptyList com.toika.netwok.data  failure com.toika.netwok.data  getFriendsDetails com.toika.netwok.data  java com.toika.netwok.data  
mutableListOf com.toika.netwok.data  substringBefore com.toika.netwok.data  success com.toika.netwok.data  trySend com.toika.netwok.data  Boolean $com.toika.netwok.data.FriendsManager  DataSnapshot $com.toika.netwok.data.FriendsManager  
DatabaseError $com.toika.netwok.data.FriendsManager  	Exception $com.toika.netwok.data.FriendsManager  FRIENDSHIPS_PATH $com.toika.netwok.data.FriendsManager  FRIEND_REQUESTS_PATH $com.toika.netwok.data.FriendsManager  FirebaseAuth $com.toika.netwok.data.FriendsManager  FirebaseDatabase $com.toika.netwok.data.FriendsManager  Flow $com.toika.netwok.data.FriendsManager  
FriendRequest $com.toika.netwok.data.FriendsManager  FriendRequestStatus $com.toika.netwok.data.FriendsManager  
Friendship $com.toika.netwok.data.FriendsManager  List $com.toika.netwok.data.FriendsManager  Log $com.toika.netwok.data.FriendsManager  Result $com.toika.netwok.data.FriendsManager  String $com.toika.netwok.data.FriendsManager  System $com.toika.netwok.data.FriendsManager  TAG $com.toika.netwok.data.FriendsManager  
USERS_PATH $com.toika.netwok.data.FriendsManager  Unit $com.toika.netwok.data.FriendsManager  User $com.toika.netwok.data.FriendsManager  ValueEventListener $com.toika.netwok.data.FriendsManager  acceptFriendRequest $com.toika.netwok.data.FriendsManager  addFriendDirectly $com.toika.netwok.data.FriendsManager  auth $com.toika.netwok.data.FriendsManager  await $com.toika.netwok.data.FriendsManager  
awaitClose $com.toika.netwok.data.FriendsManager  callbackFlow $com.toika.netwok.data.FriendsManager  checkExistingFriendRequest $com.toika.netwok.data.FriendsManager  checkExistingFriendship $com.toika.netwok.data.FriendsManager  close $com.toika.netwok.data.FriendsManager  database $com.toika.netwok.data.FriendsManager  	emptyList $com.toika.netwok.data.FriendsManager  ensureCurrentUserExists $com.toika.netwok.data.FriendsManager  failure $com.toika.netwok.data.FriendsManager  
getFriends $com.toika.netwok.data.FriendsManager  getFriendsDetails $com.toika.netwok.data.FriendsManager  getIncomingFriendRequests $com.toika.netwok.data.FriendsManager  java $com.toika.netwok.data.FriendsManager  
mutableListOf $com.toika.netwok.data.FriendsManager  rejectFriendRequest $com.toika.netwok.data.FriendsManager  removeFriend $com.toika.netwok.data.FriendsManager  searchUserByUid $com.toika.netwok.data.FriendsManager  substringBefore $com.toika.netwok.data.FriendsManager  success $com.toika.netwok.data.FriendsManager  trySend $com.toika.netwok.data.FriendsManager  	Exception .com.toika.netwok.data.FriendsManager.Companion  FRIENDSHIPS_PATH .com.toika.netwok.data.FriendsManager.Companion  FRIEND_REQUESTS_PATH .com.toika.netwok.data.FriendsManager.Companion  FirebaseAuth .com.toika.netwok.data.FriendsManager.Companion  FirebaseDatabase .com.toika.netwok.data.FriendsManager.Companion  
FriendRequest .com.toika.netwok.data.FriendsManager.Companion  FriendRequestStatus .com.toika.netwok.data.FriendsManager.Companion  
Friendship .com.toika.netwok.data.FriendsManager.Companion  Log .com.toika.netwok.data.FriendsManager.Companion  Result .com.toika.netwok.data.FriendsManager.Companion  System .com.toika.netwok.data.FriendsManager.Companion  TAG .com.toika.netwok.data.FriendsManager.Companion  
USERS_PATH .com.toika.netwok.data.FriendsManager.Companion  Unit .com.toika.netwok.data.FriendsManager.Companion  User .com.toika.netwok.data.FriendsManager.Companion  auth .com.toika.netwok.data.FriendsManager.Companion  await .com.toika.netwok.data.FriendsManager.Companion  
awaitClose .com.toika.netwok.data.FriendsManager.Companion  callbackFlow .com.toika.netwok.data.FriendsManager.Companion  close .com.toika.netwok.data.FriendsManager.Companion  database .com.toika.netwok.data.FriendsManager.Companion  	emptyList .com.toika.netwok.data.FriendsManager.Companion  failure .com.toika.netwok.data.FriendsManager.Companion  getFriendsDetails .com.toika.netwok.data.FriendsManager.Companion  java .com.toika.netwok.data.FriendsManager.Companion  
mutableListOf .com.toika.netwok.data.FriendsManager.Companion  substringBefore .com.toika.netwok.data.FriendsManager.Companion  success .com.toika.netwok.data.FriendsManager.Companion  trySend .com.toika.netwok.data.FriendsManager.Companion  Boolean com.toika.netwok.data.models  DataSnapshot com.toika.netwok.data.models  
DatabaseError com.toika.netwok.data.models  	Exception com.toika.netwok.data.models  FRIENDSHIPS_PATH com.toika.netwok.data.models  FRIEND_REQUESTS_PATH com.toika.netwok.data.models  FirebaseAuth com.toika.netwok.data.models  FirebaseDatabase com.toika.netwok.data.models  Flow com.toika.netwok.data.models  
FriendRequest com.toika.netwok.data.models  FriendRequestStatus com.toika.netwok.data.models  
Friendship com.toika.netwok.data.models  List com.toika.netwok.data.models  Log com.toika.netwok.data.models  Long com.toika.netwok.data.models  Result com.toika.netwok.data.models  String com.toika.netwok.data.models  System com.toika.netwok.data.models  TAG com.toika.netwok.data.models  
USERS_PATH com.toika.netwok.data.models  Unit com.toika.netwok.data.models  User com.toika.netwok.data.models  ValueEventListener com.toika.netwok.data.models  auth com.toika.netwok.data.models  await com.toika.netwok.data.models  callbackFlow com.toika.netwok.data.models  close com.toika.netwok.data.models  database com.toika.netwok.data.models  	emptyList com.toika.netwok.data.models  failure com.toika.netwok.data.models  getFriendsDetails com.toika.netwok.data.models  java com.toika.netwok.data.models  
mutableListOf com.toika.netwok.data.models  substringBefore com.toika.netwok.data.models  success com.toika.netwok.data.models  trySend com.toika.netwok.data.models  FriendRequestStatus *com.toika.netwok.data.models.FriendRequest  
fromUserEmail *com.toika.netwok.data.models.FriendRequest  
fromUserId *com.toika.netwok.data.models.FriendRequest  fromUserName *com.toika.netwok.data.models.FriendRequest  id *com.toika.netwok.data.models.FriendRequest  status *com.toika.netwok.data.models.FriendRequest  toUserId *com.toika.netwok.data.models.FriendRequest  ACCEPTED 0com.toika.netwok.data.models.FriendRequestStatus  PENDING 0com.toika.netwok.data.models.FriendRequestStatus  REJECTED 0com.toika.netwok.data.models.FriendRequestStatus  name 0com.toika.netwok.data.models.FriendRequestStatus  user1Id 'com.toika.netwok.data.models.Friendship  user2Id 'com.toika.netwok.data.models.Friendship  displayName !com.toika.netwok.data.models.User  email !com.toika.netwok.data.models.User  isOnline !com.toika.netwok.data.models.User  let !com.toika.netwok.data.models.User  uid !com.toika.netwok.data.models.User  AddFriendScreen com.toika.netwok.navigation  
AppNavigation com.toika.netwok.navigation  
Composable com.toika.netwok.navigation  EmailVerificationScreen com.toika.netwok.navigation  FirebaseTestScreen com.toika.netwok.navigation  
FriendsScreen com.toika.netwok.navigation  
HomeScreen com.toika.netwok.navigation  LiveStreamScreen com.toika.netwok.navigation  LoginScreen com.toika.netwok.navigation  NavHostController com.toika.netwok.navigation  
ProfileScreen com.toika.netwok.navigation  RegisterScreen com.toika.netwok.navigation  Routes com.toika.netwok.navigation  remember com.toika.netwok.navigation  
ADD_FRIEND "com.toika.netwok.navigation.Routes  EMAIL_VERIFICATION "com.toika.netwok.navigation.Routes  
FIREBASE_TEST "com.toika.netwok.navigation.Routes  FRIENDS "com.toika.netwok.navigation.Routes  HOME "com.toika.netwok.navigation.Routes  LIVE_STREAM "com.toika.netwok.navigation.Routes  LOGIN "com.toika.netwok.navigation.Routes  PROFILE "com.toika.netwok.navigation.Routes  REGISTER "com.toika.netwok.navigation.Routes  Activity com.toika.netwok.ui.screens  ActivityResultContracts com.toika.netwok.ui.screens  AddFriendScreen com.toika.netwok.ui.screens  AlertDialog com.toika.netwok.ui.screens  	Alignment com.toika.netwok.ui.screens  Arrangement com.toika.netwok.ui.screens  	ArrowBack com.toika.netwok.ui.screens  
AuthResult com.toika.netwok.ui.screens  BottomNavButton com.toika.netwok.ui.screens  Box com.toika.netwok.ui.screens  Button com.toika.netwok.ui.screens  ButtonDefaults com.toika.netwok.ui.screens  Card com.toika.netwok.ui.screens  CardDefaults com.toika.netwok.ui.screens  CircleShape com.toika.netwok.ui.screens  CircularProgressIndicator com.toika.netwok.ui.screens  ClipData com.toika.netwok.ui.screens  ClipboardManager com.toika.netwok.ui.screens  Column com.toika.netwok.ui.screens  
Composable com.toika.netwok.ui.screens  Context com.toika.netwok.ui.screens  Edit com.toika.netwok.ui.screens  EmailVerificationScreen com.toika.netwok.ui.screens  	Exception com.toika.netwok.ui.screens  	ExitToApp com.toika.netwok.ui.screens  ExperimentalMaterial3Api com.toika.netwok.ui.screens  FirebaseAuth com.toika.netwok.ui.screens  FirebaseDatabase com.toika.netwok.ui.screens  FirebaseTestScreen com.toika.netwok.ui.screens  
FontWeight com.toika.netwok.ui.screens  
FriendItem com.toika.netwok.ui.screens  
FriendRequest com.toika.netwok.ui.screens  FriendRequestItem com.toika.netwok.ui.screens  
FriendsScreen com.toika.netwok.ui.screens  Group com.toika.netwok.ui.screens  	GroupItem com.toika.netwok.ui.screens  GroupsScreen com.toika.netwok.ui.screens  
HomeScreen com.toika.netwok.ui.screens  Icon com.toika.netwok.ui.screens  
IconButton com.toika.netwok.ui.screens  Icons com.toika.netwok.ui.screens  Int com.toika.netwok.ui.screens  KeyboardOptions com.toika.netwok.ui.screens  KeyboardType com.toika.netwok.ui.screens  LaunchedEffect com.toika.netwok.ui.screens  
LazyColumn com.toika.netwok.ui.screens  List com.toika.netwok.ui.screens  LiveStreamScreen com.toika.netwok.ui.screens  LoginScreen com.toika.netwok.ui.screens  
MaterialTheme com.toika.netwok.ui.screens  Modifier com.toika.netwok.ui.screens  OptIn com.toika.netwok.ui.screens  OutlinedButton com.toika.netwok.ui.screens  OutlinedTextField com.toika.netwok.ui.screens  PasswordVisualTransformation com.toika.netwok.ui.screens  Person com.toika.netwok.ui.screens  
ProfileScreen com.toika.netwok.ui.screens  RadioButton com.toika.netwok.ui.screens  RegisterScreen com.toika.netwok.ui.screens  RoundedCornerShape com.toika.netwok.ui.screens  Row com.toika.netwok.ui.screens  Scaffold com.toika.netwok.ui.screens  SettingItem com.toika.netwok.ui.screens  SimpleAddFriendScreen com.toika.netwok.ui.screens  SimpleFriendsScreen com.toika.netwok.ui.screens  SnackbarDuration com.toika.netwok.ui.screens  SnackbarHost com.toika.netwok.ui.screens  SnackbarHostState com.toika.netwok.ui.screens  Spacer com.toika.netwok.ui.screens  String com.toika.netwok.ui.screens  Switch com.toika.netwok.ui.screens  System com.toika.netwok.ui.screens  Text com.toika.netwok.ui.screens  	TextAlign com.toika.netwok.ui.screens  
TextButton com.toika.netwok.ui.screens  Toast com.toika.netwok.ui.screens  Unit com.toika.netwok.ui.screens  User com.toika.netwok.ui.screens  VisualTransformation com.toika.netwok.ui.screens  align com.toika.netwok.ui.screens  android com.toika.netwok.ui.screens  androidx com.toika.netwok.ui.screens  buttonColors com.toika.netwok.ui.screens  
cardColors com.toika.netwok.ui.screens  	clickable com.toika.netwok.ui.screens  clip com.toika.netwok.ui.screens  collectAsState com.toika.netwok.ui.screens  com com.toika.netwok.ui.screens  contains com.toika.netwok.ui.screens  delay com.toika.netwok.ui.screens  	emptyList com.toika.netwok.ui.screens  fillMaxSize com.toika.netwok.ui.screens  fillMaxWidth com.toika.netwok.ui.screens  forEach com.toika.netwok.ui.screens  getValue com.toika.netwok.ui.screens  height com.toika.netwok.ui.screens  heightIn com.toika.netwok.ui.screens  ifEmpty com.toika.netwok.ui.screens  
isNotBlank com.toika.netwok.ui.screens  
isNotEmpty com.toika.netwok.ui.screens  launch com.toika.netwok.ui.screens  let com.toika.netwok.ui.screens  listOf com.toika.netwok.ui.screens  mutableStateOf com.toika.netwok.ui.screens  	onFailure com.toika.netwok.ui.screens  	onSuccess com.toika.netwok.ui.screens  outlinedButtonColors com.toika.netwok.ui.screens  padding com.toika.netwok.ui.screens  plus com.toika.netwok.ui.screens  provideDelegate com.toika.netwok.ui.screens  remember com.toika.netwok.ui.screens  rememberCoroutineScope com.toika.netwok.ui.screens  runFirebaseTests com.toika.netwok.ui.screens  setValue com.toika.netwok.ui.screens  size com.toika.netwok.ui.screens  spacedBy com.toika.netwok.ui.screens  sumOf com.toika.netwok.ui.screens  trim com.toika.netwok.ui.screens  weight com.toika.netwok.ui.screens  width com.toika.netwok.ui.screens  Error &com.toika.netwok.ui.screens.AuthResult  Success &com.toika.netwok.ui.screens.AuthResult  description !com.toika.netwok.ui.screens.Group  memberCount !com.toika.netwok.ui.screens.Group  name !com.toika.netwok.ui.screens.Group  unreadMessages !com.toika.netwok.ui.screens.Group  compose $com.toika.netwok.ui.screens.androidx  ui ,com.toika.netwok.ui.screens.androidx.compose  graphics /com.toika.netwok.ui.screens.androidx.compose.ui  vector 8com.toika.netwok.ui.screens.androidx.compose.ui.graphics  ImageVector ?com.toika.netwok.ui.screens.androidx.compose.ui.graphics.vector  toika com.toika.netwok.ui.screens.com  netwok %com.toika.netwok.ui.screens.com.toika  data ,com.toika.netwok.ui.screens.com.toika.netwok  models 1com.toika.netwok.ui.screens.com.toika.netwok.data  User 8com.toika.netwok.ui.screens.com.toika.netwok.data.models  
AnimeTheme com.toika.netwok.ui.theme  Boolean com.toika.netwok.ui.theme  Build com.toika.netwok.ui.theme  
Composable com.toika.netwok.ui.theme  DarkColorScheme com.toika.netwok.ui.theme  
FontFamily com.toika.netwok.ui.theme  
FontWeight com.toika.netwok.ui.theme  LightColorScheme com.toika.netwok.ui.theme  Pink40 com.toika.netwok.ui.theme  Pink80 com.toika.netwok.ui.theme  Purple40 com.toika.netwok.ui.theme  Purple80 com.toika.netwok.ui.theme  PurpleGrey40 com.toika.netwok.ui.theme  PurpleGrey80 com.toika.netwok.ui.theme  
Typography com.toika.netwok.ui.theme  Unit com.toika.netwok.ui.theme  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  matches java.util.regex.Matcher  matcher java.util.regex.Pattern  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  	Throwable kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  let kotlin  	onFailure kotlin  	onSuccess kotlin  plus kotlin  to kotlin  not kotlin.Boolean  sp 
kotlin.Double  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  dec 
kotlin.Int  inc 
kotlin.Int  toString 
kotlin.Int  minus kotlin.Long  	Companion 
kotlin.Result  failure 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  contains 
kotlin.String  ifEmpty 
kotlin.String  
isNotBlank 
kotlin.String  length 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  substringBefore 
kotlin.String  take 
kotlin.String  to 
kotlin.String  trim 
kotlin.String  message kotlin.Throwable  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  Set kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  ifEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  plus kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  take kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  plus kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  Entry kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  iterator "kotlin.collections.MutableIterable  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  java 
kotlin.jvm  contains 
kotlin.ranges  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  contains kotlin.sequences  forEach kotlin.sequences  ifEmpty kotlin.sequences  plus kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  contains kotlin.text  forEach kotlin.text  ifEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  plus kotlin.text  substringBefore kotlin.text  sumOf kotlin.text  take kotlin.text  trim kotlin.text  CoroutineScope kotlinx.coroutines  Delay kotlinx.coroutines  Job kotlinx.coroutines  delay kotlinx.coroutines  launch kotlinx.coroutines  SnackbarDuration !kotlinx.coroutines.CoroutineScope  delay !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  plus !kotlinx.coroutines.CoroutineScope  runFirebaseTests !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  
ChannelResult kotlinx.coroutines.channels  
ProducerScope kotlinx.coroutines.channels  
awaitClose kotlinx.coroutines.channels  FRIENDSHIPS_PATH )kotlinx.coroutines.channels.ProducerScope  FRIEND_REQUESTS_PATH )kotlinx.coroutines.channels.ProducerScope  
FriendRequest )kotlinx.coroutines.channels.ProducerScope  FriendRequestStatus )kotlinx.coroutines.channels.ProducerScope  
Friendship )kotlinx.coroutines.channels.ProducerScope  Log )kotlinx.coroutines.channels.ProducerScope  TAG )kotlinx.coroutines.channels.ProducerScope  auth )kotlinx.coroutines.channels.ProducerScope  
awaitClose )kotlinx.coroutines.channels.ProducerScope  close )kotlinx.coroutines.channels.ProducerScope  database )kotlinx.coroutines.channels.ProducerScope  	emptyList )kotlinx.coroutines.channels.ProducerScope  getFriendsDetails )kotlinx.coroutines.channels.ProducerScope  java )kotlinx.coroutines.channels.ProducerScope  
mutableListOf )kotlinx.coroutines.channels.ProducerScope  trySend )kotlinx.coroutines.channels.ProducerScope  Flow kotlinx.coroutines.flow  callbackFlow kotlinx.coroutines.flow  collectAsState kotlinx.coroutines.flow.Flow  await kotlinx.coroutines.tasks  Manifest android  CAMERA android.Manifest.permission  RECORD_AUDIO android.Manifest.permission  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  
LiveStream "androidx.compose.foundation.layout  Manifest "androidx.compose.foundation.layout  arrayOf "androidx.compose.foundation.layout  fold "androidx.compose.foundation.layout  isBlank "androidx.compose.foundation.layout  
LiveStream androidx.compose.material3  Manifest androidx.compose.material3  arrayOf androidx.compose.material3  fold androidx.compose.material3  isBlank androidx.compose.material3  
LiveStream androidx.compose.runtime  Manifest androidx.compose.runtime  arrayOf androidx.compose.runtime  fold androidx.compose.runtime  isBlank androidx.compose.runtime  ChildEventListener com.google.firebase.database  Context com.google.firebase.database  Int com.google.firebase.database  
LiveStream com.google.firebase.database  
MESSAGES_PATH com.google.firebase.database  MessageType com.google.firebase.database  STREAMS_PATH com.google.firebase.database  
StreamMessage com.google.firebase.database  StreamViewer com.google.firebase.database  USER_STREAMS_PATH com.google.firebase.database  UUID com.google.firebase.database  VIEWERS_PATH com.google.firebase.database  
WebRTCManager com.google.firebase.database  let com.google.firebase.database  onNewMessage com.google.firebase.database  onViewerCountChanged com.google.firebase.database  sortedBy com.google.firebase.database  sortedByDescending com.google.firebase.database  
childrenCount )com.google.firebase.database.DataSnapshot  addChildEventListener "com.google.firebase.database.Query  limitToLast "com.google.firebase.database.Query  Int com.toika.netwok.data.models  
LiveStream com.toika.netwok.data.models  MessageType com.toika.netwok.data.models  
StreamMessage com.toika.netwok.data.models  StreamStats com.toika.netwok.data.models  StreamViewer com.toika.netwok.data.models  allowedViewers 'com.toika.netwok.data.models.LiveStream  copy 'com.toika.netwok.data.models.LiveStream  isActive 'com.toika.netwok.data.models.LiveStream  	isPrivate 'com.toika.netwok.data.models.LiveStream  let 'com.toika.netwok.data.models.LiveStream  	startTime 'com.toika.netwok.data.models.LiveStream  streamId 'com.toika.netwok.data.models.LiveStream  JOIN (com.toika.netwok.data.models.MessageType  LEAVE (com.toika.netwok.data.models.MessageType  TEXT (com.toika.netwok.data.models.MessageType  let *com.toika.netwok.data.models.StreamMessage  	messageId *com.toika.netwok.data.models.StreamMessage  isActive )com.toika.netwok.data.models.StreamViewer  joinedAt )com.toika.netwok.data.models.StreamViewer  let )com.toika.netwok.data.models.StreamViewer  
viewerName )com.toika.netwok.data.models.StreamViewer  Boolean com.toika.netwok.streaming  ChildEventListener com.toika.netwok.streaming  Context com.toika.netwok.streaming  DataSnapshot com.toika.netwok.streaming  
DatabaseError com.toika.netwok.streaming  	Exception com.toika.netwok.streaming  FirebaseAuth com.toika.netwok.streaming  FirebaseDatabase com.toika.netwok.streaming  Int com.toika.netwok.streaming  List com.toika.netwok.streaming  
LiveStream com.toika.netwok.streaming  LiveStreamManager com.toika.netwok.streaming  Log com.toika.netwok.streaming  
MESSAGES_PATH com.toika.netwok.streaming  MessageType com.toika.netwok.streaming  Result com.toika.netwok.streaming  STREAMS_PATH com.toika.netwok.streaming  
StreamMessage com.toika.netwok.streaming  StreamViewer com.toika.netwok.streaming  String com.toika.netwok.streaming  System com.toika.netwok.streaming  TAG com.toika.netwok.streaming  USER_STREAMS_PATH com.toika.netwok.streaming  UUID com.toika.netwok.streaming  Unit com.toika.netwok.streaming  VIEWERS_PATH com.toika.netwok.streaming  ValueEventListener com.toika.netwok.streaming  
WebRTCManager com.toika.netwok.streaming  await com.toika.netwok.streaming  database com.toika.netwok.streaming  	emptyList com.toika.netwok.streaming  failure com.toika.netwok.streaming  java com.toika.netwok.streaming  let com.toika.netwok.streaming  
mutableListOf com.toika.netwok.streaming  onNewMessage com.toika.netwok.streaming  onViewerCountChanged com.toika.netwok.streaming  sortedBy com.toika.netwok.streaming  sortedByDescending com.toika.netwok.streaming  success com.toika.netwok.streaming  Boolean ,com.toika.netwok.streaming.LiveStreamManager  ChildEventListener ,com.toika.netwok.streaming.LiveStreamManager  Context ,com.toika.netwok.streaming.LiveStreamManager  DataSnapshot ,com.toika.netwok.streaming.LiveStreamManager  
DatabaseError ,com.toika.netwok.streaming.LiveStreamManager  	Exception ,com.toika.netwok.streaming.LiveStreamManager  FirebaseAuth ,com.toika.netwok.streaming.LiveStreamManager  FirebaseDatabase ,com.toika.netwok.streaming.LiveStreamManager  Int ,com.toika.netwok.streaming.LiveStreamManager  List ,com.toika.netwok.streaming.LiveStreamManager  
LiveStream ,com.toika.netwok.streaming.LiveStreamManager  Log ,com.toika.netwok.streaming.LiveStreamManager  
MESSAGES_PATH ,com.toika.netwok.streaming.LiveStreamManager  MessageType ,com.toika.netwok.streaming.LiveStreamManager  Result ,com.toika.netwok.streaming.LiveStreamManager  STREAMS_PATH ,com.toika.netwok.streaming.LiveStreamManager  
StreamMessage ,com.toika.netwok.streaming.LiveStreamManager  StreamViewer ,com.toika.netwok.streaming.LiveStreamManager  String ,com.toika.netwok.streaming.LiveStreamManager  System ,com.toika.netwok.streaming.LiveStreamManager  TAG ,com.toika.netwok.streaming.LiveStreamManager  USER_STREAMS_PATH ,com.toika.netwok.streaming.LiveStreamManager  UUID ,com.toika.netwok.streaming.LiveStreamManager  Unit ,com.toika.netwok.streaming.LiveStreamManager  VIEWERS_PATH ,com.toika.netwok.streaming.LiveStreamManager  ValueEventListener ,com.toika.netwok.streaming.LiveStreamManager  
WebRTCManager ,com.toika.netwok.streaming.LiveStreamManager  auth ,com.toika.netwok.streaming.LiveStreamManager  await ,com.toika.netwok.streaming.LiveStreamManager  context ,com.toika.netwok.streaming.LiveStreamManager  
currentStream ,com.toika.netwok.streaming.LiveStreamManager  database ,com.toika.netwok.streaming.LiveStreamManager  	emptyList ,com.toika.netwok.streaming.LiveStreamManager  failure ,com.toika.netwok.streaming.LiveStreamManager  getActiveStreams ,com.toika.netwok.streaming.LiveStreamManager  isStreaming ,com.toika.netwok.streaming.LiveStreamManager  	isViewing ,com.toika.netwok.streaming.LiveStreamManager  java ,com.toika.netwok.streaming.LiveStreamManager  let ,com.toika.netwok.streaming.LiveStreamManager  
mutableListOf ,com.toika.netwok.streaming.LiveStreamManager  onNewMessage ,com.toika.netwok.streaming.LiveStreamManager  
onStreamEnded ,com.toika.netwok.streaming.LiveStreamManager  onStreamStarted ,com.toika.netwok.streaming.LiveStreamManager  onViewerCountChanged ,com.toika.netwok.streaming.LiveStreamManager  sendSystemMessage ,com.toika.netwok.streaming.LiveStreamManager  sortedBy ,com.toika.netwok.streaming.LiveStreamManager  sortedByDescending ,com.toika.netwok.streaming.LiveStreamManager  startLiveStream ,com.toika.netwok.streaming.LiveStreamManager  startMessageMonitoring ,com.toika.netwok.streaming.LiveStreamManager  startViewerCountMonitoring ,com.toika.netwok.streaming.LiveStreamManager  success ,com.toika.netwok.streaming.LiveStreamManager  
webRTCManager ,com.toika.netwok.streaming.LiveStreamManager  	Exception 6com.toika.netwok.streaming.LiveStreamManager.Companion  FirebaseAuth 6com.toika.netwok.streaming.LiveStreamManager.Companion  FirebaseDatabase 6com.toika.netwok.streaming.LiveStreamManager.Companion  
LiveStream 6com.toika.netwok.streaming.LiveStreamManager.Companion  Log 6com.toika.netwok.streaming.LiveStreamManager.Companion  
MESSAGES_PATH 6com.toika.netwok.streaming.LiveStreamManager.Companion  MessageType 6com.toika.netwok.streaming.LiveStreamManager.Companion  Result 6com.toika.netwok.streaming.LiveStreamManager.Companion  STREAMS_PATH 6com.toika.netwok.streaming.LiveStreamManager.Companion  
StreamMessage 6com.toika.netwok.streaming.LiveStreamManager.Companion  StreamViewer 6com.toika.netwok.streaming.LiveStreamManager.Companion  System 6com.toika.netwok.streaming.LiveStreamManager.Companion  TAG 6com.toika.netwok.streaming.LiveStreamManager.Companion  USER_STREAMS_PATH 6com.toika.netwok.streaming.LiveStreamManager.Companion  UUID 6com.toika.netwok.streaming.LiveStreamManager.Companion  Unit 6com.toika.netwok.streaming.LiveStreamManager.Companion  VIEWERS_PATH 6com.toika.netwok.streaming.LiveStreamManager.Companion  
WebRTCManager 6com.toika.netwok.streaming.LiveStreamManager.Companion  await 6com.toika.netwok.streaming.LiveStreamManager.Companion  database 6com.toika.netwok.streaming.LiveStreamManager.Companion  	emptyList 6com.toika.netwok.streaming.LiveStreamManager.Companion  failure 6com.toika.netwok.streaming.LiveStreamManager.Companion  java 6com.toika.netwok.streaming.LiveStreamManager.Companion  let 6com.toika.netwok.streaming.LiveStreamManager.Companion  
mutableListOf 6com.toika.netwok.streaming.LiveStreamManager.Companion  onNewMessage 6com.toika.netwok.streaming.LiveStreamManager.Companion  onViewerCountChanged 6com.toika.netwok.streaming.LiveStreamManager.Companion  sortedBy 6com.toika.netwok.streaming.LiveStreamManager.Companion  sortedByDescending 6com.toika.netwok.streaming.LiveStreamManager.Companion  success 6com.toika.netwok.streaming.LiveStreamManager.Companion  
LiveStream com.toika.netwok.ui.screens  Manifest com.toika.netwok.ui.screens  arrayOf com.toika.netwok.ui.screens  fold com.toika.netwok.ui.screens  isBlank com.toika.netwok.ui.screens  Boolean com.toika.netwok.webrtc  Context com.toika.netwok.webrtc  	Exception com.toika.netwok.webrtc  Log com.toika.netwok.webrtc  TAG com.toika.netwok.webrtc  Unit com.toika.netwok.webrtc  
WebRTCManager com.toika.netwok.webrtc  Boolean %com.toika.netwok.webrtc.WebRTCManager  Context %com.toika.netwok.webrtc.WebRTCManager  	Exception %com.toika.netwok.webrtc.WebRTCManager  Log %com.toika.netwok.webrtc.WebRTCManager  TAG %com.toika.netwok.webrtc.WebRTCManager  Unit %com.toika.netwok.webrtc.WebRTCManager  dispose %com.toika.netwok.webrtc.WebRTCManager  isAudioEnabled %com.toika.netwok.webrtc.WebRTCManager  isStreaming %com.toika.netwok.webrtc.WebRTCManager  isVideoEnabled %com.toika.netwok.webrtc.WebRTCManager  onLocalStreamReady %com.toika.netwok.webrtc.WebRTCManager  onStreamStopped %com.toika.netwok.webrtc.WebRTCManager  startLocalStream %com.toika.netwok.webrtc.WebRTCManager  stopLocalStream %com.toika.netwok.webrtc.WebRTCManager  switchCamera %com.toika.netwok.webrtc.WebRTCManager  toggleAudio %com.toika.netwok.webrtc.WebRTCManager  toggleVideo %com.toika.netwok.webrtc.WebRTCManager  Log /com.toika.netwok.webrtc.WebRTCManager.Companion  TAG /com.toika.netwok.webrtc.WebRTCManager.Companion  UUID 	java.util  
randomUUID java.util.UUID  toString java.util.UUID  arrayOf kotlin  fold kotlin  toInt kotlin.Long  fold 
kotlin.Result  isBlank 
kotlin.String  fold kotlin.collections  sortedBy kotlin.collections  sortedByDescending kotlin.collections  contains kotlin.collections.List  get kotlin.collections.Map  sortedBy kotlin.collections.MutableList  sortedByDescending kotlin.collections.MutableList  fold kotlin.sequences  sortedBy kotlin.sequences  sortedByDescending kotlin.sequences  fold kotlin.text  isBlank kotlin.text  Manifest !kotlinx.coroutines.CoroutineScope  arrayOf !kotlinx.coroutines.CoroutineScope  fold !kotlinx.coroutines.CoroutineScope  Camera androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  Preview androidx.camera.core  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  PreviewView androidx.camera.view  CameraSelector  androidx.camera.view.PreviewView  
ContextCompat  androidx.camera.view.PreviewView  Preview  androidx.camera.view.PreviewView  ProcessCameraProvider  androidx.camera.view.PreviewView  also  androidx.camera.view.PreviewView  apply  androidx.camera.view.PreviewView  surfaceProvider  androidx.camera.view.PreviewView  ActiveStreamScreen /androidx.compose.animation.AnimatedContentScope  System /androidx.compose.animation.AnimatedContentScope  com /androidx.compose.animation.AnimatedContentScope  
background androidx.compose.foundation  AndroidView "androidx.compose.foundation.layout  CameraSelector "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  
ContextCompat "androidx.compose.foundation.layout  Date "androidx.compose.foundation.layout  Locale "androidx.compose.foundation.layout  Preview "androidx.compose.foundation.layout  PreviewView "androidx.compose.foundation.layout  ProcessCameraProvider "androidx.compose.foundation.layout  
StreamCard "androidx.compose.foundation.layout  StreamViewer "androidx.compose.foundation.layout  TextOverflow "androidx.compose.foundation.layout  also "androidx.compose.foundation.layout  apply "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  
cardElevation "androidx.compose.foundation.layout  take "androidx.compose.foundation.layout  	uppercase "androidx.compose.foundation.layout  AndroidView +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  	ArrowBack +androidx.compose.foundation.layout.BoxScope  CameraSelector +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  
ContextCompat +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  
IconButton +androidx.compose.foundation.layout.BoxScope  	PlayArrow +androidx.compose.foundation.layout.BoxScope  Preview +androidx.compose.foundation.layout.BoxScope  PreviewView +androidx.compose.foundation.layout.BoxScope  ProcessCameraProvider +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  System +androidx.compose.foundation.layout.BoxScope  also +androidx.compose.foundation.layout.BoxScope  apply +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  
isNotBlank +androidx.compose.foundation.layout.BoxScope  take +androidx.compose.foundation.layout.BoxScope  	uppercase +androidx.compose.foundation.layout.BoxScope  weight +androidx.compose.foundation.layout.BoxScope  width +androidx.compose.foundation.layout.BoxScope  Color .androidx.compose.foundation.layout.ColumnScope  
PaddingValues .androidx.compose.foundation.layout.ColumnScope  SnackbarDuration .androidx.compose.foundation.layout.ColumnScope  
StreamCard .androidx.compose.foundation.layout.ColumnScope  System .androidx.compose.foundation.layout.ColumnScope  TextOverflow .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  fold .androidx.compose.foundation.layout.ColumnScope  take .androidx.compose.foundation.layout.ColumnScope  	uppercase .androidx.compose.foundation.layout.ColumnScope  Color +androidx.compose.foundation.layout.RowScope  
PaddingValues +androidx.compose.foundation.layout.RowScope  RoundedCornerShape +androidx.compose.foundation.layout.RowScope  Warning +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  height +androidx.compose.foundation.layout.RowScope  
isNotBlank +androidx.compose.foundation.layout.RowScope  take +androidx.compose.foundation.layout.RowScope  	uppercase +androidx.compose.foundation.layout.RowScope  
FontWeight .androidx.compose.foundation.lazy.LazyItemScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyItemScope  Modifier .androidx.compose.foundation.lazy.LazyItemScope  SnackbarDuration .androidx.compose.foundation.lazy.LazyItemScope  
StreamCard .androidx.compose.foundation.lazy.LazyItemScope  Text .androidx.compose.foundation.lazy.LazyItemScope  dp .androidx.compose.foundation.lazy.LazyItemScope  fold .androidx.compose.foundation.lazy.LazyItemScope  padding .androidx.compose.foundation.lazy.LazyItemScope  sp .androidx.compose.foundation.lazy.LazyItemScope  
FontWeight .androidx.compose.foundation.lazy.LazyListScope  
MaterialTheme .androidx.compose.foundation.lazy.LazyListScope  Modifier .androidx.compose.foundation.lazy.LazyListScope  SnackbarDuration .androidx.compose.foundation.lazy.LazyListScope  
StreamCard .androidx.compose.foundation.lazy.LazyListScope  Text .androidx.compose.foundation.lazy.LazyListScope  dp .androidx.compose.foundation.lazy.LazyListScope  fold .androidx.compose.foundation.lazy.LazyListScope  item .androidx.compose.foundation.lazy.LazyListScope  padding .androidx.compose.foundation.lazy.LazyListScope  sp .androidx.compose.foundation.lazy.LazyListScope  Warning ,androidx.compose.material.icons.Icons.Filled  Warning &androidx.compose.material.icons.filled  AndroidView androidx.compose.material3  CameraSelector androidx.compose.material3  
CardElevation androidx.compose.material3  Color androidx.compose.material3  
ContextCompat androidx.compose.material3  Date androidx.compose.material3  Locale androidx.compose.material3  
PaddingValues androidx.compose.material3  Preview androidx.compose.material3  PreviewView androidx.compose.material3  ProcessCameraProvider androidx.compose.material3  
StreamCard androidx.compose.material3  StreamViewer androidx.compose.material3  TextOverflow androidx.compose.material3  also androidx.compose.material3  apply androidx.compose.material3  
background androidx.compose.material3  
cardElevation androidx.compose.material3  take androidx.compose.material3  	uppercase androidx.compose.material3  
cardElevation 'androidx.compose.material3.CardDefaults  ActiveStreamScreen androidx.compose.runtime  AndroidView androidx.compose.runtime  CameraSelector androidx.compose.runtime  Color androidx.compose.runtime  
ContextCompat androidx.compose.runtime  Date androidx.compose.runtime  Locale androidx.compose.runtime  
PaddingValues androidx.compose.runtime  Preview androidx.compose.runtime  PreviewView androidx.compose.runtime  ProcessCameraProvider androidx.compose.runtime  
StreamCard androidx.compose.runtime  StreamViewer androidx.compose.runtime  TextOverflow androidx.compose.runtime  also androidx.compose.runtime  apply androidx.compose.runtime  
background androidx.compose.runtime  
cardElevation androidx.compose.runtime  take androidx.compose.runtime  	uppercase androidx.compose.runtime  TopStart androidx.compose.ui.Alignment  TopStart 'androidx.compose.ui.Alignment.Companion  
background androidx.compose.ui.Modifier  
background &androidx.compose.ui.Modifier.Companion  Black "androidx.compose.ui.graphics.Color  	Companion "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Transparent "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  Red ,androidx.compose.ui.graphics.Color.Companion  Transparent ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  LocalLifecycleOwner androidx.compose.ui.platform  TextOverflow androidx.compose.ui.text.style  	Companion +androidx.compose.ui.text.style.TextOverflow  Ellipsis +androidx.compose.ui.text.style.TextOverflow  Ellipsis 5androidx.compose.ui.text.style.TextOverflow.Companion  AndroidView androidx.compose.ui.viewinterop  
ContextCompat androidx.core.content  getMainExecutor #androidx.core.content.ContextCompat  LifecycleOwner androidx.lifecycle  ActiveStreamScreen #androidx.navigation.NavGraphBuilder  System #androidx.navigation.NavGraphBuilder  com #androidx.navigation.NavGraphBuilder  ListenableFuture !com.google.common.util.concurrent  addListener 2com.google.common.util.concurrent.ListenableFuture  get 2com.google.common.util.concurrent.ListenableFuture  description 'com.toika.netwok.data.models.LiveStream  hostName 'com.toika.netwok.data.models.LiveStream  hostProfileImage 'com.toika.netwok.data.models.LiveStream  title 'com.toika.netwok.data.models.LiveStream  viewerCount 'com.toika.netwok.data.models.LiveStream  ActiveStreamScreen com.toika.netwok.navigation  System com.toika.netwok.navigation  com com.toika.netwok.navigation  
ACTIVE_STREAM "com.toika.netwok.navigation.Routes  
endLiveStream ,com.toika.netwok.streaming.LiveStreamManager  
getViewers ,com.toika.netwok.streaming.LiveStreamManager  
joinStream ,com.toika.netwok.streaming.LiveStreamManager  switchCamera ,com.toika.netwok.streaming.LiveStreamManager  toggleAudio ,com.toika.netwok.streaming.LiveStreamManager  toggleVideo ,com.toika.netwok.streaming.LiveStreamManager  	Alignment com.toika.netwok.ui.components  Arrangement com.toika.netwok.ui.components  Box com.toika.netwok.ui.components  Button com.toika.netwok.ui.components  Card com.toika.netwok.ui.components  CardDefaults com.toika.netwok.ui.components  CircleShape com.toika.netwok.ui.components  Color com.toika.netwok.ui.components  Column com.toika.netwok.ui.components  
Composable com.toika.netwok.ui.components  Date com.toika.netwok.ui.components  ExperimentalMaterial3Api com.toika.netwok.ui.components  
FontWeight com.toika.netwok.ui.components  Icon com.toika.netwok.ui.components  Icons com.toika.netwok.ui.components  
LiveStream com.toika.netwok.ui.components  Locale com.toika.netwok.ui.components  
MaterialTheme com.toika.netwok.ui.components  Modifier com.toika.netwok.ui.components  OptIn com.toika.netwok.ui.components  
PaddingValues com.toika.netwok.ui.components  RoundedCornerShape com.toika.netwok.ui.components  Row com.toika.netwok.ui.components  Spacer com.toika.netwok.ui.components  
StreamCard com.toika.netwok.ui.components  StreamCardSkeleton com.toika.netwok.ui.components  System com.toika.netwok.ui.components  Text com.toika.netwok.ui.components  TextOverflow com.toika.netwok.ui.components  Unit com.toika.netwok.ui.components  align com.toika.netwok.ui.components  
background com.toika.netwok.ui.components  
cardColors com.toika.netwok.ui.components  
cardElevation com.toika.netwok.ui.components  fillMaxSize com.toika.netwok.ui.components  fillMaxWidth com.toika.netwok.ui.components  height com.toika.netwok.ui.components  
isNotBlank com.toika.netwok.ui.components  padding com.toika.netwok.ui.components  size com.toika.netwok.ui.components  take com.toika.netwok.ui.components  	uppercase com.toika.netwok.ui.components  weight com.toika.netwok.ui.components  width com.toika.netwok.ui.components  ActiveStreamScreen com.toika.netwok.ui.screens  AndroidView com.toika.netwok.ui.screens  CameraSelector com.toika.netwok.ui.screens  Color com.toika.netwok.ui.screens  
ContextCompat com.toika.netwok.ui.screens  
PaddingValues com.toika.netwok.ui.screens  Preview com.toika.netwok.ui.screens  PreviewView com.toika.netwok.ui.screens  ProcessCameraProvider com.toika.netwok.ui.screens  
StreamCard com.toika.netwok.ui.screens  StreamViewer com.toika.netwok.ui.screens  also com.toika.netwok.ui.screens  apply com.toika.netwok.ui.screens  
background com.toika.netwok.ui.screens  take com.toika.netwok.ui.screens  	uppercase com.toika.netwok.ui.screens  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  	Alignment 	java.util  Arrangement 	java.util  Box 	java.util  Button 	java.util  Card 	java.util  CardDefaults 	java.util  CircleShape 	java.util  Color 	java.util  Column 	java.util  
Composable 	java.util  Date 	java.util  ExperimentalMaterial3Api 	java.util  
FontWeight 	java.util  Icon 	java.util  Icons 	java.util  
LiveStream 	java.util  Locale 	java.util  
MaterialTheme 	java.util  Modifier 	java.util  OptIn 	java.util  
PaddingValues 	java.util  RoundedCornerShape 	java.util  Row 	java.util  Spacer 	java.util  System 	java.util  Text 	java.util  TextOverflow 	java.util  Unit 	java.util  align 	java.util  
background 	java.util  
cardColors 	java.util  
cardElevation 	java.util  fillMaxSize 	java.util  fillMaxWidth 	java.util  height 	java.util  
isNotBlank 	java.util  padding 	java.util  size 	java.util  take 	java.util  	uppercase 	java.util  weight 	java.util  width 	java.util  
getDefault java.util.Locale  Executor java.util.concurrent  also kotlin  apply kotlin  div kotlin.Long  	uppercase 
kotlin.String  	uppercase kotlin.text  MEDIA_PROJECTION_SERVICE android.content.Context  MediaProjectionManager android.media.projection  createScreenCaptureIntent /android.media.projection.MediaProjectionManager  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  MediaProjectionManager "androidx.compose.foundation.layout  CircularProgressIndicator +androidx.compose.foundation.layout.BoxScope  Context +androidx.compose.foundation.layout.BoxScope  launch +androidx.compose.foundation.layout.BoxScope  launch +androidx.compose.foundation.layout.RowScope  MediaProjectionManager androidx.compose.material3  MediaProjectionManager androidx.compose.runtime  Green "androidx.compose.ui.graphics.Color  Green ,androidx.compose.ui.graphics.Color.Companion  android com.google.firebase.database  mapOf com.google.firebase.database  to com.google.firebase.database  updateChildren .com.google.firebase.database.DatabaseReference  content $com.google.firebase.database.android  Intent ,com.google.firebase.database.android.content  android com.toika.netwok.streaming  mapOf com.toika.netwok.streaming  to com.toika.netwok.streaming  android ,com.toika.netwok.streaming.LiveStreamManager  mapOf ,com.toika.netwok.streaming.LiveStreamManager  startScreenShare ,com.toika.netwok.streaming.LiveStreamManager  stopScreenShare ,com.toika.netwok.streaming.LiveStreamManager  to ,com.toika.netwok.streaming.LiveStreamManager  mapOf 6com.toika.netwok.streaming.LiveStreamManager.Companion  to 6com.toika.netwok.streaming.LiveStreamManager.Companion  content 4com.toika.netwok.streaming.LiveStreamManager.android  Intent <com.toika.netwok.streaming.LiveStreamManager.android.content  content "com.toika.netwok.streaming.android  Intent *com.toika.netwok.streaming.android.content  MediaProjectionManager com.toika.netwok.ui.screens  android com.toika.netwok.webrtc  android %com.toika.netwok.webrtc.WebRTCManager  startScreenShare %com.toika.netwok.webrtc.WebRTCManager  stopScreenShare %com.toika.netwok.webrtc.WebRTCManager  content -com.toika.netwok.webrtc.WebRTCManager.android  Intent 5com.toika.netwok.webrtc.WebRTCManager.android.content  content com.toika.netwok.webrtc.android  Intent 'com.toika.netwok.webrtc.android.content  ACTION_START_CAPTURE android.app  ACTION_STOP_CAPTURE android.app  Boolean android.app  Build android.app  
CHANNEL_ID android.app  Context android.app  DisplayManager android.app  DisplayMetrics android.app  EXTRA_RESULT_CODE android.app  EXTRA_RESULT_DATA android.app  	Exception android.app  IBinder android.app  Int android.app  Intent android.app  Log android.app  MediaProjection android.app  MediaProjectionManager android.app  
MediaRecorder android.app  NOTIFICATION_ID android.app  Notification android.app  NotificationChannel android.app  NotificationCompat android.app  NotificationManager android.app  
PendingIntent android.app  R android.app  START_NOT_STICKY android.app  ScreenCaptureService android.app  Service android.app  Suppress android.app  Surface android.app  System android.app  TAG android.app  VirtualDisplay android.app  
WindowManager android.app  apply android.app  displayMetrics android.app  externalCacheDir android.app  java android.app  RESULT_CANCELED android.app.Activity  apply android.app.NotificationChannel  description android.app.NotificationChannel  setShowBadge android.app.NotificationChannel  IMPORTANCE_LOW android.app.NotificationManager  createNotificationChannel android.app.NotificationManager  FLAG_IMMUTABLE android.app.PendingIntent  FLAG_UPDATE_CURRENT android.app.PendingIntent  
getService android.app.PendingIntent  ACTION_START_CAPTURE android.app.Service  ACTION_STOP_CAPTURE android.app.Service  Activity android.app.Service  Build android.app.Service  
CHANNEL_ID android.app.Service  Context android.app.Service  DisplayManager android.app.Service  DisplayMetrics android.app.Service  EXTRA_RESULT_CODE android.app.Service  EXTRA_RESULT_DATA android.app.Service  	Exception android.app.Service  Intent android.app.Service  Log android.app.Service  MediaProjectionManager android.app.Service  
MediaRecorder android.app.Service  NOTIFICATION_ID android.app.Service  NotificationChannel android.app.Service  NotificationCompat android.app.Service  NotificationManager android.app.Service  
PendingIntent android.app.Service  R android.app.Service  START_NOT_STICKY android.app.Service  ScreenCaptureService android.app.Service  Suppress android.app.Service  Surface android.app.Service  System android.app.Service  TAG android.app.Service  
WindowManager android.app.Service  apply android.app.Service  displayMetrics android.app.Service  externalCacheDir android.app.Service  java android.app.Service  onCreate android.app.Service  	onDestroy android.app.Service  startForeground android.app.Service  stopSelf android.app.Service  
ComponentName android.content  ACTION_START_CAPTURE android.content.Context  ACTION_STOP_CAPTURE android.content.Context  Activity android.content.Context  Build android.content.Context  
CHANNEL_ID android.content.Context  Context android.content.Context  DisplayManager android.content.Context  DisplayMetrics android.content.Context  EXTRA_RESULT_CODE android.content.Context  EXTRA_RESULT_DATA android.content.Context  	Exception android.content.Context  Intent android.content.Context  Log android.content.Context  MediaProjectionManager android.content.Context  
MediaRecorder android.content.Context  NOTIFICATION_ID android.content.Context  NotificationChannel android.content.Context  NotificationCompat android.content.Context  NotificationManager android.content.Context  
PendingIntent android.content.Context  R android.content.Context  START_NOT_STICKY android.content.Context  ScreenCaptureService android.content.Context  Suppress android.content.Context  System android.content.Context  TAG android.content.Context  WINDOW_SERVICE android.content.Context  
WindowManager android.content.Context  apply android.content.Context  displayMetrics android.content.Context  externalCacheDir android.content.Context  java android.content.Context  startForegroundService android.content.Context  startService android.content.Context  ACTION_START_CAPTURE android.content.ContextWrapper  ACTION_STOP_CAPTURE android.content.ContextWrapper  Activity android.content.ContextWrapper  Build android.content.ContextWrapper  
CHANNEL_ID android.content.ContextWrapper  Context android.content.ContextWrapper  DisplayManager android.content.ContextWrapper  DisplayMetrics android.content.ContextWrapper  EXTRA_RESULT_CODE android.content.ContextWrapper  EXTRA_RESULT_DATA android.content.ContextWrapper  	Exception android.content.ContextWrapper  Intent android.content.ContextWrapper  Log android.content.ContextWrapper  MediaProjectionManager android.content.ContextWrapper  
MediaRecorder android.content.ContextWrapper  NOTIFICATION_ID android.content.ContextWrapper  NotificationChannel android.content.ContextWrapper  NotificationCompat android.content.ContextWrapper  NotificationManager android.content.ContextWrapper  
PendingIntent android.content.ContextWrapper  R android.content.ContextWrapper  START_NOT_STICKY android.content.ContextWrapper  ScreenCaptureService android.content.ContextWrapper  Suppress android.content.ContextWrapper  System android.content.ContextWrapper  TAG android.content.ContextWrapper  
WindowManager android.content.ContextWrapper  apply android.content.ContextWrapper  displayMetrics android.content.ContextWrapper  externalCacheDir android.content.ContextWrapper  getSystemService android.content.ContextWrapper  java android.content.ContextWrapper  ACTION_STOP_CAPTURE android.content.Intent  ScreenCaptureService android.content.Intent  action android.content.Intent  android android.content.Intent  apply android.content.Intent  getIntExtra android.content.Intent  getParcelableExtra android.content.Intent  putExtra android.content.Intent  PixelFormat android.graphics  DisplayManager android.hardware.display  VirtualDisplay android.hardware.display   VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR 'android.hardware.display.DisplayManager  release 'android.hardware.display.VirtualDisplay  
MediaRecorder 
android.media  
MediaRecorder android.media.MediaRecorder  System android.media.MediaRecorder  apply android.media.MediaRecorder  displayMetrics android.media.MediaRecorder  externalCacheDir android.media.MediaRecorder  prepare android.media.MediaRecorder  release android.media.MediaRecorder  setAudioEncoder android.media.MediaRecorder  setAudioSource android.media.MediaRecorder  
setOutputFile android.media.MediaRecorder  setOutputFormat android.media.MediaRecorder  setVideoEncoder android.media.MediaRecorder  setVideoEncodingBitRate android.media.MediaRecorder  setVideoFrameRate android.media.MediaRecorder  setVideoSize android.media.MediaRecorder  setVideoSource android.media.MediaRecorder  start android.media.MediaRecorder  stop android.media.MediaRecorder  surface android.media.MediaRecorder  AAC (android.media.MediaRecorder.AudioEncoder  MIC 'android.media.MediaRecorder.AudioSource  MPEG_4 (android.media.MediaRecorder.OutputFormat  H264 (android.media.MediaRecorder.VideoEncoder  SURFACE 'android.media.MediaRecorder.VideoSource  MediaProjection android.media.projection  createVirtualDisplay (android.media.projection.MediaProjection  stop (android.media.projection.MediaProjection  getMediaProjection /android.media.projection.MediaProjectionManager  IBinder 
android.os  O android.os.Build.VERSION_CODES  DisplayMetrics android.util  
densityDpi android.util.DisplayMetrics  heightPixels android.util.DisplayMetrics  widthPixels android.util.DisplayMetrics  Surface android.view  
WindowManager android.view  
getMetrics android.view.Display  defaultDisplay android.view.WindowManager  ViewStreamScreen /androidx.compose.animation.AnimatedContentScope  DisposableEffect "androidx.compose.foundation.layout  Divider "androidx.compose.foundation.layout  MessageItem "androidx.compose.foundation.layout  OutlinedTextFieldDefaults "androidx.compose.foundation.layout  
StreamMessage "androidx.compose.foundation.layout  colors "androidx.compose.foundation.layout  reversed "androidx.compose.foundation.layout  Divider +androidx.compose.foundation.layout.BoxScope  
LazyColumn +androidx.compose.foundation.layout.BoxScope  MessageItem +androidx.compose.foundation.layout.BoxScope  OutlinedTextField +androidx.compose.foundation.layout.BoxScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.BoxScope  Send +androidx.compose.foundation.layout.BoxScope  
TextButton +androidx.compose.foundation.layout.BoxScope  colors +androidx.compose.foundation.layout.BoxScope  items +androidx.compose.foundation.layout.BoxScope  reversed +androidx.compose.foundation.layout.BoxScope  Divider .androidx.compose.foundation.layout.ColumnScope  MessageItem .androidx.compose.foundation.layout.ColumnScope  OutlinedTextFieldDefaults .androidx.compose.foundation.layout.ColumnScope  Send .androidx.compose.foundation.layout.ColumnScope  colors .androidx.compose.foundation.layout.ColumnScope  reversed .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField +androidx.compose.foundation.layout.RowScope  OutlinedTextFieldDefaults +androidx.compose.foundation.layout.RowScope  Send +androidx.compose.foundation.layout.RowScope  System +androidx.compose.foundation.layout.RowScope  colors +androidx.compose.foundation.layout.RowScope  	Alignment .androidx.compose.foundation.lazy.LazyItemScope  Icon .androidx.compose.foundation.lazy.LazyItemScope  Icons .androidx.compose.foundation.lazy.LazyItemScope  MessageItem .androidx.compose.foundation.lazy.LazyItemScope  Person .androidx.compose.foundation.lazy.LazyItemScope  Row .androidx.compose.foundation.lazy.LazyItemScope  Spacer .androidx.compose.foundation.lazy.LazyItemScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyItemScope  size .androidx.compose.foundation.lazy.LazyItemScope  width .androidx.compose.foundation.lazy.LazyItemScope  	Alignment .androidx.compose.foundation.lazy.LazyListScope  Icon .androidx.compose.foundation.lazy.LazyListScope  Icons .androidx.compose.foundation.lazy.LazyListScope  MessageItem .androidx.compose.foundation.lazy.LazyListScope  Person .androidx.compose.foundation.lazy.LazyListScope  Row .androidx.compose.foundation.lazy.LazyListScope  Spacer .androidx.compose.foundation.lazy.LazyListScope  fillMaxWidth .androidx.compose.foundation.lazy.LazyListScope  reversed .androidx.compose.foundation.lazy.LazyListScope  size .androidx.compose.foundation.lazy.LazyListScope  width .androidx.compose.foundation.lazy.LazyListScope  Send ,androidx.compose.material.icons.Icons.Filled  Send &androidx.compose.material.icons.filled  DisposableEffect androidx.compose.material3  Divider androidx.compose.material3  MessageItem androidx.compose.material3  OutlinedTextFieldDefaults androidx.compose.material3  
StreamMessage androidx.compose.material3  TextFieldColors androidx.compose.material3  colors androidx.compose.material3  reversed androidx.compose.material3  colors 4androidx.compose.material3.OutlinedTextFieldDefaults  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  Divider androidx.compose.runtime  MessageItem androidx.compose.runtime  OutlinedTextFieldDefaults androidx.compose.runtime  
StreamMessage androidx.compose.runtime  ViewStreamScreen androidx.compose.runtime  colors androidx.compose.runtime  reversed androidx.compose.runtime  launch .androidx.compose.runtime.DisposableEffectScope  	onDispose .androidx.compose.runtime.DisposableEffectScope  Blue "androidx.compose.ui.graphics.Color  Gray "androidx.compose.ui.graphics.Color  Blue ,androidx.compose.ui.graphics.Color.Companion  Gray ,androidx.compose.ui.graphics.Color.Companion  NotificationCompat androidx.core.app  Builder $androidx.core.app.NotificationCompat  	addAction ,androidx.core.app.NotificationCompat.Builder  build ,androidx.core.app.NotificationCompat.Builder  setContentText ,androidx.core.app.NotificationCompat.Builder  setContentTitle ,androidx.core.app.NotificationCompat.Builder  
setOngoing ,androidx.core.app.NotificationCompat.Builder  setSmallIcon ,androidx.core.app.NotificationCompat.Builder  ViewStreamScreen #androidx.navigation.NavGraphBuilder  createDummyStreams com.google.firebase.database  listOf com.google.firebase.database  ic_launcher_foreground com.toika.netwok.R.drawable  hostId 'com.toika.netwok.data.models.LiveStream  message *com.toika.netwok.data.models.StreamMessage  
senderName *com.toika.netwok.data.models.StreamMessage  ViewStreamScreen com.toika.netwok.navigation  VIEW_STREAM "com.toika.netwok.navigation.Routes  ACTION_START_CAPTURE com.toika.netwok.streaming  ACTION_STOP_CAPTURE com.toika.netwok.streaming  Activity com.toika.netwok.streaming  Build com.toika.netwok.streaming  
CHANNEL_ID com.toika.netwok.streaming  DisplayManager com.toika.netwok.streaming  DisplayMetrics com.toika.netwok.streaming  EXTRA_RESULT_CODE com.toika.netwok.streaming  EXTRA_RESULT_DATA com.toika.netwok.streaming  IBinder com.toika.netwok.streaming  Intent com.toika.netwok.streaming  MediaProjection com.toika.netwok.streaming  MediaProjectionManager com.toika.netwok.streaming  
MediaRecorder com.toika.netwok.streaming  NOTIFICATION_ID com.toika.netwok.streaming  Notification com.toika.netwok.streaming  NotificationChannel com.toika.netwok.streaming  NotificationCompat com.toika.netwok.streaming  NotificationManager com.toika.netwok.streaming  
PendingIntent com.toika.netwok.streaming  R com.toika.netwok.streaming  START_NOT_STICKY com.toika.netwok.streaming  ScreenCaptureService com.toika.netwok.streaming  Service com.toika.netwok.streaming  Suppress com.toika.netwok.streaming  Surface com.toika.netwok.streaming  VirtualDisplay com.toika.netwok.streaming  
WindowManager com.toika.netwok.streaming  apply com.toika.netwok.streaming  createDummyStreams com.toika.netwok.streaming  displayMetrics com.toika.netwok.streaming  externalCacheDir com.toika.netwok.streaming  listOf com.toika.netwok.streaming  createDummyStreams ,com.toika.netwok.streaming.LiveStreamManager  leaveStream ,com.toika.netwok.streaming.LiveStreamManager  listOf ,com.toika.netwok.streaming.LiveStreamManager  sendMessage ,com.toika.netwok.streaming.LiveStreamManager  createDummyStreams 6com.toika.netwok.streaming.LiveStreamManager.Companion  listOf 6com.toika.netwok.streaming.LiveStreamManager.Companion  ACTION_START_CAPTURE /com.toika.netwok.streaming.ScreenCaptureService  ACTION_STOP_CAPTURE /com.toika.netwok.streaming.ScreenCaptureService  Activity /com.toika.netwok.streaming.ScreenCaptureService  Boolean /com.toika.netwok.streaming.ScreenCaptureService  Build /com.toika.netwok.streaming.ScreenCaptureService  
CHANNEL_ID /com.toika.netwok.streaming.ScreenCaptureService  	Companion /com.toika.netwok.streaming.ScreenCaptureService  Context /com.toika.netwok.streaming.ScreenCaptureService  DisplayManager /com.toika.netwok.streaming.ScreenCaptureService  DisplayMetrics /com.toika.netwok.streaming.ScreenCaptureService  EXTRA_RESULT_CODE /com.toika.netwok.streaming.ScreenCaptureService  EXTRA_RESULT_DATA /com.toika.netwok.streaming.ScreenCaptureService  	Exception /com.toika.netwok.streaming.ScreenCaptureService  IBinder /com.toika.netwok.streaming.ScreenCaptureService  Int /com.toika.netwok.streaming.ScreenCaptureService  Intent /com.toika.netwok.streaming.ScreenCaptureService  Log /com.toika.netwok.streaming.ScreenCaptureService  MediaProjection /com.toika.netwok.streaming.ScreenCaptureService  MediaProjectionManager /com.toika.netwok.streaming.ScreenCaptureService  
MediaRecorder /com.toika.netwok.streaming.ScreenCaptureService  NOTIFICATION_ID /com.toika.netwok.streaming.ScreenCaptureService  Notification /com.toika.netwok.streaming.ScreenCaptureService  NotificationChannel /com.toika.netwok.streaming.ScreenCaptureService  NotificationCompat /com.toika.netwok.streaming.ScreenCaptureService  NotificationManager /com.toika.netwok.streaming.ScreenCaptureService  
PendingIntent /com.toika.netwok.streaming.ScreenCaptureService  R /com.toika.netwok.streaming.ScreenCaptureService  START_NOT_STICKY /com.toika.netwok.streaming.ScreenCaptureService  ScreenCaptureService /com.toika.netwok.streaming.ScreenCaptureService  Suppress /com.toika.netwok.streaming.ScreenCaptureService  Surface /com.toika.netwok.streaming.ScreenCaptureService  System /com.toika.netwok.streaming.ScreenCaptureService  TAG /com.toika.netwok.streaming.ScreenCaptureService  VirtualDisplay /com.toika.netwok.streaming.ScreenCaptureService  
WindowManager /com.toika.netwok.streaming.ScreenCaptureService  apply /com.toika.netwok.streaming.ScreenCaptureService  createNotification /com.toika.netwok.streaming.ScreenCaptureService  createNotificationChannel /com.toika.netwok.streaming.ScreenCaptureService  createVirtualDisplay /com.toika.netwok.streaming.ScreenCaptureService  displayMetrics /com.toika.netwok.streaming.ScreenCaptureService  externalCacheDir /com.toika.netwok.streaming.ScreenCaptureService  getSystemService /com.toika.netwok.streaming.ScreenCaptureService  isRecording /com.toika.netwok.streaming.ScreenCaptureService  java /com.toika.netwok.streaming.ScreenCaptureService  mediaProjection /com.toika.netwok.streaming.ScreenCaptureService  mediaProjectionManager /com.toika.netwok.streaming.ScreenCaptureService  
mediaRecorder /com.toika.netwok.streaming.ScreenCaptureService  setupMediaRecorder /com.toika.netwok.streaming.ScreenCaptureService  startForeground /com.toika.netwok.streaming.ScreenCaptureService  startScreenCapture /com.toika.netwok.streaming.ScreenCaptureService  stopScreenCapture /com.toika.netwok.streaming.ScreenCaptureService  stopSelf /com.toika.netwok.streaming.ScreenCaptureService  virtualDisplay /com.toika.netwok.streaming.ScreenCaptureService  
windowManager /com.toika.netwok.streaming.ScreenCaptureService  ACTION_START_CAPTURE 9com.toika.netwok.streaming.ScreenCaptureService.Companion  ACTION_STOP_CAPTURE 9com.toika.netwok.streaming.ScreenCaptureService.Companion  Activity 9com.toika.netwok.streaming.ScreenCaptureService.Companion  Build 9com.toika.netwok.streaming.ScreenCaptureService.Companion  
CHANNEL_ID 9com.toika.netwok.streaming.ScreenCaptureService.Companion  Context 9com.toika.netwok.streaming.ScreenCaptureService.Companion  DisplayManager 9com.toika.netwok.streaming.ScreenCaptureService.Companion  DisplayMetrics 9com.toika.netwok.streaming.ScreenCaptureService.Companion  EXTRA_RESULT_CODE 9com.toika.netwok.streaming.ScreenCaptureService.Companion  EXTRA_RESULT_DATA 9com.toika.netwok.streaming.ScreenCaptureService.Companion  Intent 9com.toika.netwok.streaming.ScreenCaptureService.Companion  Log 9com.toika.netwok.streaming.ScreenCaptureService.Companion  
MediaRecorder 9com.toika.netwok.streaming.ScreenCaptureService.Companion  NOTIFICATION_ID 9com.toika.netwok.streaming.ScreenCaptureService.Companion  NotificationChannel 9com.toika.netwok.streaming.ScreenCaptureService.Companion  NotificationCompat 9com.toika.netwok.streaming.ScreenCaptureService.Companion  NotificationManager 9com.toika.netwok.streaming.ScreenCaptureService.Companion  
PendingIntent 9com.toika.netwok.streaming.ScreenCaptureService.Companion  R 9com.toika.netwok.streaming.ScreenCaptureService.Companion  START_NOT_STICKY 9com.toika.netwok.streaming.ScreenCaptureService.Companion  ScreenCaptureService 9com.toika.netwok.streaming.ScreenCaptureService.Companion  System 9com.toika.netwok.streaming.ScreenCaptureService.Companion  TAG 9com.toika.netwok.streaming.ScreenCaptureService.Companion  apply 9com.toika.netwok.streaming.ScreenCaptureService.Companion  displayMetrics 9com.toika.netwok.streaming.ScreenCaptureService.Companion  externalCacheDir 9com.toika.netwok.streaming.ScreenCaptureService.Companion  java 9com.toika.netwok.streaming.ScreenCaptureService.Companion  DisposableEffect com.toika.netwok.ui.screens  Divider com.toika.netwok.ui.screens  MessageItem com.toika.netwok.ui.screens  OutlinedTextFieldDefaults com.toika.netwok.ui.screens  
StreamMessage com.toika.netwok.ui.screens  ViewStreamScreen com.toika.netwok.ui.screens  colors com.toika.netwok.ui.screens  reversed com.toika.netwok.ui.screens  Intent com.toika.netwok.webrtc  ScreenCaptureService com.toika.netwok.webrtc  apply com.toika.netwok.webrtc  java com.toika.netwok.webrtc  Intent %com.toika.netwok.webrtc.WebRTCManager  ScreenCaptureService %com.toika.netwok.webrtc.WebRTCManager  apply %com.toika.netwok.webrtc.WebRTCManager  context %com.toika.netwok.webrtc.WebRTCManager  java %com.toika.netwok.webrtc.WebRTCManager  Intent /com.toika.netwok.webrtc.WebRTCManager.Companion  ScreenCaptureService /com.toika.netwok.webrtc.WebRTCManager.Companion  android /com.toika.netwok.webrtc.WebRTCManager.Companion  apply /com.toika.netwok.webrtc.WebRTCManager.Companion  java /com.toika.netwok.webrtc.WebRTCManager.Companion  File java.io  IOException java.io  
Comparator 	java.util  Suppress kotlin  or 
kotlin.Int  times 
kotlin.Int  reversed kotlin.collections  reversed kotlin.collections.List  addAll kotlin.collections.MutableList  isEmpty kotlin.collections.MutableList  reversed kotlin.comparisons  CharProgression 
kotlin.ranges  IntProgression 
kotlin.ranges  LongProgression 
kotlin.ranges  UIntProgression 
kotlin.ranges  ULongProgression 
kotlin.ranges  reversed 
kotlin.ranges  reversed kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           