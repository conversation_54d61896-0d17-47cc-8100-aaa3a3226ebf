  Activity android.app  Application android.app  
AnimeTheme android.app.Activity  
AppNavigation android.app.Activity  FirebaseAnalytics android.app.Activity  FirebaseHelper android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  	RESULT_OK android.app.Activity  Surface android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  logEvent android.app.Activity  mapOf android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  testFirebaseConnection android.app.Activity  to android.app.Activity  FirebaseAnalytics android.app.Application  FirebaseApp android.app.Application  FirebaseAuth android.app.Application  FirebaseDatabase android.app.Application  FirebaseStorage android.app.Application  onCreate android.app.Application  ClipData android.content  ClipboardManager android.content  Context android.content  Intent android.content  newPlainText android.content.ClipData  setPrimaryClip  android.content.ClipboardManager  
AnimeTheme android.content.Context  
AppNavigation android.content.Context  CLIPBOARD_SERVICE android.content.Context  FirebaseAnalytics android.content.Context  FirebaseApp android.content.Context  FirebaseAuth android.content.Context  FirebaseDatabase android.content.Context  FirebaseHelper android.content.Context  FirebaseStorage android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  	getString android.content.Context  getSystemService android.content.Context  logEvent android.content.Context  mapOf android.content.Context  packageName android.content.Context  	resources android.content.Context  
setContent android.content.Context  testFirebaseConnection android.content.Context  to android.content.Context  
AnimeTheme android.content.ContextWrapper  
AppNavigation android.content.ContextWrapper  FirebaseAnalytics android.content.ContextWrapper  FirebaseApp android.content.ContextWrapper  FirebaseAuth android.content.ContextWrapper  FirebaseDatabase android.content.ContextWrapper  FirebaseHelper android.content.ContextWrapper  FirebaseStorage android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  logEvent android.content.ContextWrapper  mapOf android.content.ContextWrapper  
setContent android.content.ContextWrapper  testFirebaseConnection android.content.ContextWrapper  to android.content.ContextWrapper  
getIdentifier android.content.res.Resources  Uri android.net  toString android.net.Uri  Build 
android.os  Bundle 
android.os  
putBoolean android.os.BaseBundle  	putDouble android.os.BaseBundle  putInt android.os.BaseBundle  putLong android.os.BaseBundle  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
putBoolean android.os.Bundle  	putDouble android.os.Bundle  putInt android.os.Bundle  putLong android.os.Bundle  	putString android.os.Bundle  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  
EMAIL_ADDRESS android.util.Patterns  
AnimeTheme  android.view.ContextThemeWrapper  
AppNavigation  android.view.ContextThemeWrapper  FirebaseAnalytics  android.view.ContextThemeWrapper  FirebaseHelper  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  logEvent  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  testFirebaseConnection  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
AnimeTheme #androidx.activity.ComponentActivity  
AppNavigation #androidx.activity.ComponentActivity  FirebaseAnalytics #androidx.activity.ComponentActivity  FirebaseHelper #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  logEvent #androidx.activity.ComponentActivity  mapOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  testFirebaseConnection #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResult androidx.activity.result  ActivityResultLauncher androidx.activity.result  data 'androidx.activity.result.ActivityResult  
resultCode 'androidx.activity.result.ActivityResult  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  AnimatedContentScope androidx.compose.animation  AddFriendScreen /androidx.compose.animation.AnimatedContentScope  FirebaseTestScreen /androidx.compose.animation.AnimatedContentScope  
FriendsScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  LiveStreamScreen /androidx.compose.animation.AnimatedContentScope  LoginScreen /androidx.compose.animation.AnimatedContentScope  
ProfileScreen /androidx.compose.animation.AnimatedContentScope  RegisterScreen /androidx.compose.animation.AnimatedContentScope  Routes /androidx.compose.animation.AnimatedContentScope  ScrollState androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Activity "androidx.compose.foundation.layout  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  	ArrowBack "androidx.compose.foundation.layout  
AuthResult "androidx.compose.foundation.layout  BottomNavButton "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  ClipData "androidx.compose.foundation.layout  ClipboardManager "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  	Exception "androidx.compose.foundation.layout  	ExitToApp "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FirebaseAuth "androidx.compose.foundation.layout  FirebaseDatabase "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
FriendItem "androidx.compose.foundation.layout  
FriendRequest "androidx.compose.foundation.layout  FriendRequestItem "androidx.compose.foundation.layout  Group "androidx.compose.foundation.layout  	GroupItem "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  SettingItem "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  System "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  User "androidx.compose.foundation.layout  VisualTransformation "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  await "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  com "androidx.compose.foundation.layout  contains "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  ifEmpty "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  	onFailure "androidx.compose.foundation.layout  	onSuccess "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  plus "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  runFirebaseTests "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  sumOf "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Error -androidx.compose.foundation.layout.AuthResult  Success -androidx.compose.foundation.layout.AuthResult  	Alignment +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Person +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  BottomNavButton .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  ClipData .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Context .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  Email .androidx.compose.foundation.layout.ColumnScope  	ExitToApp .androidx.compose.foundation.layout.ColumnScope  FirebaseAuth .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
FriendItem .androidx.compose.foundation.layout.ColumnScope  FriendRequestItem .androidx.compose.foundation.layout.ColumnScope  	GroupItem .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Lock .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Search .androidx.compose.foundation.layout.ColumnScope  SettingItem .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  VisualTransformation .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  android .androidx.compose.foundation.layout.ColumnScope  await .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  com .androidx.compose.foundation.layout.ColumnScope  contains .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  ifEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  	onFailure .androidx.compose.foundation.layout.ColumnScope  	onSuccess .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  plus .androidx.compose.foundation.layout.ColumnScope  runFirebaseTests .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  sumOf .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  BottomNavButton +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  ClipData +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Context +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  	ExitToApp +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Person +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  align +androidx.compose.foundation.layout.RowScope  
cardColors +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  ifEmpty +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  sumOf +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  vector ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  ImageVector Fandroidx.compose.foundation.layout.androidx.compose.ui.graphics.vector  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  
FriendItem .androidx.compose.foundation.lazy.LazyItemScope  FriendRequestItem .androidx.compose.foundation.lazy.LazyItemScope  	GroupItem .androidx.compose.foundation.lazy.LazyItemScope  launch .androidx.compose.foundation.lazy.LazyItemScope  
FriendItem .androidx.compose.foundation.lazy.LazyListScope  FriendRequestItem .androidx.compose.foundation.lazy.LazyListScope  	GroupItem .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  launch .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Email ,androidx.compose.material.icons.Icons.Filled  	ExitToApp ,androidx.compose.material.icons.Icons.Filled  Lock ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  	ExitToApp &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  SettingItem &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  compose /androidx.compose.material.icons.filled.androidx  ui 7androidx.compose.material.icons.filled.androidx.compose  graphics :androidx.compose.material.icons.filled.androidx.compose.ui  vector Candroidx.compose.material.icons.filled.androidx.compose.ui.graphics  ImageVector Jandroidx.compose.material.icons.filled.androidx.compose.ui.graphics.vector  Activity androidx.compose.material3  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  	ArrowBack androidx.compose.material3  
AuthResult androidx.compose.material3  BottomNavButton androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ClipData androidx.compose.material3  ClipboardManager androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Context androidx.compose.material3  Edit androidx.compose.material3  	Exception androidx.compose.material3  	ExitToApp androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FirebaseAuth androidx.compose.material3  FirebaseDatabase androidx.compose.material3  
FontWeight androidx.compose.material3  
FriendItem androidx.compose.material3  
FriendRequest androidx.compose.material3  FriendRequestItem androidx.compose.material3  Group androidx.compose.material3  	GroupItem androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Int androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  
LazyColumn androidx.compose.material3  List androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  Person androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  SettingItem androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  System androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  Toast androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  User androidx.compose.material3  VisualTransformation androidx.compose.material3  align androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  await androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  clip androidx.compose.material3  collectAsState androidx.compose.material3  com androidx.compose.material3  contains androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  forEach androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  heightIn androidx.compose.material3  ifEmpty androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  	onFailure androidx.compose.material3  	onSuccess androidx.compose.material3  outlinedButtonColors androidx.compose.material3  padding androidx.compose.material3  plus androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  runFirebaseTests androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  sumOf androidx.compose.material3  trim androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  Error %androidx.compose.material3.AuthResult  Success %androidx.compose.material3.AuthResult  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onError &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSecondaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  secondaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  vector 7androidx.compose.material3.androidx.compose.ui.graphics  ImageVector >androidx.compose.material3.androidx.compose.ui.graphics.vector  Activity androidx.compose.runtime  ActivityResultContracts androidx.compose.runtime  AddFriendScreen androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  	ArrowBack androidx.compose.runtime  
AuthResult androidx.compose.runtime  BottomNavButton androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  ClipData androidx.compose.runtime  ClipboardManager androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Context androidx.compose.runtime  Edit androidx.compose.runtime  	Exception androidx.compose.runtime  	ExitToApp androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FirebaseAuth androidx.compose.runtime  FirebaseDatabase androidx.compose.runtime  FirebaseTestScreen androidx.compose.runtime  
FontWeight androidx.compose.runtime  
FriendItem androidx.compose.runtime  
FriendRequest androidx.compose.runtime  FriendRequestItem androidx.compose.runtime  
FriendsScreen androidx.compose.runtime  Group androidx.compose.runtime  	GroupItem androidx.compose.runtime  
HomeScreen androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Int androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  
LazyColumn androidx.compose.runtime  List androidx.compose.runtime  LiveStreamScreen androidx.compose.runtime  LoginScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  NavHostController androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  Person androidx.compose.runtime  
ProfileScreen androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RegisterScreen androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Routes androidx.compose.runtime  Row androidx.compose.runtime  SettingItem androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  Toast androidx.compose.runtime  Unit androidx.compose.runtime  User androidx.compose.runtime  VisualTransformation androidx.compose.runtime  align androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  await androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  clip androidx.compose.runtime  collectAsState androidx.compose.runtime  com androidx.compose.runtime  contains androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  heightIn androidx.compose.runtime  ifEmpty androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  	onFailure androidx.compose.runtime  	onSuccess androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  padding androidx.compose.runtime  plus androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  runFirebaseTests androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  sumOf androidx.compose.runtime  trim androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  Error #androidx.compose.runtime.AuthResult  Success #androidx.compose.runtime.AuthResult  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  vector 5androidx.compose.runtime.androidx.compose.ui.graphics  ImageVector <androidx.compose.runtime.androidx.compose.ui.graphics.vector  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  heightIn &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Light (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Light 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  	Companion 3androidx.compose.ui.text.input.VisualTransformation  None 3androidx.compose.ui.text.input.VisualTransformation  None =androidx.compose.ui.text.input.VisualTransformation.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  
AnimeTheme #androidx.core.app.ComponentActivity  
AppNavigation #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  FirebaseAnalytics #androidx.core.app.ComponentActivity  FirebaseHelper #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  logEvent #androidx.core.app.ComponentActivity  mapOf #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  testFirebaseConnection #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  AddFriendScreen #androidx.navigation.NavGraphBuilder  FirebaseTestScreen #androidx.navigation.NavGraphBuilder  
FriendsScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  LiveStreamScreen #androidx.navigation.NavGraphBuilder  LoginScreen #androidx.navigation.NavGraphBuilder  
ProfileScreen #androidx.navigation.NavGraphBuilder  RegisterScreen #androidx.navigation.NavGraphBuilder  Routes #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  Routes %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  GoogleSignIn &com.google.android.gms.auth.api.signin  GoogleSignInAccount &com.google.android.gms.auth.api.signin  GoogleSignInClient &com.google.android.gms.auth.api.signin  GoogleSignInOptions &com.google.android.gms.auth.api.signin  	getClient 3com.google.android.gms.auth.api.signin.GoogleSignIn  getLastSignedInAccount 3com.google.android.gms.auth.api.signin.GoogleSignIn  getSignedInAccountFromIntent 3com.google.android.gms.auth.api.signin.GoogleSignIn  displayName :com.google.android.gms.auth.api.signin.GoogleSignInAccount  email :com.google.android.gms.auth.api.signin.GoogleSignInAccount  idToken :com.google.android.gms.auth.api.signin.GoogleSignInAccount  signInIntent 9com.google.android.gms.auth.api.signin.GoogleSignInClient  signOut 9com.google.android.gms.auth.api.signin.GoogleSignInClient  Builder :com.google.android.gms.auth.api.signin.GoogleSignInOptions  DEFAULT_SIGN_IN :com.google.android.gms.auth.api.signin.GoogleSignInOptions  build Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestEmail Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestIdToken Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  ApiException !com.google.android.gms.common.api  message .com.google.android.gms.common.api.ApiException  
statusCode .com.google.android.gms.common.api.ApiException  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  await !com.google.android.gms.tasks.Task  	getResult !com.google.android.gms.tasks.Task  FirebaseApp com.google.firebase  
initializeApp com.google.firebase.FirebaseApp  FirebaseAnalytics com.google.firebase.analytics  getInstance /com.google.firebase.analytics.FirebaseAnalytics  logEvent /com.google.firebase.analytics.FirebaseAnalytics  AuthCredential com.google.firebase.auth  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  GoogleAuthProvider com.google.firebase.auth  user #com.google.firebase.auth.AuthResult  createUserWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  currentUser %com.google.firebase.auth.FirebaseAuth  getInstance %com.google.firebase.auth.FirebaseAuth  signInWithCredential %com.google.firebase.auth.FirebaseAuth  signInWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  signOut %com.google.firebase.auth.FirebaseAuth  displayName %com.google.firebase.auth.FirebaseUser  email %com.google.firebase.auth.FirebaseUser  isEmailVerified %com.google.firebase.auth.FirebaseUser  let %com.google.firebase.auth.FirebaseUser  photoUrl %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  
getCredential +com.google.firebase.auth.GoogleAuthProvider  isEmailVerified !com.google.firebase.auth.UserInfo  Boolean com.google.firebase.database  DataSnapshot com.google.firebase.database  
DatabaseError com.google.firebase.database  DatabaseReference com.google.firebase.database  	Exception com.google.firebase.database  FRIENDSHIPS_PATH com.google.firebase.database  FRIEND_REQUESTS_PATH com.google.firebase.database  FirebaseAuth com.google.firebase.database  FirebaseDatabase com.google.firebase.database  Flow com.google.firebase.database  
FriendRequest com.google.firebase.database  FriendRequestStatus com.google.firebase.database  
Friendship com.google.firebase.database  List com.google.firebase.database  Log com.google.firebase.database  Query com.google.firebase.database  Result com.google.firebase.database  String com.google.firebase.database  System com.google.firebase.database  TAG com.google.firebase.database  
USERS_PATH com.google.firebase.database  Unit com.google.firebase.database  User com.google.firebase.database  ValueEventListener com.google.firebase.database  auth com.google.firebase.database  await com.google.firebase.database  callbackFlow com.google.firebase.database  close com.google.firebase.database  database com.google.firebase.database  	emptyList com.google.firebase.database  failure com.google.firebase.database  getFriendsDetails com.google.firebase.database  java com.google.firebase.database  
mutableListOf com.google.firebase.database  substringBefore com.google.firebase.database  success com.google.firebase.database  trySend com.google.firebase.database  children )com.google.firebase.database.DataSnapshot  exists )com.google.firebase.database.DataSnapshot  getValue )com.google.firebase.database.DataSnapshot  ref )com.google.firebase.database.DataSnapshot  value )com.google.firebase.database.DataSnapshot  message *com.google.firebase.database.DatabaseError  addValueEventListener .com.google.firebase.database.DatabaseReference  child .com.google.firebase.database.DatabaseReference  get .com.google.firebase.database.DatabaseReference  key .com.google.firebase.database.DatabaseReference  limitToFirst .com.google.firebase.database.DatabaseReference  orderByChild .com.google.firebase.database.DatabaseReference  push .com.google.firebase.database.DatabaseReference  removeEventListener .com.google.firebase.database.DatabaseReference  removeValue .com.google.firebase.database.DatabaseReference  setValue .com.google.firebase.database.DatabaseReference  getInstance -com.google.firebase.database.FirebaseDatabase  getReference -com.google.firebase.database.FirebaseDatabase  setPersistenceEnabled -com.google.firebase.database.FirebaseDatabase  addValueEventListener "com.google.firebase.database.Query  equalTo "com.google.firebase.database.Query  get "com.google.firebase.database.Query  limitToFirst "com.google.firebase.database.Query  orderByChild "com.google.firebase.database.Query  removeEventListener "com.google.firebase.database.Query  FirebaseStorage com.google.firebase.storage  StorageReference com.google.firebase.storage  getInstance +com.google.firebase.storage.FirebaseStorage  getReference +com.google.firebase.storage.FirebaseStorage  path ,com.google.firebase.storage.StorageReference  
AnimeTheme com.toika.netwok  Any com.toika.netwok  
AppNavigation com.toika.netwok  Application com.toika.netwok  Boolean com.toika.netwok  Bundle com.toika.netwok  ComponentActivity com.toika.netwok  Double com.toika.netwok  	Exception com.toika.netwok  FirebaseAnalytics com.toika.netwok  FirebaseApp com.toika.netwok  FirebaseAuth com.toika.netwok  FirebaseDatabase com.toika.netwok  FirebaseHelper com.toika.netwok  FirebaseStorage com.toika.netwok  Int com.toika.netwok  Log com.toika.netwok  Long com.toika.netwok  MainActivity com.toika.netwok  Map com.toika.netwok  
MaterialTheme com.toika.netwok  Modifier com.toika.netwok  
MyApplication com.toika.netwok  String com.toika.netwok  Surface com.toika.netwok  System com.toika.netwok  Unit com.toika.netwok  android com.toika.netwok  
component1 com.toika.netwok  
component2 com.toika.netwok  fillMaxSize com.toika.netwok  forEach com.toika.netwok  logEvent com.toika.netwok  mapOf com.toika.netwok  testFirebaseConnection com.toika.netwok  to com.toika.netwok  FirebaseAuth com.toika.netwok.FirebaseHelper  FirebaseDatabase com.toika.netwok.FirebaseHelper  FirebaseStorage com.toika.netwok.FirebaseHelper  Log com.toika.netwok.FirebaseHelper  System com.toika.netwok.FirebaseHelper  TAG com.toika.netwok.FirebaseHelper  android com.toika.netwok.FirebaseHelper  
component1 com.toika.netwok.FirebaseHelper  
component2 com.toika.netwok.FirebaseHelper  getCurrentUser com.toika.netwok.FirebaseHelper  getStorageReference com.toika.netwok.FirebaseHelper  logEvent com.toika.netwok.FirebaseHelper  mapOf com.toika.netwok.FirebaseHelper  testFirebaseConnection com.toika.netwok.FirebaseHelper  to com.toika.netwok.FirebaseHelper  writeToDatabase com.toika.netwok.FirebaseHelper  
AnimeTheme com.toika.netwok.MainActivity  
AppNavigation com.toika.netwok.MainActivity  FirebaseAnalytics com.toika.netwok.MainActivity  FirebaseHelper com.toika.netwok.MainActivity  
MaterialTheme com.toika.netwok.MainActivity  Modifier com.toika.netwok.MainActivity  Surface com.toika.netwok.MainActivity  enableEdgeToEdge com.toika.netwok.MainActivity  fillMaxSize com.toika.netwok.MainActivity  logEvent com.toika.netwok.MainActivity  mapOf com.toika.netwok.MainActivity  
setContent com.toika.netwok.MainActivity  testFirebaseConnection com.toika.netwok.MainActivity  to com.toika.netwok.MainActivity  FirebaseAnalytics com.toika.netwok.MyApplication  FirebaseApp com.toika.netwok.MyApplication  FirebaseAuth com.toika.netwok.MyApplication  FirebaseDatabase com.toika.netwok.MyApplication  FirebaseStorage com.toika.netwok.MyApplication  ApiException com.toika.netwok.auth  
AuthResult com.toika.netwok.auth  AuthenticationManager com.toika.netwok.auth  Boolean com.toika.netwok.auth  Context com.toika.netwok.auth  	Exception com.toika.netwok.auth  FirebaseAuth com.toika.netwok.auth  FirebaseDatabase com.toika.netwok.auth  FirebaseUser com.toika.netwok.auth  GoogleAuthProvider com.toika.netwok.auth  GoogleSignIn com.toika.netwok.auth  GoogleSignInAccount com.toika.netwok.auth  GoogleSignInClient com.toika.netwok.auth  GoogleSignInOptions com.toika.netwok.auth  Intent com.toika.netwok.auth  Log com.toika.netwok.auth  String com.toika.netwok.auth  System com.toika.netwok.auth  TAG com.toika.netwok.auth  User com.toika.netwok.auth  UserInfo com.toika.netwok.auth  await com.toika.netwok.auth  java com.toika.netwok.auth  let com.toika.netwok.auth  substringBefore com.toika.netwok.auth  take com.toika.netwok.auth  
AuthResult  com.toika.netwok.auth.AuthResult  Error  com.toika.netwok.auth.AuthResult  FirebaseUser  com.toika.netwok.auth.AuthResult  String  com.toika.netwok.auth.AuthResult  Success  com.toika.netwok.auth.AuthResult  message &com.toika.netwok.auth.AuthResult.Error  ApiException +com.toika.netwok.auth.AuthenticationManager  
AuthResult +com.toika.netwok.auth.AuthenticationManager  Boolean +com.toika.netwok.auth.AuthenticationManager  Context +com.toika.netwok.auth.AuthenticationManager  	Exception +com.toika.netwok.auth.AuthenticationManager  FirebaseAuth +com.toika.netwok.auth.AuthenticationManager  FirebaseDatabase +com.toika.netwok.auth.AuthenticationManager  FirebaseUser +com.toika.netwok.auth.AuthenticationManager  GoogleAuthProvider +com.toika.netwok.auth.AuthenticationManager  GoogleSignIn +com.toika.netwok.auth.AuthenticationManager  GoogleSignInAccount +com.toika.netwok.auth.AuthenticationManager  GoogleSignInClient +com.toika.netwok.auth.AuthenticationManager  GoogleSignInOptions +com.toika.netwok.auth.AuthenticationManager  Intent +com.toika.netwok.auth.AuthenticationManager  Log +com.toika.netwok.auth.AuthenticationManager  String +com.toika.netwok.auth.AuthenticationManager  System +com.toika.netwok.auth.AuthenticationManager  TAG +com.toika.netwok.auth.AuthenticationManager  User +com.toika.netwok.auth.AuthenticationManager  UserInfo +com.toika.netwok.auth.AuthenticationManager  auth +com.toika.netwok.auth.AuthenticationManager  await +com.toika.netwok.auth.AuthenticationManager  context +com.toika.netwok.auth.AuthenticationManager  createUserWithEmailAndPassword +com.toika.netwok.auth.AuthenticationManager  database +com.toika.netwok.auth.AuthenticationManager  firebaseAuthWithGoogle +com.toika.netwok.auth.AuthenticationManager  getCurrentUser +com.toika.netwok.auth.AuthenticationManager  getGoogleSignInIntent +com.toika.netwok.auth.AuthenticationManager  getUserInfo +com.toika.netwok.auth.AuthenticationManager  googleSignInClient +com.toika.netwok.auth.AuthenticationManager  handleGoogleSignInResult +com.toika.netwok.auth.AuthenticationManager  java +com.toika.netwok.auth.AuthenticationManager  let +com.toika.netwok.auth.AuthenticationManager  saveUserToDatabase +com.toika.netwok.auth.AuthenticationManager  signInWithEmailAndPassword +com.toika.netwok.auth.AuthenticationManager  signOut +com.toika.netwok.auth.AuthenticationManager  substringBefore +com.toika.netwok.auth.AuthenticationManager  take +com.toika.netwok.auth.AuthenticationManager  ApiException 5com.toika.netwok.auth.AuthenticationManager.Companion  
AuthResult 5com.toika.netwok.auth.AuthenticationManager.Companion  FirebaseAuth 5com.toika.netwok.auth.AuthenticationManager.Companion  FirebaseDatabase 5com.toika.netwok.auth.AuthenticationManager.Companion  GoogleAuthProvider 5com.toika.netwok.auth.AuthenticationManager.Companion  GoogleSignIn 5com.toika.netwok.auth.AuthenticationManager.Companion  GoogleSignInOptions 5com.toika.netwok.auth.AuthenticationManager.Companion  Log 5com.toika.netwok.auth.AuthenticationManager.Companion  System 5com.toika.netwok.auth.AuthenticationManager.Companion  TAG 5com.toika.netwok.auth.AuthenticationManager.Companion  User 5com.toika.netwok.auth.AuthenticationManager.Companion  UserInfo 5com.toika.netwok.auth.AuthenticationManager.Companion  await 5com.toika.netwok.auth.AuthenticationManager.Companion  java 5com.toika.netwok.auth.AuthenticationManager.Companion  let 5com.toika.netwok.auth.AuthenticationManager.Companion  substringBefore 5com.toika.netwok.auth.AuthenticationManager.Companion  take 5com.toika.netwok.auth.AuthenticationManager.Companion  displayName com.toika.netwok.auth.UserInfo  email com.toika.netwok.auth.UserInfo  isEmailVerified com.toika.netwok.auth.UserInfo  source com.toika.netwok.auth.UserInfo  Boolean com.toika.netwok.data  DataSnapshot com.toika.netwok.data  
DatabaseError com.toika.netwok.data  	Exception com.toika.netwok.data  FRIENDSHIPS_PATH com.toika.netwok.data  FRIEND_REQUESTS_PATH com.toika.netwok.data  FirebaseAuth com.toika.netwok.data  FirebaseDatabase com.toika.netwok.data  Flow com.toika.netwok.data  
FriendRequest com.toika.netwok.data  FriendRequestStatus com.toika.netwok.data  FriendsManager com.toika.netwok.data  
Friendship com.toika.netwok.data  List com.toika.netwok.data  Log com.toika.netwok.data  Result com.toika.netwok.data  String com.toika.netwok.data  System com.toika.netwok.data  TAG com.toika.netwok.data  
USERS_PATH com.toika.netwok.data  Unit com.toika.netwok.data  User com.toika.netwok.data  ValueEventListener com.toika.netwok.data  auth com.toika.netwok.data  await com.toika.netwok.data  callbackFlow com.toika.netwok.data  close com.toika.netwok.data  database com.toika.netwok.data  	emptyList com.toika.netwok.data  failure com.toika.netwok.data  getFriendsDetails com.toika.netwok.data  java com.toika.netwok.data  
mutableListOf com.toika.netwok.data  substringBefore com.toika.netwok.data  success com.toika.netwok.data  trySend com.toika.netwok.data  Boolean $com.toika.netwok.data.FriendsManager  DataSnapshot $com.toika.netwok.data.FriendsManager  
DatabaseError $com.toika.netwok.data.FriendsManager  	Exception $com.toika.netwok.data.FriendsManager  FRIENDSHIPS_PATH $com.toika.netwok.data.FriendsManager  FRIEND_REQUESTS_PATH $com.toika.netwok.data.FriendsManager  FirebaseAuth $com.toika.netwok.data.FriendsManager  FirebaseDatabase $com.toika.netwok.data.FriendsManager  Flow $com.toika.netwok.data.FriendsManager  
FriendRequest $com.toika.netwok.data.FriendsManager  FriendRequestStatus $com.toika.netwok.data.FriendsManager  
Friendship $com.toika.netwok.data.FriendsManager  List $com.toika.netwok.data.FriendsManager  Log $com.toika.netwok.data.FriendsManager  Result $com.toika.netwok.data.FriendsManager  String $com.toika.netwok.data.FriendsManager  System $com.toika.netwok.data.FriendsManager  TAG $com.toika.netwok.data.FriendsManager  
USERS_PATH $com.toika.netwok.data.FriendsManager  Unit $com.toika.netwok.data.FriendsManager  User $com.toika.netwok.data.FriendsManager  ValueEventListener $com.toika.netwok.data.FriendsManager  acceptFriendRequest $com.toika.netwok.data.FriendsManager  addFriendDirectly $com.toika.netwok.data.FriendsManager  auth $com.toika.netwok.data.FriendsManager  await $com.toika.netwok.data.FriendsManager  
awaitClose $com.toika.netwok.data.FriendsManager  callbackFlow $com.toika.netwok.data.FriendsManager  checkExistingFriendRequest $com.toika.netwok.data.FriendsManager  checkExistingFriendship $com.toika.netwok.data.FriendsManager  close $com.toika.netwok.data.FriendsManager  database $com.toika.netwok.data.FriendsManager  	emptyList $com.toika.netwok.data.FriendsManager  ensureCurrentUserExists $com.toika.netwok.data.FriendsManager  failure $com.toika.netwok.data.FriendsManager  
getFriends $com.toika.netwok.data.FriendsManager  getFriendsDetails $com.toika.netwok.data.FriendsManager  getIncomingFriendRequests $com.toika.netwok.data.FriendsManager  java $com.toika.netwok.data.FriendsManager  
mutableListOf $com.toika.netwok.data.FriendsManager  rejectFriendRequest $com.toika.netwok.data.FriendsManager  removeFriend $com.toika.netwok.data.FriendsManager  searchUserByUid $com.toika.netwok.data.FriendsManager  substringBefore $com.toika.netwok.data.FriendsManager  success $com.toika.netwok.data.FriendsManager  trySend $com.toika.netwok.data.FriendsManager  	Exception .com.toika.netwok.data.FriendsManager.Companion  FRIENDSHIPS_PATH .com.toika.netwok.data.FriendsManager.Companion  FRIEND_REQUESTS_PATH .com.toika.netwok.data.FriendsManager.Companion  FirebaseAuth .com.toika.netwok.data.FriendsManager.Companion  FirebaseDatabase .com.toika.netwok.data.FriendsManager.Companion  
FriendRequest .com.toika.netwok.data.FriendsManager.Companion  FriendRequestStatus .com.toika.netwok.data.FriendsManager.Companion  
Friendship .com.toika.netwok.data.FriendsManager.Companion  Log .com.toika.netwok.data.FriendsManager.Companion  Result .com.toika.netwok.data.FriendsManager.Companion  System .com.toika.netwok.data.FriendsManager.Companion  TAG .com.toika.netwok.data.FriendsManager.Companion  
USERS_PATH .com.toika.netwok.data.FriendsManager.Companion  Unit .com.toika.netwok.data.FriendsManager.Companion  User .com.toika.netwok.data.FriendsManager.Companion  auth .com.toika.netwok.data.FriendsManager.Companion  await .com.toika.netwok.data.FriendsManager.Companion  
awaitClose .com.toika.netwok.data.FriendsManager.Companion  callbackFlow .com.toika.netwok.data.FriendsManager.Companion  close .com.toika.netwok.data.FriendsManager.Companion  database .com.toika.netwok.data.FriendsManager.Companion  	emptyList .com.toika.netwok.data.FriendsManager.Companion  failure .com.toika.netwok.data.FriendsManager.Companion  getFriendsDetails .com.toika.netwok.data.FriendsManager.Companion  java .com.toika.netwok.data.FriendsManager.Companion  
mutableListOf .com.toika.netwok.data.FriendsManager.Companion  substringBefore .com.toika.netwok.data.FriendsManager.Companion  success .com.toika.netwok.data.FriendsManager.Companion  trySend .com.toika.netwok.data.FriendsManager.Companion  Boolean com.toika.netwok.data.models  DataSnapshot com.toika.netwok.data.models  
DatabaseError com.toika.netwok.data.models  	Exception com.toika.netwok.data.models  FRIENDSHIPS_PATH com.toika.netwok.data.models  FRIEND_REQUESTS_PATH com.toika.netwok.data.models  FirebaseAuth com.toika.netwok.data.models  FirebaseDatabase com.toika.netwok.data.models  Flow com.toika.netwok.data.models  
FriendRequest com.toika.netwok.data.models  FriendRequestStatus com.toika.netwok.data.models  
Friendship com.toika.netwok.data.models  List com.toika.netwok.data.models  Log com.toika.netwok.data.models  Long com.toika.netwok.data.models  Result com.toika.netwok.data.models  String com.toika.netwok.data.models  System com.toika.netwok.data.models  TAG com.toika.netwok.data.models  
USERS_PATH com.toika.netwok.data.models  Unit com.toika.netwok.data.models  User com.toika.netwok.data.models  ValueEventListener com.toika.netwok.data.models  auth com.toika.netwok.data.models  await com.toika.netwok.data.models  callbackFlow com.toika.netwok.data.models  close com.toika.netwok.data.models  database com.toika.netwok.data.models  	emptyList com.toika.netwok.data.models  failure com.toika.netwok.data.models  getFriendsDetails com.toika.netwok.data.models  java com.toika.netwok.data.models  
mutableListOf com.toika.netwok.data.models  substringBefore com.toika.netwok.data.models  success com.toika.netwok.data.models  trySend com.toika.netwok.data.models  FriendRequestStatus *com.toika.netwok.data.models.FriendRequest  
fromUserEmail *com.toika.netwok.data.models.FriendRequest  
fromUserId *com.toika.netwok.data.models.FriendRequest  fromUserName *com.toika.netwok.data.models.FriendRequest  id *com.toika.netwok.data.models.FriendRequest  status *com.toika.netwok.data.models.FriendRequest  toUserId *com.toika.netwok.data.models.FriendRequest  ACCEPTED 0com.toika.netwok.data.models.FriendRequestStatus  PENDING 0com.toika.netwok.data.models.FriendRequestStatus  REJECTED 0com.toika.netwok.data.models.FriendRequestStatus  name 0com.toika.netwok.data.models.FriendRequestStatus  user1Id 'com.toika.netwok.data.models.Friendship  user2Id 'com.toika.netwok.data.models.Friendship  displayName !com.toika.netwok.data.models.User  email !com.toika.netwok.data.models.User  isOnline !com.toika.netwok.data.models.User  let !com.toika.netwok.data.models.User  uid !com.toika.netwok.data.models.User  AddFriendScreen com.toika.netwok.navigation  
AppNavigation com.toika.netwok.navigation  
Composable com.toika.netwok.navigation  FirebaseAuth com.toika.netwok.navigation  FirebaseTestScreen com.toika.netwok.navigation  
FriendsScreen com.toika.netwok.navigation  
HomeScreen com.toika.netwok.navigation  LiveStreamScreen com.toika.netwok.navigation  LoginScreen com.toika.netwok.navigation  NavHostController com.toika.netwok.navigation  
ProfileScreen com.toika.netwok.navigation  RegisterScreen com.toika.netwok.navigation  Routes com.toika.netwok.navigation  
ADD_FRIEND "com.toika.netwok.navigation.Routes  
FIREBASE_TEST "com.toika.netwok.navigation.Routes  FRIENDS "com.toika.netwok.navigation.Routes  HOME "com.toika.netwok.navigation.Routes  LIVE_STREAM "com.toika.netwok.navigation.Routes  LOGIN "com.toika.netwok.navigation.Routes  PROFILE "com.toika.netwok.navigation.Routes  REGISTER "com.toika.netwok.navigation.Routes  Activity com.toika.netwok.ui.screens  ActivityResultContracts com.toika.netwok.ui.screens  AddFriendScreen com.toika.netwok.ui.screens  AlertDialog com.toika.netwok.ui.screens  	Alignment com.toika.netwok.ui.screens  Arrangement com.toika.netwok.ui.screens  	ArrowBack com.toika.netwok.ui.screens  
AuthResult com.toika.netwok.ui.screens  BottomNavButton com.toika.netwok.ui.screens  Box com.toika.netwok.ui.screens  Button com.toika.netwok.ui.screens  ButtonDefaults com.toika.netwok.ui.screens  Card com.toika.netwok.ui.screens  CardDefaults com.toika.netwok.ui.screens  CircleShape com.toika.netwok.ui.screens  CircularProgressIndicator com.toika.netwok.ui.screens  ClipData com.toika.netwok.ui.screens  ClipboardManager com.toika.netwok.ui.screens  Column com.toika.netwok.ui.screens  
Composable com.toika.netwok.ui.screens  Context com.toika.netwok.ui.screens  Edit com.toika.netwok.ui.screens  	Exception com.toika.netwok.ui.screens  	ExitToApp com.toika.netwok.ui.screens  ExperimentalMaterial3Api com.toika.netwok.ui.screens  FirebaseAuth com.toika.netwok.ui.screens  FirebaseDatabase com.toika.netwok.ui.screens  FirebaseTestScreen com.toika.netwok.ui.screens  
FontWeight com.toika.netwok.ui.screens  
FriendItem com.toika.netwok.ui.screens  
FriendRequest com.toika.netwok.ui.screens  FriendRequestItem com.toika.netwok.ui.screens  
FriendsScreen com.toika.netwok.ui.screens  Group com.toika.netwok.ui.screens  	GroupItem com.toika.netwok.ui.screens  GroupsScreen com.toika.netwok.ui.screens  
HomeScreen com.toika.netwok.ui.screens  Icon com.toika.netwok.ui.screens  
IconButton com.toika.netwok.ui.screens  Icons com.toika.netwok.ui.screens  Int com.toika.netwok.ui.screens  KeyboardOptions com.toika.netwok.ui.screens  KeyboardType com.toika.netwok.ui.screens  
LazyColumn com.toika.netwok.ui.screens  List com.toika.netwok.ui.screens  LiveStreamScreen com.toika.netwok.ui.screens  LoginScreen com.toika.netwok.ui.screens  
MaterialTheme com.toika.netwok.ui.screens  Modifier com.toika.netwok.ui.screens  OptIn com.toika.netwok.ui.screens  OutlinedButton com.toika.netwok.ui.screens  OutlinedTextField com.toika.netwok.ui.screens  PasswordVisualTransformation com.toika.netwok.ui.screens  Person com.toika.netwok.ui.screens  
ProfileScreen com.toika.netwok.ui.screens  RegisterScreen com.toika.netwok.ui.screens  RoundedCornerShape com.toika.netwok.ui.screens  Row com.toika.netwok.ui.screens  SettingItem com.toika.netwok.ui.screens  SimpleAddFriendScreen com.toika.netwok.ui.screens  SimpleFriendsScreen com.toika.netwok.ui.screens  Spacer com.toika.netwok.ui.screens  String com.toika.netwok.ui.screens  System com.toika.netwok.ui.screens  Text com.toika.netwok.ui.screens  	TextAlign com.toika.netwok.ui.screens  
TextButton com.toika.netwok.ui.screens  Toast com.toika.netwok.ui.screens  Unit com.toika.netwok.ui.screens  User com.toika.netwok.ui.screens  VisualTransformation com.toika.netwok.ui.screens  align com.toika.netwok.ui.screens  android com.toika.netwok.ui.screens  androidx com.toika.netwok.ui.screens  await com.toika.netwok.ui.screens  
cardColors com.toika.netwok.ui.screens  	clickable com.toika.netwok.ui.screens  clip com.toika.netwok.ui.screens  collectAsState com.toika.netwok.ui.screens  com com.toika.netwok.ui.screens  contains com.toika.netwok.ui.screens  	emptyList com.toika.netwok.ui.screens  fillMaxSize com.toika.netwok.ui.screens  fillMaxWidth com.toika.netwok.ui.screens  forEach com.toika.netwok.ui.screens  getValue com.toika.netwok.ui.screens  height com.toika.netwok.ui.screens  heightIn com.toika.netwok.ui.screens  ifEmpty com.toika.netwok.ui.screens  
isNotBlank com.toika.netwok.ui.screens  
isNotEmpty com.toika.netwok.ui.screens  launch com.toika.netwok.ui.screens  let com.toika.netwok.ui.screens  listOf com.toika.netwok.ui.screens  mutableStateOf com.toika.netwok.ui.screens  	onFailure com.toika.netwok.ui.screens  	onSuccess com.toika.netwok.ui.screens  outlinedButtonColors com.toika.netwok.ui.screens  padding com.toika.netwok.ui.screens  plus com.toika.netwok.ui.screens  provideDelegate com.toika.netwok.ui.screens  remember com.toika.netwok.ui.screens  rememberCoroutineScope com.toika.netwok.ui.screens  runFirebaseTests com.toika.netwok.ui.screens  setValue com.toika.netwok.ui.screens  size com.toika.netwok.ui.screens  spacedBy com.toika.netwok.ui.screens  sumOf com.toika.netwok.ui.screens  trim com.toika.netwok.ui.screens  weight com.toika.netwok.ui.screens  width com.toika.netwok.ui.screens  Error &com.toika.netwok.ui.screens.AuthResult  Success &com.toika.netwok.ui.screens.AuthResult  description !com.toika.netwok.ui.screens.Group  memberCount !com.toika.netwok.ui.screens.Group  name !com.toika.netwok.ui.screens.Group  unreadMessages !com.toika.netwok.ui.screens.Group  compose $com.toika.netwok.ui.screens.androidx  ui ,com.toika.netwok.ui.screens.androidx.compose  graphics /com.toika.netwok.ui.screens.androidx.compose.ui  vector 8com.toika.netwok.ui.screens.androidx.compose.ui.graphics  ImageVector ?com.toika.netwok.ui.screens.androidx.compose.ui.graphics.vector  
AnimeTheme com.toika.netwok.ui.theme  Boolean com.toika.netwok.ui.theme  Build com.toika.netwok.ui.theme  
Composable com.toika.netwok.ui.theme  DarkColorScheme com.toika.netwok.ui.theme  
FontFamily com.toika.netwok.ui.theme  
FontWeight com.toika.netwok.ui.theme  LightColorScheme com.toika.netwok.ui.theme  Pink40 com.toika.netwok.ui.theme  Pink80 com.toika.netwok.ui.theme  Purple40 com.toika.netwok.ui.theme  Purple80 com.toika.netwok.ui.theme  PurpleGrey40 com.toika.netwok.ui.theme  PurpleGrey80 com.toika.netwok.ui.theme  
Typography com.toika.netwok.ui.theme  Unit com.toika.netwok.ui.theme  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  matches java.util.regex.Matcher  matcher java.util.regex.Pattern  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  CharSequence kotlin  DoubleArray kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  IntArray kotlin  	LongArray kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  
ShortArray kotlin  	Throwable kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  let kotlin  	onFailure kotlin  	onSuccess kotlin  plus kotlin  to kotlin  not kotlin.Boolean  sp 
kotlin.Double  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  inc 
kotlin.Int  toString 
kotlin.Int  minus kotlin.Long  	Companion 
kotlin.Result  failure 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  contains 
kotlin.String  ifEmpty 
kotlin.String  
isNotBlank 
kotlin.String  length 
kotlin.String  let 
kotlin.String  plus 
kotlin.String  substringBefore 
kotlin.String  take 
kotlin.String  to 
kotlin.String  trim 
kotlin.String  message kotlin.Throwable  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  Set kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  contains kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  ifEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  plus kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  take kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  plus kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  Entry kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  iterator "kotlin.collections.MutableIterable  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  java 
kotlin.jvm  contains 
kotlin.ranges  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  contains kotlin.sequences  forEach kotlin.sequences  ifEmpty kotlin.sequences  plus kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  contains kotlin.text  forEach kotlin.text  ifEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  plus kotlin.text  substringBefore kotlin.text  sumOf kotlin.text  take kotlin.text  trim kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  await !kotlinx.coroutines.CoroutineScope  com !kotlinx.coroutines.CoroutineScope  contains !kotlinx.coroutines.CoroutineScope  	emptyList !kotlinx.coroutines.CoroutineScope  launch !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  plus !kotlinx.coroutines.CoroutineScope  runFirebaseTests !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  
ChannelResult kotlinx.coroutines.channels  
ProducerScope kotlinx.coroutines.channels  
awaitClose kotlinx.coroutines.channels  FRIENDSHIPS_PATH )kotlinx.coroutines.channels.ProducerScope  FRIEND_REQUESTS_PATH )kotlinx.coroutines.channels.ProducerScope  
FriendRequest )kotlinx.coroutines.channels.ProducerScope  FriendRequestStatus )kotlinx.coroutines.channels.ProducerScope  
Friendship )kotlinx.coroutines.channels.ProducerScope  Log )kotlinx.coroutines.channels.ProducerScope  TAG )kotlinx.coroutines.channels.ProducerScope  auth )kotlinx.coroutines.channels.ProducerScope  
awaitClose )kotlinx.coroutines.channels.ProducerScope  close )kotlinx.coroutines.channels.ProducerScope  database )kotlinx.coroutines.channels.ProducerScope  	emptyList )kotlinx.coroutines.channels.ProducerScope  getFriendsDetails )kotlinx.coroutines.channels.ProducerScope  java )kotlinx.coroutines.channels.ProducerScope  
mutableListOf )kotlinx.coroutines.channels.ProducerScope  trySend )kotlinx.coroutines.channels.ProducerScope  Flow kotlinx.coroutines.flow  callbackFlow kotlinx.coroutines.flow  collectAsState kotlinx.coroutines.flow.Flow  await kotlinx.coroutines.tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  