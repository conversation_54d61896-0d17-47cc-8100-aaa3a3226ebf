  Activity android.app  Application android.app  
AnimeTheme android.app.Activity  
AppNavigation android.app.Activity  FirebaseAnalytics android.app.Activity  FirebaseHelper android.app.Activity  
MaterialTheme android.app.Activity  Modifier android.app.Activity  	RESULT_OK android.app.Activity  Surface android.app.Activity  enableEdgeToEdge android.app.Activity  fillMaxSize android.app.Activity  logEvent android.app.Activity  mapOf android.app.Activity  onCreate android.app.Activity  
setContent android.app.Activity  testFirebaseConnection android.app.Activity  to android.app.Activity  FirebaseAnalytics android.app.Application  FirebaseApp android.app.Application  FirebaseAuth android.app.Application  FirebaseDatabase android.app.Application  FirebaseStorage android.app.Application  onCreate android.app.Application  ClipData android.content  ClipboardManager android.content  Context android.content  Intent android.content  newPlainText android.content.ClipData  setPrimaryClip  android.content.ClipboardManager  
AnimeTheme android.content.Context  
AppNavigation android.content.Context  CLIPBOARD_SERVICE android.content.Context  FirebaseAnalytics android.content.Context  FirebaseApp android.content.Context  FirebaseAuth android.content.Context  FirebaseDatabase android.content.Context  FirebaseHelper android.content.Context  FirebaseStorage android.content.Context  
MaterialTheme android.content.Context  Modifier android.content.Context  Surface android.content.Context  enableEdgeToEdge android.content.Context  fillMaxSize android.content.Context  	getString android.content.Context  getSystemService android.content.Context  logEvent android.content.Context  mapOf android.content.Context  packageName android.content.Context  	resources android.content.Context  
setContent android.content.Context  testFirebaseConnection android.content.Context  to android.content.Context  
AnimeTheme android.content.ContextWrapper  
AppNavigation android.content.ContextWrapper  FirebaseAnalytics android.content.ContextWrapper  FirebaseApp android.content.ContextWrapper  FirebaseAuth android.content.ContextWrapper  FirebaseDatabase android.content.ContextWrapper  FirebaseHelper android.content.ContextWrapper  FirebaseStorage android.content.ContextWrapper  
MaterialTheme android.content.ContextWrapper  Modifier android.content.ContextWrapper  Surface android.content.ContextWrapper  enableEdgeToEdge android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  logEvent android.content.ContextWrapper  mapOf android.content.ContextWrapper  
setContent android.content.ContextWrapper  testFirebaseConnection android.content.ContextWrapper  to android.content.ContextWrapper  
getIdentifier android.content.res.Resources  Uri android.net  toString android.net.Uri  Build 
android.os  Bundle 
android.os  
putBoolean android.os.BaseBundle  	putDouble android.os.BaseBundle  putInt android.os.BaseBundle  putLong android.os.BaseBundle  	putString android.os.BaseBundle  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  
putBoolean android.os.Bundle  	putDouble android.os.Bundle  putInt android.os.Bundle  putLong android.os.Bundle  	putString android.os.Bundle  Log android.util  d android.util.Log  e android.util.Log  w android.util.Log  
EMAIL_ADDRESS android.util.Patterns  
AnimeTheme  android.view.ContextThemeWrapper  
AppNavigation  android.view.ContextThemeWrapper  FirebaseAnalytics  android.view.ContextThemeWrapper  FirebaseHelper  android.view.ContextThemeWrapper  
MaterialTheme  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  Surface  android.view.ContextThemeWrapper  enableEdgeToEdge  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  logEvent  android.view.ContextThemeWrapper  mapOf  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  testFirebaseConnection  android.view.ContextThemeWrapper  to  android.view.ContextThemeWrapper  Toast android.widget  LENGTH_SHORT android.widget.Toast  makeText android.widget.Toast  show android.widget.Toast  ComponentActivity androidx.activity  enableEdgeToEdge androidx.activity  
AnimeTheme #androidx.activity.ComponentActivity  
AppNavigation #androidx.activity.ComponentActivity  FirebaseAnalytics #androidx.activity.ComponentActivity  FirebaseHelper #androidx.activity.ComponentActivity  
MaterialTheme #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  Surface #androidx.activity.ComponentActivity  enableEdgeToEdge #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  logEvent #androidx.activity.ComponentActivity  mapOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  testFirebaseConnection #androidx.activity.ComponentActivity  to #androidx.activity.ComponentActivity  ManagedActivityResultLauncher androidx.activity.compose  !rememberLauncherForActivityResult androidx.activity.compose  
setContent androidx.activity.compose  launch 7androidx.activity.compose.ManagedActivityResultLauncher  ActivityResult androidx.activity.result  ActivityResultLauncher androidx.activity.result  data 'androidx.activity.result.ActivityResult  
resultCode 'androidx.activity.result.ActivityResult  ActivityResultContracts !androidx.activity.result.contract  StartActivityForResult 9androidx.activity.result.contract.ActivityResultContracts  AnimatedContentScope androidx.compose.animation  AddFriendScreen /androidx.compose.animation.AnimatedContentScope  
FriendsScreen /androidx.compose.animation.AnimatedContentScope  
HomeScreen /androidx.compose.animation.AnimatedContentScope  LiveStreamScreen /androidx.compose.animation.AnimatedContentScope  LoginScreen /androidx.compose.animation.AnimatedContentScope  
ProfileScreen /androidx.compose.animation.AnimatedContentScope  RegisterScreen /androidx.compose.animation.AnimatedContentScope  Routes /androidx.compose.animation.AnimatedContentScope  ScrollState androidx.compose.foundation  	clickable androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  rememberScrollState androidx.compose.foundation  verticalScroll androidx.compose.foundation  Activity "androidx.compose.foundation.layout  ActivityResultContracts "androidx.compose.foundation.layout  AlertDialog "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Arrangement "androidx.compose.foundation.layout  	ArrowBack "androidx.compose.foundation.layout  
AuthResult "androidx.compose.foundation.layout  BottomNavButton "androidx.compose.foundation.layout  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  ButtonDefaults "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  CircularProgressIndicator "androidx.compose.foundation.layout  ClipData "androidx.compose.foundation.layout  ClipboardManager "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  ColumnScope "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  Context "androidx.compose.foundation.layout  Edit "androidx.compose.foundation.layout  	ExitToApp "androidx.compose.foundation.layout  ExperimentalMaterial3Api "androidx.compose.foundation.layout  FirebaseAuth "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  
FriendItem "androidx.compose.foundation.layout  
FriendRequest "androidx.compose.foundation.layout  FriendRequestItem "androidx.compose.foundation.layout  Group "androidx.compose.foundation.layout  	GroupItem "androidx.compose.foundation.layout  Icon "androidx.compose.foundation.layout  
IconButton "androidx.compose.foundation.layout  Icons "androidx.compose.foundation.layout  Int "androidx.compose.foundation.layout  KeyboardOptions "androidx.compose.foundation.layout  KeyboardType "androidx.compose.foundation.layout  
LazyColumn "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  OptIn "androidx.compose.foundation.layout  OutlinedButton "androidx.compose.foundation.layout  OutlinedTextField "androidx.compose.foundation.layout  PasswordVisualTransformation "androidx.compose.foundation.layout  Person "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  SettingItem "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  String "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  
TextButton "androidx.compose.foundation.layout  Toast "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  User "androidx.compose.foundation.layout  VisualTransformation "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  android "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  	clickable "androidx.compose.foundation.layout  clip "androidx.compose.foundation.layout  collectAsState "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  heightIn "androidx.compose.foundation.layout  ifEmpty "androidx.compose.foundation.layout  
isNotBlank "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  launch "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  	onFailure "androidx.compose.foundation.layout  	onSuccess "androidx.compose.foundation.layout  outlinedButtonColors "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  rememberCoroutineScope "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  sumOf "androidx.compose.foundation.layout  trim "androidx.compose.foundation.layout  weight "androidx.compose.foundation.layout  width "androidx.compose.foundation.layout  Center .androidx.compose.foundation.layout.Arrangement  
Horizontal .androidx.compose.foundation.layout.Arrangement  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  Error -androidx.compose.foundation.layout.AuthResult  Success -androidx.compose.foundation.layout.AuthResult  	Alignment +androidx.compose.foundation.layout.BoxScope  Box +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  Icon +androidx.compose.foundation.layout.BoxScope  Icons +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Person +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  clip +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  sp +androidx.compose.foundation.layout.BoxScope  Add .androidx.compose.foundation.layout.ColumnScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  	ArrowBack .androidx.compose.foundation.layout.ColumnScope  BottomNavButton .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Button .androidx.compose.foundation.layout.ColumnScope  ButtonDefaults .androidx.compose.foundation.layout.ColumnScope  Card .androidx.compose.foundation.layout.ColumnScope  CardDefaults .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  CircularProgressIndicator .androidx.compose.foundation.layout.ColumnScope  ClipData .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  Context .androidx.compose.foundation.layout.ColumnScope  Delete .androidx.compose.foundation.layout.ColumnScope  Edit .androidx.compose.foundation.layout.ColumnScope  Email .androidx.compose.foundation.layout.ColumnScope  	ExitToApp .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
FriendItem .androidx.compose.foundation.layout.ColumnScope  FriendRequestItem .androidx.compose.foundation.layout.ColumnScope  	GroupItem .androidx.compose.foundation.layout.ColumnScope  Icon .androidx.compose.foundation.layout.ColumnScope  
IconButton .androidx.compose.foundation.layout.ColumnScope  Icons .androidx.compose.foundation.layout.ColumnScope  KeyboardOptions .androidx.compose.foundation.layout.ColumnScope  KeyboardType .androidx.compose.foundation.layout.ColumnScope  
LazyColumn .androidx.compose.foundation.layout.ColumnScope  Lock .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  OutlinedButton .androidx.compose.foundation.layout.ColumnScope  OutlinedTextField .androidx.compose.foundation.layout.ColumnScope  PasswordVisualTransformation .androidx.compose.foundation.layout.ColumnScope  Person .androidx.compose.foundation.layout.ColumnScope  RoundedCornerShape .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  Search .androidx.compose.foundation.layout.ColumnScope  SettingItem .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  
TextButton .androidx.compose.foundation.layout.ColumnScope  Toast .androidx.compose.foundation.layout.ColumnScope  VisualTransformation .androidx.compose.foundation.layout.ColumnScope  align .androidx.compose.foundation.layout.ColumnScope  android .androidx.compose.foundation.layout.ColumnScope  
cardColors .androidx.compose.foundation.layout.ColumnScope  clip .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  heightIn .androidx.compose.foundation.layout.ColumnScope  ifEmpty .androidx.compose.foundation.layout.ColumnScope  
isNotBlank .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  items .androidx.compose.foundation.layout.ColumnScope  launch .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  	onFailure .androidx.compose.foundation.layout.ColumnScope  	onSuccess .androidx.compose.foundation.layout.ColumnScope  outlinedButtonColors .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  sp .androidx.compose.foundation.layout.ColumnScope  spacedBy .androidx.compose.foundation.layout.ColumnScope  sumOf .androidx.compose.foundation.layout.ColumnScope  trim .androidx.compose.foundation.layout.ColumnScope  weight .androidx.compose.foundation.layout.ColumnScope  width .androidx.compose.foundation.layout.ColumnScope  Add +androidx.compose.foundation.layout.RowScope  	Alignment +androidx.compose.foundation.layout.RowScope  Arrangement +androidx.compose.foundation.layout.RowScope  	ArrowBack +androidx.compose.foundation.layout.RowScope  BottomNavButton +androidx.compose.foundation.layout.RowScope  Box +androidx.compose.foundation.layout.RowScope  Button +androidx.compose.foundation.layout.RowScope  Card +androidx.compose.foundation.layout.RowScope  CardDefaults +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  CircularProgressIndicator +androidx.compose.foundation.layout.RowScope  ClipData +androidx.compose.foundation.layout.RowScope  Column +androidx.compose.foundation.layout.RowScope  Context +androidx.compose.foundation.layout.RowScope  Delete +androidx.compose.foundation.layout.RowScope  Edit +androidx.compose.foundation.layout.RowScope  	ExitToApp +androidx.compose.foundation.layout.RowScope  
FontWeight +androidx.compose.foundation.layout.RowScope  Icon +androidx.compose.foundation.layout.RowScope  
IconButton +androidx.compose.foundation.layout.RowScope  Icons +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  OutlinedButton +androidx.compose.foundation.layout.RowScope  Person +androidx.compose.foundation.layout.RowScope  Row +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  
TextButton +androidx.compose.foundation.layout.RowScope  Toast +androidx.compose.foundation.layout.RowScope  align +androidx.compose.foundation.layout.RowScope  
cardColors +androidx.compose.foundation.layout.RowScope  clip +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  ifEmpty +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  sp +androidx.compose.foundation.layout.RowScope  sumOf +androidx.compose.foundation.layout.RowScope  weight +androidx.compose.foundation.layout.RowScope  width +androidx.compose.foundation.layout.RowScope  compose +androidx.compose.foundation.layout.androidx  ui 3androidx.compose.foundation.layout.androidx.compose  graphics 6androidx.compose.foundation.layout.androidx.compose.ui  vector ?androidx.compose.foundation.layout.androidx.compose.ui.graphics  ImageVector Fandroidx.compose.foundation.layout.androidx.compose.ui.graphics.vector  
LazyColumn  androidx.compose.foundation.lazy  
LazyItemScope  androidx.compose.foundation.lazy  
LazyListScope  androidx.compose.foundation.lazy  items  androidx.compose.foundation.lazy  
FriendItem .androidx.compose.foundation.lazy.LazyItemScope  FriendRequestItem .androidx.compose.foundation.lazy.LazyItemScope  	GroupItem .androidx.compose.foundation.lazy.LazyItemScope  launch .androidx.compose.foundation.lazy.LazyItemScope  
FriendItem .androidx.compose.foundation.lazy.LazyListScope  FriendRequestItem .androidx.compose.foundation.lazy.LazyListScope  	GroupItem .androidx.compose.foundation.lazy.LazyListScope  items .androidx.compose.foundation.lazy.LazyListScope  launch .androidx.compose.foundation.lazy.LazyListScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  KeyboardOptions  androidx.compose.foundation.text  Icons androidx.compose.material.icons  Default %androidx.compose.material.icons.Icons  Filled %androidx.compose.material.icons.Icons  Add ,androidx.compose.material.icons.Icons.Filled  	ArrowBack ,androidx.compose.material.icons.Icons.Filled  Delete ,androidx.compose.material.icons.Icons.Filled  Edit ,androidx.compose.material.icons.Icons.Filled  Email ,androidx.compose.material.icons.Icons.Filled  	ExitToApp ,androidx.compose.material.icons.Icons.Filled  Lock ,androidx.compose.material.icons.Icons.Filled  Person ,androidx.compose.material.icons.Icons.Filled  Search ,androidx.compose.material.icons.Icons.Filled  Add &androidx.compose.material.icons.filled  AlertDialog &androidx.compose.material.icons.filled  	Alignment &androidx.compose.material.icons.filled  	ArrowBack &androidx.compose.material.icons.filled  Box &androidx.compose.material.icons.filled  Card &androidx.compose.material.icons.filled  CardDefaults &androidx.compose.material.icons.filled  CircleShape &androidx.compose.material.icons.filled  Column &androidx.compose.material.icons.filled  
Composable &androidx.compose.material.icons.filled  Delete &androidx.compose.material.icons.filled  Edit &androidx.compose.material.icons.filled  Email &androidx.compose.material.icons.filled  	ExitToApp &androidx.compose.material.icons.filled  ExperimentalMaterial3Api &androidx.compose.material.icons.filled  
FontWeight &androidx.compose.material.icons.filled  Icon &androidx.compose.material.icons.filled  
IconButton &androidx.compose.material.icons.filled  Icons &androidx.compose.material.icons.filled  Lock &androidx.compose.material.icons.filled  
MaterialTheme &androidx.compose.material.icons.filled  Modifier &androidx.compose.material.icons.filled  OptIn &androidx.compose.material.icons.filled  Person &androidx.compose.material.icons.filled  Row &androidx.compose.material.icons.filled  Search &androidx.compose.material.icons.filled  SettingItem &androidx.compose.material.icons.filled  Spacer &androidx.compose.material.icons.filled  String &androidx.compose.material.icons.filled  Text &androidx.compose.material.icons.filled  
TextButton &androidx.compose.material.icons.filled  Unit &androidx.compose.material.icons.filled  androidx &androidx.compose.material.icons.filled  
cardColors &androidx.compose.material.icons.filled  clip &androidx.compose.material.icons.filled  fillMaxSize &androidx.compose.material.icons.filled  fillMaxWidth &androidx.compose.material.icons.filled  getValue &androidx.compose.material.icons.filled  height &androidx.compose.material.icons.filled  mutableStateOf &androidx.compose.material.icons.filled  padding &androidx.compose.material.icons.filled  provideDelegate &androidx.compose.material.icons.filled  remember &androidx.compose.material.icons.filled  setValue &androidx.compose.material.icons.filled  size &androidx.compose.material.icons.filled  weight &androidx.compose.material.icons.filled  width &androidx.compose.material.icons.filled  compose /androidx.compose.material.icons.filled.androidx  ui 7androidx.compose.material.icons.filled.androidx.compose  graphics :androidx.compose.material.icons.filled.androidx.compose.ui  vector Candroidx.compose.material.icons.filled.androidx.compose.ui.graphics  ImageVector Jandroidx.compose.material.icons.filled.androidx.compose.ui.graphics.vector  Activity androidx.compose.material3  ActivityResultContracts androidx.compose.material3  AlertDialog androidx.compose.material3  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  	ArrowBack androidx.compose.material3  
AuthResult androidx.compose.material3  BottomNavButton androidx.compose.material3  Box androidx.compose.material3  Button androidx.compose.material3  ButtonColors androidx.compose.material3  ButtonDefaults androidx.compose.material3  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  CircleShape androidx.compose.material3  CircularProgressIndicator androidx.compose.material3  ClipData androidx.compose.material3  ClipboardManager androidx.compose.material3  ColorScheme androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  Context androidx.compose.material3  Edit androidx.compose.material3  	ExitToApp androidx.compose.material3  ExperimentalMaterial3Api androidx.compose.material3  FirebaseAuth androidx.compose.material3  
FontWeight androidx.compose.material3  
FriendItem androidx.compose.material3  
FriendRequest androidx.compose.material3  FriendRequestItem androidx.compose.material3  Group androidx.compose.material3  	GroupItem androidx.compose.material3  Icon androidx.compose.material3  
IconButton androidx.compose.material3  Icons androidx.compose.material3  Int androidx.compose.material3  KeyboardOptions androidx.compose.material3  KeyboardType androidx.compose.material3  
LazyColumn androidx.compose.material3  
MaterialTheme androidx.compose.material3  Modifier androidx.compose.material3  OptIn androidx.compose.material3  OutlinedButton androidx.compose.material3  OutlinedTextField androidx.compose.material3  PasswordVisualTransformation androidx.compose.material3  Person androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  SettingItem androidx.compose.material3  Spacer androidx.compose.material3  String androidx.compose.material3  Surface androidx.compose.material3  Text androidx.compose.material3  	TextAlign androidx.compose.material3  
TextButton androidx.compose.material3  Toast androidx.compose.material3  
Typography androidx.compose.material3  Unit androidx.compose.material3  User androidx.compose.material3  VisualTransformation androidx.compose.material3  align androidx.compose.material3  android androidx.compose.material3  androidx androidx.compose.material3  
cardColors androidx.compose.material3  	clickable androidx.compose.material3  clip androidx.compose.material3  collectAsState androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  	emptyList androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  getValue androidx.compose.material3  height androidx.compose.material3  heightIn androidx.compose.material3  ifEmpty androidx.compose.material3  
isNotBlank androidx.compose.material3  
isNotEmpty androidx.compose.material3  launch androidx.compose.material3  let androidx.compose.material3  lightColorScheme androidx.compose.material3  listOf androidx.compose.material3  mutableStateOf androidx.compose.material3  	onFailure androidx.compose.material3  	onSuccess androidx.compose.material3  outlinedButtonColors androidx.compose.material3  padding androidx.compose.material3  provideDelegate androidx.compose.material3  remember androidx.compose.material3  rememberCoroutineScope androidx.compose.material3  setValue androidx.compose.material3  size androidx.compose.material3  spacedBy androidx.compose.material3  sumOf androidx.compose.material3  trim androidx.compose.material3  weight androidx.compose.material3  width androidx.compose.material3  Error %androidx.compose.material3.AuthResult  Success %androidx.compose.material3.AuthResult  outlinedButtonColors )androidx.compose.material3.ButtonDefaults  
cardColors 'androidx.compose.material3.CardDefaults  
background &androidx.compose.material3.ColorScheme  error &androidx.compose.material3.ColorScheme  errorContainer &androidx.compose.material3.ColorScheme  onError &androidx.compose.material3.ColorScheme  onErrorContainer &androidx.compose.material3.ColorScheme  	onPrimary &androidx.compose.material3.ColorScheme  onPrimaryContainer &androidx.compose.material3.ColorScheme  onSecondaryContainer &androidx.compose.material3.ColorScheme  	onSurface &androidx.compose.material3.ColorScheme  onSurfaceVariant &androidx.compose.material3.ColorScheme  primary &androidx.compose.material3.ColorScheme  primaryContainer &androidx.compose.material3.ColorScheme  secondaryContainer &androidx.compose.material3.ColorScheme  surface &androidx.compose.material3.ColorScheme  surfaceVariant &androidx.compose.material3.ColorScheme  colorScheme (androidx.compose.material3.MaterialTheme  compose #androidx.compose.material3.androidx  ui +androidx.compose.material3.androidx.compose  graphics .androidx.compose.material3.androidx.compose.ui  vector 7androidx.compose.material3.androidx.compose.ui.graphics  ImageVector >androidx.compose.material3.androidx.compose.ui.graphics.vector  Activity androidx.compose.runtime  ActivityResultContracts androidx.compose.runtime  AddFriendScreen androidx.compose.runtime  AlertDialog androidx.compose.runtime  	Alignment androidx.compose.runtime  Arrangement androidx.compose.runtime  	ArrowBack androidx.compose.runtime  
AuthResult androidx.compose.runtime  BottomNavButton androidx.compose.runtime  Box androidx.compose.runtime  Button androidx.compose.runtime  ButtonDefaults androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  CircularProgressIndicator androidx.compose.runtime  ClipData androidx.compose.runtime  ClipboardManager androidx.compose.runtime  Column androidx.compose.runtime  
Composable androidx.compose.runtime  Context androidx.compose.runtime  Edit androidx.compose.runtime  	ExitToApp androidx.compose.runtime  ExperimentalMaterial3Api androidx.compose.runtime  FirebaseAuth androidx.compose.runtime  
FontWeight androidx.compose.runtime  
FriendItem androidx.compose.runtime  
FriendRequest androidx.compose.runtime  FriendRequestItem androidx.compose.runtime  
FriendsScreen androidx.compose.runtime  Group androidx.compose.runtime  	GroupItem androidx.compose.runtime  
HomeScreen androidx.compose.runtime  Icon androidx.compose.runtime  
IconButton androidx.compose.runtime  Icons androidx.compose.runtime  Int androidx.compose.runtime  KeyboardOptions androidx.compose.runtime  KeyboardType androidx.compose.runtime  
LazyColumn androidx.compose.runtime  LiveStreamScreen androidx.compose.runtime  LoginScreen androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  NavHostController androidx.compose.runtime  OptIn androidx.compose.runtime  OutlinedButton androidx.compose.runtime  OutlinedTextField androidx.compose.runtime  PasswordVisualTransformation androidx.compose.runtime  Person androidx.compose.runtime  
ProfileScreen androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  RegisterScreen androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  Routes androidx.compose.runtime  Row androidx.compose.runtime  SettingItem androidx.compose.runtime  Spacer androidx.compose.runtime  State androidx.compose.runtime  String androidx.compose.runtime  Text androidx.compose.runtime  	TextAlign androidx.compose.runtime  
TextButton androidx.compose.runtime  Toast androidx.compose.runtime  Unit androidx.compose.runtime  User androidx.compose.runtime  VisualTransformation androidx.compose.runtime  align androidx.compose.runtime  android androidx.compose.runtime  androidx androidx.compose.runtime  
cardColors androidx.compose.runtime  	clickable androidx.compose.runtime  clip androidx.compose.runtime  collectAsState androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  getValue androidx.compose.runtime  height androidx.compose.runtime  heightIn androidx.compose.runtime  ifEmpty androidx.compose.runtime  
isNotBlank androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  launch androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  mutableStateOf androidx.compose.runtime  	onFailure androidx.compose.runtime  	onSuccess androidx.compose.runtime  outlinedButtonColors androidx.compose.runtime  padding androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  rememberCoroutineScope androidx.compose.runtime  setValue androidx.compose.runtime  size androidx.compose.runtime  spacedBy androidx.compose.runtime  sumOf androidx.compose.runtime  trim androidx.compose.runtime  weight androidx.compose.runtime  width androidx.compose.runtime  Error #androidx.compose.runtime.AuthResult  Success #androidx.compose.runtime.AuthResult  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  getValue androidx.compose.runtime.State  provideDelegate androidx.compose.runtime.State  compose !androidx.compose.runtime.androidx  ui )androidx.compose.runtime.androidx.compose  graphics ,androidx.compose.runtime.androidx.compose.ui  vector 5androidx.compose.runtime.androidx.compose.ui.graphics  ImageVector <androidx.compose.runtime.androidx.compose.ui.graphics.vector  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  ComposableFunction2 !androidx.compose.runtime.internal  	Alignment androidx.compose.ui  Modifier androidx.compose.ui  	BottomEnd androidx.compose.ui.Alignment  Center androidx.compose.ui.Alignment  CenterHorizontally androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  TopEnd androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  	BottomEnd 'androidx.compose.ui.Alignment.Companion  Center 'androidx.compose.ui.Alignment.Companion  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  TopEnd 'androidx.compose.ui.Alignment.Companion  	Companion androidx.compose.ui.Modifier  align androidx.compose.ui.Modifier  	clickable androidx.compose.ui.Modifier  clip androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  height androidx.compose.ui.Modifier  heightIn androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  verticalScroll androidx.compose.ui.Modifier  weight androidx.compose.ui.Modifier  width androidx.compose.ui.Modifier  align &androidx.compose.ui.Modifier.Companion  	clickable &androidx.compose.ui.Modifier.Companion  fillMaxSize &androidx.compose.ui.Modifier.Companion  fillMaxWidth &androidx.compose.ui.Modifier.Companion  height &androidx.compose.ui.Modifier.Companion  heightIn &androidx.compose.ui.Modifier.Companion  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  weight &androidx.compose.ui.Modifier.Companion  width &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  Color androidx.compose.ui.graphics  copy "androidx.compose.ui.graphics.Color  ImageVector #androidx.compose.ui.graphics.vector  LocalContext androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  Bold (androidx.compose.ui.text.font.FontWeight  	Companion (androidx.compose.ui.text.font.FontWeight  Light (androidx.compose.ui.text.font.FontWeight  Medium (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  Light 2androidx.compose.ui.text.font.FontWeight.Companion  Medium 2androidx.compose.ui.text.font.FontWeight.Companion  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  KeyboardType androidx.compose.ui.text.input  PasswordVisualTransformation androidx.compose.ui.text.input  VisualTransformation androidx.compose.ui.text.input  	Companion +androidx.compose.ui.text.input.KeyboardType  Email +androidx.compose.ui.text.input.KeyboardType  Password +androidx.compose.ui.text.input.KeyboardType  Email 5androidx.compose.ui.text.input.KeyboardType.Companion  Password 5androidx.compose.ui.text.input.KeyboardType.Companion  	Companion 3androidx.compose.ui.text.input.VisualTransformation  None 3androidx.compose.ui.text.input.VisualTransformation  None =androidx.compose.ui.text.input.VisualTransformation.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  Dp androidx.compose.ui.unit  TextUnit androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  sp androidx.compose.ui.unit  
AnimeTheme #androidx.core.app.ComponentActivity  
AppNavigation #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  FirebaseAnalytics #androidx.core.app.ComponentActivity  FirebaseHelper #androidx.core.app.ComponentActivity  
MaterialTheme #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  Surface #androidx.core.app.ComponentActivity  enableEdgeToEdge #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  logEvent #androidx.core.app.ComponentActivity  mapOf #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  testFirebaseConnection #androidx.core.app.ComponentActivity  to #androidx.core.app.ComponentActivity  NavBackStackEntry androidx.navigation  NavGraphBuilder androidx.navigation  NavHostController androidx.navigation  NavOptionsBuilder androidx.navigation  PopUpToBuilder androidx.navigation  navigate !androidx.navigation.NavController  popBackStack !androidx.navigation.NavController  AddFriendScreen #androidx.navigation.NavGraphBuilder  
FriendsScreen #androidx.navigation.NavGraphBuilder  
HomeScreen #androidx.navigation.NavGraphBuilder  LiveStreamScreen #androidx.navigation.NavGraphBuilder  LoginScreen #androidx.navigation.NavGraphBuilder  
ProfileScreen #androidx.navigation.NavGraphBuilder  RegisterScreen #androidx.navigation.NavGraphBuilder  Routes #androidx.navigation.NavGraphBuilder  
composable #androidx.navigation.NavGraphBuilder  navigate %androidx.navigation.NavHostController  popBackStack %androidx.navigation.NavHostController  Routes %androidx.navigation.NavOptionsBuilder  popUpTo %androidx.navigation.NavOptionsBuilder  	inclusive "androidx.navigation.PopUpToBuilder  NavHost androidx.navigation.compose  
composable androidx.navigation.compose  rememberNavController androidx.navigation.compose  GoogleSignIn &com.google.android.gms.auth.api.signin  GoogleSignInAccount &com.google.android.gms.auth.api.signin  GoogleSignInClient &com.google.android.gms.auth.api.signin  GoogleSignInOptions &com.google.android.gms.auth.api.signin  	getClient 3com.google.android.gms.auth.api.signin.GoogleSignIn  getLastSignedInAccount 3com.google.android.gms.auth.api.signin.GoogleSignIn  getSignedInAccountFromIntent 3com.google.android.gms.auth.api.signin.GoogleSignIn  displayName :com.google.android.gms.auth.api.signin.GoogleSignInAccount  email :com.google.android.gms.auth.api.signin.GoogleSignInAccount  idToken :com.google.android.gms.auth.api.signin.GoogleSignInAccount  signInIntent 9com.google.android.gms.auth.api.signin.GoogleSignInClient  signOut 9com.google.android.gms.auth.api.signin.GoogleSignInClient  Builder :com.google.android.gms.auth.api.signin.GoogleSignInOptions  DEFAULT_SIGN_IN :com.google.android.gms.auth.api.signin.GoogleSignInOptions  build Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestEmail Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  requestIdToken Bcom.google.android.gms.auth.api.signin.GoogleSignInOptions.Builder  ApiException !com.google.android.gms.common.api  message .com.google.android.gms.common.api.ApiException  
statusCode .com.google.android.gms.common.api.ApiException  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  await !com.google.android.gms.tasks.Task  	getResult !com.google.android.gms.tasks.Task  FirebaseApp com.google.firebase  
initializeApp com.google.firebase.FirebaseApp  FirebaseAnalytics com.google.firebase.analytics  getInstance /com.google.firebase.analytics.FirebaseAnalytics  logEvent /com.google.firebase.analytics.FirebaseAnalytics  AuthCredential com.google.firebase.auth  FirebaseAuth com.google.firebase.auth  FirebaseUser com.google.firebase.auth  GoogleAuthProvider com.google.firebase.auth  user #com.google.firebase.auth.AuthResult  createUserWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  currentUser %com.google.firebase.auth.FirebaseAuth  getInstance %com.google.firebase.auth.FirebaseAuth  signInWithCredential %com.google.firebase.auth.FirebaseAuth  signInWithEmailAndPassword %com.google.firebase.auth.FirebaseAuth  signOut %com.google.firebase.auth.FirebaseAuth  displayName %com.google.firebase.auth.FirebaseUser  email %com.google.firebase.auth.FirebaseUser  isEmailVerified %com.google.firebase.auth.FirebaseUser  let %com.google.firebase.auth.FirebaseUser  photoUrl %com.google.firebase.auth.FirebaseUser  uid %com.google.firebase.auth.FirebaseUser  
getCredential +com.google.firebase.auth.GoogleAuthProvider  isEmailVerified !com.google.firebase.auth.UserInfo  Boolean com.google.firebase.database  DataSnapshot com.google.firebase.database  
DatabaseError com.google.firebase.database  DatabaseException com.google.firebase.database  DatabaseReference com.google.firebase.database  	Exception com.google.firebase.database  FRIENDSHIPS_PATH com.google.firebase.database  FRIEND_REQUESTS_PATH com.google.firebase.database  FirebaseAuth com.google.firebase.database  FirebaseDatabase com.google.firebase.database  Flow com.google.firebase.database  
FriendRequest com.google.firebase.database  FriendRequestStatus com.google.firebase.database  
Friendship com.google.firebase.database  List com.google.firebase.database  Log com.google.firebase.database  Query com.google.firebase.database  Result com.google.firebase.database  String com.google.firebase.database  System com.google.firebase.database  TAG com.google.firebase.database  
USERS_PATH com.google.firebase.database  Unit com.google.firebase.database  User com.google.firebase.database  ValueEventListener com.google.firebase.database  auth com.google.firebase.database  await com.google.firebase.database  callbackFlow com.google.firebase.database  close com.google.firebase.database  database com.google.firebase.database  	emptyList com.google.firebase.database  failure com.google.firebase.database  getFriendsDetails com.google.firebase.database  java com.google.firebase.database  
mutableListOf com.google.firebase.database  substringBefore com.google.firebase.database  success com.google.firebase.database  trySend com.google.firebase.database  children )com.google.firebase.database.DataSnapshot  getValue )com.google.firebase.database.DataSnapshot  ref )com.google.firebase.database.DataSnapshot  value )com.google.firebase.database.DataSnapshot  message *com.google.firebase.database.DatabaseError  toException *com.google.firebase.database.DatabaseError  addValueEventListener .com.google.firebase.database.DatabaseReference  child .com.google.firebase.database.DatabaseReference  get .com.google.firebase.database.DatabaseReference  key .com.google.firebase.database.DatabaseReference  orderByChild .com.google.firebase.database.DatabaseReference  push .com.google.firebase.database.DatabaseReference  removeEventListener .com.google.firebase.database.DatabaseReference  removeValue .com.google.firebase.database.DatabaseReference  setValue .com.google.firebase.database.DatabaseReference  getInstance -com.google.firebase.database.FirebaseDatabase  getReference -com.google.firebase.database.FirebaseDatabase  setPersistenceEnabled -com.google.firebase.database.FirebaseDatabase  addValueEventListener "com.google.firebase.database.Query  equalTo "com.google.firebase.database.Query  get "com.google.firebase.database.Query  orderByChild "com.google.firebase.database.Query  removeEventListener "com.google.firebase.database.Query  FirebaseStorage com.google.firebase.storage  StorageReference com.google.firebase.storage  getInstance +com.google.firebase.storage.FirebaseStorage  getReference +com.google.firebase.storage.FirebaseStorage  path ,com.google.firebase.storage.StorageReference  
AnimeTheme com.web22.myapplication  Any com.web22.myapplication  
AppNavigation com.web22.myapplication  Application com.web22.myapplication  Boolean com.web22.myapplication  Bundle com.web22.myapplication  ComponentActivity com.web22.myapplication  Double com.web22.myapplication  	Exception com.web22.myapplication  FirebaseAnalytics com.web22.myapplication  FirebaseApp com.web22.myapplication  FirebaseAuth com.web22.myapplication  FirebaseDatabase com.web22.myapplication  FirebaseHelper com.web22.myapplication  FirebaseStorage com.web22.myapplication  Int com.web22.myapplication  Log com.web22.myapplication  Long com.web22.myapplication  MainActivity com.web22.myapplication  Map com.web22.myapplication  
MaterialTheme com.web22.myapplication  Modifier com.web22.myapplication  
MyApplication com.web22.myapplication  String com.web22.myapplication  Surface com.web22.myapplication  System com.web22.myapplication  Unit com.web22.myapplication  android com.web22.myapplication  
component1 com.web22.myapplication  
component2 com.web22.myapplication  fillMaxSize com.web22.myapplication  forEach com.web22.myapplication  logEvent com.web22.myapplication  mapOf com.web22.myapplication  testFirebaseConnection com.web22.myapplication  to com.web22.myapplication  FirebaseAuth &com.web22.myapplication.FirebaseHelper  FirebaseDatabase &com.web22.myapplication.FirebaseHelper  FirebaseStorage &com.web22.myapplication.FirebaseHelper  Log &com.web22.myapplication.FirebaseHelper  System &com.web22.myapplication.FirebaseHelper  TAG &com.web22.myapplication.FirebaseHelper  android &com.web22.myapplication.FirebaseHelper  
component1 &com.web22.myapplication.FirebaseHelper  
component2 &com.web22.myapplication.FirebaseHelper  getCurrentUser &com.web22.myapplication.FirebaseHelper  getStorageReference &com.web22.myapplication.FirebaseHelper  logEvent &com.web22.myapplication.FirebaseHelper  mapOf &com.web22.myapplication.FirebaseHelper  testFirebaseConnection &com.web22.myapplication.FirebaseHelper  to &com.web22.myapplication.FirebaseHelper  writeToDatabase &com.web22.myapplication.FirebaseHelper  
AnimeTheme $com.web22.myapplication.MainActivity  
AppNavigation $com.web22.myapplication.MainActivity  FirebaseAnalytics $com.web22.myapplication.MainActivity  FirebaseHelper $com.web22.myapplication.MainActivity  
MaterialTheme $com.web22.myapplication.MainActivity  Modifier $com.web22.myapplication.MainActivity  Surface $com.web22.myapplication.MainActivity  enableEdgeToEdge $com.web22.myapplication.MainActivity  fillMaxSize $com.web22.myapplication.MainActivity  logEvent $com.web22.myapplication.MainActivity  mapOf $com.web22.myapplication.MainActivity  
setContent $com.web22.myapplication.MainActivity  testFirebaseConnection $com.web22.myapplication.MainActivity  to $com.web22.myapplication.MainActivity  FirebaseAnalytics %com.web22.myapplication.MyApplication  FirebaseApp %com.web22.myapplication.MyApplication  FirebaseAuth %com.web22.myapplication.MyApplication  FirebaseDatabase %com.web22.myapplication.MyApplication  FirebaseStorage %com.web22.myapplication.MyApplication  ApiException com.web22.myapplication.auth  
AuthResult com.web22.myapplication.auth  AuthenticationManager com.web22.myapplication.auth  Boolean com.web22.myapplication.auth  Context com.web22.myapplication.auth  	Exception com.web22.myapplication.auth  FirebaseAuth com.web22.myapplication.auth  FirebaseDatabase com.web22.myapplication.auth  FirebaseUser com.web22.myapplication.auth  GoogleAuthProvider com.web22.myapplication.auth  GoogleSignIn com.web22.myapplication.auth  GoogleSignInAccount com.web22.myapplication.auth  GoogleSignInClient com.web22.myapplication.auth  GoogleSignInOptions com.web22.myapplication.auth  Intent com.web22.myapplication.auth  Log com.web22.myapplication.auth  String com.web22.myapplication.auth  System com.web22.myapplication.auth  TAG com.web22.myapplication.auth  User com.web22.myapplication.auth  UserInfo com.web22.myapplication.auth  await com.web22.myapplication.auth  java com.web22.myapplication.auth  let com.web22.myapplication.auth  substringBefore com.web22.myapplication.auth  take com.web22.myapplication.auth  
AuthResult 'com.web22.myapplication.auth.AuthResult  Error 'com.web22.myapplication.auth.AuthResult  FirebaseUser 'com.web22.myapplication.auth.AuthResult  String 'com.web22.myapplication.auth.AuthResult  Success 'com.web22.myapplication.auth.AuthResult  message -com.web22.myapplication.auth.AuthResult.Error  ApiException 2com.web22.myapplication.auth.AuthenticationManager  
AuthResult 2com.web22.myapplication.auth.AuthenticationManager  Boolean 2com.web22.myapplication.auth.AuthenticationManager  Context 2com.web22.myapplication.auth.AuthenticationManager  	Exception 2com.web22.myapplication.auth.AuthenticationManager  FirebaseAuth 2com.web22.myapplication.auth.AuthenticationManager  FirebaseDatabase 2com.web22.myapplication.auth.AuthenticationManager  FirebaseUser 2com.web22.myapplication.auth.AuthenticationManager  GoogleAuthProvider 2com.web22.myapplication.auth.AuthenticationManager  GoogleSignIn 2com.web22.myapplication.auth.AuthenticationManager  GoogleSignInAccount 2com.web22.myapplication.auth.AuthenticationManager  GoogleSignInClient 2com.web22.myapplication.auth.AuthenticationManager  GoogleSignInOptions 2com.web22.myapplication.auth.AuthenticationManager  Intent 2com.web22.myapplication.auth.AuthenticationManager  Log 2com.web22.myapplication.auth.AuthenticationManager  String 2com.web22.myapplication.auth.AuthenticationManager  System 2com.web22.myapplication.auth.AuthenticationManager  TAG 2com.web22.myapplication.auth.AuthenticationManager  User 2com.web22.myapplication.auth.AuthenticationManager  UserInfo 2com.web22.myapplication.auth.AuthenticationManager  auth 2com.web22.myapplication.auth.AuthenticationManager  await 2com.web22.myapplication.auth.AuthenticationManager  context 2com.web22.myapplication.auth.AuthenticationManager  createUserWithEmailAndPassword 2com.web22.myapplication.auth.AuthenticationManager  database 2com.web22.myapplication.auth.AuthenticationManager  firebaseAuthWithGoogle 2com.web22.myapplication.auth.AuthenticationManager  getCurrentUser 2com.web22.myapplication.auth.AuthenticationManager  getGoogleSignInIntent 2com.web22.myapplication.auth.AuthenticationManager  getUserInfo 2com.web22.myapplication.auth.AuthenticationManager  googleSignInClient 2com.web22.myapplication.auth.AuthenticationManager  handleGoogleSignInResult 2com.web22.myapplication.auth.AuthenticationManager  java 2com.web22.myapplication.auth.AuthenticationManager  let 2com.web22.myapplication.auth.AuthenticationManager  saveUserToDatabase 2com.web22.myapplication.auth.AuthenticationManager  signInWithEmailAndPassword 2com.web22.myapplication.auth.AuthenticationManager  signOut 2com.web22.myapplication.auth.AuthenticationManager  substringBefore 2com.web22.myapplication.auth.AuthenticationManager  take 2com.web22.myapplication.auth.AuthenticationManager  ApiException <com.web22.myapplication.auth.AuthenticationManager.Companion  
AuthResult <com.web22.myapplication.auth.AuthenticationManager.Companion  FirebaseAuth <com.web22.myapplication.auth.AuthenticationManager.Companion  FirebaseDatabase <com.web22.myapplication.auth.AuthenticationManager.Companion  GoogleAuthProvider <com.web22.myapplication.auth.AuthenticationManager.Companion  GoogleSignIn <com.web22.myapplication.auth.AuthenticationManager.Companion  GoogleSignInOptions <com.web22.myapplication.auth.AuthenticationManager.Companion  Log <com.web22.myapplication.auth.AuthenticationManager.Companion  System <com.web22.myapplication.auth.AuthenticationManager.Companion  TAG <com.web22.myapplication.auth.AuthenticationManager.Companion  User <com.web22.myapplication.auth.AuthenticationManager.Companion  UserInfo <com.web22.myapplication.auth.AuthenticationManager.Companion  await <com.web22.myapplication.auth.AuthenticationManager.Companion  java <com.web22.myapplication.auth.AuthenticationManager.Companion  let <com.web22.myapplication.auth.AuthenticationManager.Companion  substringBefore <com.web22.myapplication.auth.AuthenticationManager.Companion  take <com.web22.myapplication.auth.AuthenticationManager.Companion  displayName %com.web22.myapplication.auth.UserInfo  email %com.web22.myapplication.auth.UserInfo  isEmailVerified %com.web22.myapplication.auth.UserInfo  source %com.web22.myapplication.auth.UserInfo  Boolean com.web22.myapplication.data  DataSnapshot com.web22.myapplication.data  
DatabaseError com.web22.myapplication.data  	Exception com.web22.myapplication.data  FRIENDSHIPS_PATH com.web22.myapplication.data  FRIEND_REQUESTS_PATH com.web22.myapplication.data  FirebaseAuth com.web22.myapplication.data  FirebaseDatabase com.web22.myapplication.data  Flow com.web22.myapplication.data  
FriendRequest com.web22.myapplication.data  FriendRequestStatus com.web22.myapplication.data  FriendsManager com.web22.myapplication.data  
Friendship com.web22.myapplication.data  List com.web22.myapplication.data  Log com.web22.myapplication.data  Result com.web22.myapplication.data  String com.web22.myapplication.data  System com.web22.myapplication.data  TAG com.web22.myapplication.data  
USERS_PATH com.web22.myapplication.data  Unit com.web22.myapplication.data  User com.web22.myapplication.data  ValueEventListener com.web22.myapplication.data  auth com.web22.myapplication.data  await com.web22.myapplication.data  callbackFlow com.web22.myapplication.data  close com.web22.myapplication.data  database com.web22.myapplication.data  	emptyList com.web22.myapplication.data  failure com.web22.myapplication.data  getFriendsDetails com.web22.myapplication.data  java com.web22.myapplication.data  
mutableListOf com.web22.myapplication.data  substringBefore com.web22.myapplication.data  success com.web22.myapplication.data  trySend com.web22.myapplication.data  Boolean +com.web22.myapplication.data.FriendsManager  DataSnapshot +com.web22.myapplication.data.FriendsManager  
DatabaseError +com.web22.myapplication.data.FriendsManager  	Exception +com.web22.myapplication.data.FriendsManager  FRIENDSHIPS_PATH +com.web22.myapplication.data.FriendsManager  FRIEND_REQUESTS_PATH +com.web22.myapplication.data.FriendsManager  FirebaseAuth +com.web22.myapplication.data.FriendsManager  FirebaseDatabase +com.web22.myapplication.data.FriendsManager  Flow +com.web22.myapplication.data.FriendsManager  
FriendRequest +com.web22.myapplication.data.FriendsManager  FriendRequestStatus +com.web22.myapplication.data.FriendsManager  
Friendship +com.web22.myapplication.data.FriendsManager  List +com.web22.myapplication.data.FriendsManager  Log +com.web22.myapplication.data.FriendsManager  Result +com.web22.myapplication.data.FriendsManager  String +com.web22.myapplication.data.FriendsManager  System +com.web22.myapplication.data.FriendsManager  TAG +com.web22.myapplication.data.FriendsManager  
USERS_PATH +com.web22.myapplication.data.FriendsManager  Unit +com.web22.myapplication.data.FriendsManager  User +com.web22.myapplication.data.FriendsManager  ValueEventListener +com.web22.myapplication.data.FriendsManager  acceptFriendRequest +com.web22.myapplication.data.FriendsManager  auth +com.web22.myapplication.data.FriendsManager  await +com.web22.myapplication.data.FriendsManager  
awaitClose +com.web22.myapplication.data.FriendsManager  callbackFlow +com.web22.myapplication.data.FriendsManager  checkExistingFriendRequest +com.web22.myapplication.data.FriendsManager  checkExistingFriendship +com.web22.myapplication.data.FriendsManager  close +com.web22.myapplication.data.FriendsManager  database +com.web22.myapplication.data.FriendsManager  	emptyList +com.web22.myapplication.data.FriendsManager  failure +com.web22.myapplication.data.FriendsManager  
getFriends +com.web22.myapplication.data.FriendsManager  getFriendsDetails +com.web22.myapplication.data.FriendsManager  getIncomingFriendRequests +com.web22.myapplication.data.FriendsManager  java +com.web22.myapplication.data.FriendsManager  
mutableListOf +com.web22.myapplication.data.FriendsManager  rejectFriendRequest +com.web22.myapplication.data.FriendsManager  removeFriend +com.web22.myapplication.data.FriendsManager  searchUserByUid +com.web22.myapplication.data.FriendsManager  sendFriendRequest +com.web22.myapplication.data.FriendsManager  substringBefore +com.web22.myapplication.data.FriendsManager  success +com.web22.myapplication.data.FriendsManager  trySend +com.web22.myapplication.data.FriendsManager  	Exception 5com.web22.myapplication.data.FriendsManager.Companion  FRIENDSHIPS_PATH 5com.web22.myapplication.data.FriendsManager.Companion  FRIEND_REQUESTS_PATH 5com.web22.myapplication.data.FriendsManager.Companion  FirebaseAuth 5com.web22.myapplication.data.FriendsManager.Companion  FirebaseDatabase 5com.web22.myapplication.data.FriendsManager.Companion  
FriendRequest 5com.web22.myapplication.data.FriendsManager.Companion  FriendRequestStatus 5com.web22.myapplication.data.FriendsManager.Companion  
Friendship 5com.web22.myapplication.data.FriendsManager.Companion  Log 5com.web22.myapplication.data.FriendsManager.Companion  Result 5com.web22.myapplication.data.FriendsManager.Companion  System 5com.web22.myapplication.data.FriendsManager.Companion  TAG 5com.web22.myapplication.data.FriendsManager.Companion  
USERS_PATH 5com.web22.myapplication.data.FriendsManager.Companion  Unit 5com.web22.myapplication.data.FriendsManager.Companion  User 5com.web22.myapplication.data.FriendsManager.Companion  auth 5com.web22.myapplication.data.FriendsManager.Companion  await 5com.web22.myapplication.data.FriendsManager.Companion  
awaitClose 5com.web22.myapplication.data.FriendsManager.Companion  callbackFlow 5com.web22.myapplication.data.FriendsManager.Companion  close 5com.web22.myapplication.data.FriendsManager.Companion  database 5com.web22.myapplication.data.FriendsManager.Companion  	emptyList 5com.web22.myapplication.data.FriendsManager.Companion  failure 5com.web22.myapplication.data.FriendsManager.Companion  getFriendsDetails 5com.web22.myapplication.data.FriendsManager.Companion  java 5com.web22.myapplication.data.FriendsManager.Companion  
mutableListOf 5com.web22.myapplication.data.FriendsManager.Companion  substringBefore 5com.web22.myapplication.data.FriendsManager.Companion  success 5com.web22.myapplication.data.FriendsManager.Companion  trySend 5com.web22.myapplication.data.FriendsManager.Companion  Boolean #com.web22.myapplication.data.models  DataSnapshot #com.web22.myapplication.data.models  
DatabaseError #com.web22.myapplication.data.models  	Exception #com.web22.myapplication.data.models  FRIENDSHIPS_PATH #com.web22.myapplication.data.models  FRIEND_REQUESTS_PATH #com.web22.myapplication.data.models  FirebaseAuth #com.web22.myapplication.data.models  FirebaseDatabase #com.web22.myapplication.data.models  Flow #com.web22.myapplication.data.models  
FriendRequest #com.web22.myapplication.data.models  FriendRequestStatus #com.web22.myapplication.data.models  
Friendship #com.web22.myapplication.data.models  List #com.web22.myapplication.data.models  Log #com.web22.myapplication.data.models  Long #com.web22.myapplication.data.models  Result #com.web22.myapplication.data.models  String #com.web22.myapplication.data.models  System #com.web22.myapplication.data.models  TAG #com.web22.myapplication.data.models  
USERS_PATH #com.web22.myapplication.data.models  Unit #com.web22.myapplication.data.models  User #com.web22.myapplication.data.models  ValueEventListener #com.web22.myapplication.data.models  auth #com.web22.myapplication.data.models  await #com.web22.myapplication.data.models  callbackFlow #com.web22.myapplication.data.models  close #com.web22.myapplication.data.models  database #com.web22.myapplication.data.models  	emptyList #com.web22.myapplication.data.models  failure #com.web22.myapplication.data.models  getFriendsDetails #com.web22.myapplication.data.models  java #com.web22.myapplication.data.models  
mutableListOf #com.web22.myapplication.data.models  substringBefore #com.web22.myapplication.data.models  success #com.web22.myapplication.data.models  trySend #com.web22.myapplication.data.models  FriendRequestStatus 1com.web22.myapplication.data.models.FriendRequest  
fromUserEmail 1com.web22.myapplication.data.models.FriendRequest  
fromUserId 1com.web22.myapplication.data.models.FriendRequest  fromUserName 1com.web22.myapplication.data.models.FriendRequest  id 1com.web22.myapplication.data.models.FriendRequest  status 1com.web22.myapplication.data.models.FriendRequest  toUserId 1com.web22.myapplication.data.models.FriendRequest  ACCEPTED 7com.web22.myapplication.data.models.FriendRequestStatus  PENDING 7com.web22.myapplication.data.models.FriendRequestStatus  REJECTED 7com.web22.myapplication.data.models.FriendRequestStatus  name 7com.web22.myapplication.data.models.FriendRequestStatus  user1Id .com.web22.myapplication.data.models.Friendship  user2Id .com.web22.myapplication.data.models.Friendship  displayName (com.web22.myapplication.data.models.User  email (com.web22.myapplication.data.models.User  isOnline (com.web22.myapplication.data.models.User  let (com.web22.myapplication.data.models.User  uid (com.web22.myapplication.data.models.User  AddFriendScreen "com.web22.myapplication.navigation  
AppNavigation "com.web22.myapplication.navigation  
Composable "com.web22.myapplication.navigation  FirebaseAuth "com.web22.myapplication.navigation  
FriendsScreen "com.web22.myapplication.navigation  
HomeScreen "com.web22.myapplication.navigation  LiveStreamScreen "com.web22.myapplication.navigation  LoginScreen "com.web22.myapplication.navigation  NavHostController "com.web22.myapplication.navigation  
ProfileScreen "com.web22.myapplication.navigation  RegisterScreen "com.web22.myapplication.navigation  Routes "com.web22.myapplication.navigation  
ADD_FRIEND )com.web22.myapplication.navigation.Routes  FRIENDS )com.web22.myapplication.navigation.Routes  HOME )com.web22.myapplication.navigation.Routes  LIVE_STREAM )com.web22.myapplication.navigation.Routes  LOGIN )com.web22.myapplication.navigation.Routes  PROFILE )com.web22.myapplication.navigation.Routes  REGISTER )com.web22.myapplication.navigation.Routes  Activity "com.web22.myapplication.ui.screens  ActivityResultContracts "com.web22.myapplication.ui.screens  AddFriendScreen "com.web22.myapplication.ui.screens  AlertDialog "com.web22.myapplication.ui.screens  	Alignment "com.web22.myapplication.ui.screens  Arrangement "com.web22.myapplication.ui.screens  	ArrowBack "com.web22.myapplication.ui.screens  
AuthResult "com.web22.myapplication.ui.screens  BottomNavButton "com.web22.myapplication.ui.screens  Box "com.web22.myapplication.ui.screens  Button "com.web22.myapplication.ui.screens  ButtonDefaults "com.web22.myapplication.ui.screens  Card "com.web22.myapplication.ui.screens  CardDefaults "com.web22.myapplication.ui.screens  CircleShape "com.web22.myapplication.ui.screens  CircularProgressIndicator "com.web22.myapplication.ui.screens  ClipData "com.web22.myapplication.ui.screens  ClipboardManager "com.web22.myapplication.ui.screens  Column "com.web22.myapplication.ui.screens  
Composable "com.web22.myapplication.ui.screens  Context "com.web22.myapplication.ui.screens  Edit "com.web22.myapplication.ui.screens  	ExitToApp "com.web22.myapplication.ui.screens  ExperimentalMaterial3Api "com.web22.myapplication.ui.screens  FirebaseAuth "com.web22.myapplication.ui.screens  
FontWeight "com.web22.myapplication.ui.screens  
FriendItem "com.web22.myapplication.ui.screens  
FriendRequest "com.web22.myapplication.ui.screens  FriendRequestItem "com.web22.myapplication.ui.screens  
FriendsScreen "com.web22.myapplication.ui.screens  Group "com.web22.myapplication.ui.screens  	GroupItem "com.web22.myapplication.ui.screens  GroupsScreen "com.web22.myapplication.ui.screens  
HomeScreen "com.web22.myapplication.ui.screens  Icon "com.web22.myapplication.ui.screens  
IconButton "com.web22.myapplication.ui.screens  Icons "com.web22.myapplication.ui.screens  Int "com.web22.myapplication.ui.screens  KeyboardOptions "com.web22.myapplication.ui.screens  KeyboardType "com.web22.myapplication.ui.screens  
LazyColumn "com.web22.myapplication.ui.screens  LiveStreamScreen "com.web22.myapplication.ui.screens  LoginScreen "com.web22.myapplication.ui.screens  
MaterialTheme "com.web22.myapplication.ui.screens  Modifier "com.web22.myapplication.ui.screens  OptIn "com.web22.myapplication.ui.screens  OutlinedButton "com.web22.myapplication.ui.screens  OutlinedTextField "com.web22.myapplication.ui.screens  PasswordVisualTransformation "com.web22.myapplication.ui.screens  Person "com.web22.myapplication.ui.screens  
ProfileScreen "com.web22.myapplication.ui.screens  RegisterScreen "com.web22.myapplication.ui.screens  RoundedCornerShape "com.web22.myapplication.ui.screens  Row "com.web22.myapplication.ui.screens  SettingItem "com.web22.myapplication.ui.screens  Spacer "com.web22.myapplication.ui.screens  String "com.web22.myapplication.ui.screens  Text "com.web22.myapplication.ui.screens  	TextAlign "com.web22.myapplication.ui.screens  
TextButton "com.web22.myapplication.ui.screens  Toast "com.web22.myapplication.ui.screens  Unit "com.web22.myapplication.ui.screens  User "com.web22.myapplication.ui.screens  VisualTransformation "com.web22.myapplication.ui.screens  align "com.web22.myapplication.ui.screens  android "com.web22.myapplication.ui.screens  androidx "com.web22.myapplication.ui.screens  
cardColors "com.web22.myapplication.ui.screens  	clickable "com.web22.myapplication.ui.screens  clip "com.web22.myapplication.ui.screens  collectAsState "com.web22.myapplication.ui.screens  	emptyList "com.web22.myapplication.ui.screens  fillMaxSize "com.web22.myapplication.ui.screens  fillMaxWidth "com.web22.myapplication.ui.screens  getValue "com.web22.myapplication.ui.screens  height "com.web22.myapplication.ui.screens  heightIn "com.web22.myapplication.ui.screens  ifEmpty "com.web22.myapplication.ui.screens  
isNotBlank "com.web22.myapplication.ui.screens  
isNotEmpty "com.web22.myapplication.ui.screens  launch "com.web22.myapplication.ui.screens  let "com.web22.myapplication.ui.screens  listOf "com.web22.myapplication.ui.screens  mutableStateOf "com.web22.myapplication.ui.screens  	onFailure "com.web22.myapplication.ui.screens  	onSuccess "com.web22.myapplication.ui.screens  outlinedButtonColors "com.web22.myapplication.ui.screens  padding "com.web22.myapplication.ui.screens  provideDelegate "com.web22.myapplication.ui.screens  remember "com.web22.myapplication.ui.screens  rememberCoroutineScope "com.web22.myapplication.ui.screens  setValue "com.web22.myapplication.ui.screens  size "com.web22.myapplication.ui.screens  spacedBy "com.web22.myapplication.ui.screens  sumOf "com.web22.myapplication.ui.screens  trim "com.web22.myapplication.ui.screens  weight "com.web22.myapplication.ui.screens  width "com.web22.myapplication.ui.screens  Error -com.web22.myapplication.ui.screens.AuthResult  Success -com.web22.myapplication.ui.screens.AuthResult  description (com.web22.myapplication.ui.screens.Group  memberCount (com.web22.myapplication.ui.screens.Group  name (com.web22.myapplication.ui.screens.Group  unreadMessages (com.web22.myapplication.ui.screens.Group  compose +com.web22.myapplication.ui.screens.androidx  ui 3com.web22.myapplication.ui.screens.androidx.compose  graphics 6com.web22.myapplication.ui.screens.androidx.compose.ui  vector ?com.web22.myapplication.ui.screens.androidx.compose.ui.graphics  ImageVector Fcom.web22.myapplication.ui.screens.androidx.compose.ui.graphics.vector  
AnimeTheme  com.web22.myapplication.ui.theme  Boolean  com.web22.myapplication.ui.theme  Build  com.web22.myapplication.ui.theme  
Composable  com.web22.myapplication.ui.theme  DarkColorScheme  com.web22.myapplication.ui.theme  
FontFamily  com.web22.myapplication.ui.theme  
FontWeight  com.web22.myapplication.ui.theme  LightColorScheme  com.web22.myapplication.ui.theme  Pink40  com.web22.myapplication.ui.theme  Pink80  com.web22.myapplication.ui.theme  Purple40  com.web22.myapplication.ui.theme  Purple80  com.web22.myapplication.ui.theme  PurpleGrey40  com.web22.myapplication.ui.theme  PurpleGrey80  com.web22.myapplication.ui.theme  
Typography  com.web22.myapplication.ui.theme  Unit  com.web22.myapplication.ui.theme  Class 	java.lang  	Exception 	java.lang  message java.lang.Exception  currentTimeMillis java.lang.System  
BigDecimal 	java.math  
BigInteger 	java.math  matches java.util.regex.Matcher  matcher java.util.regex.Pattern  CharSequence kotlin  	Function0 kotlin  	Function1 kotlin  Nothing kotlin  OptIn kotlin  Pair kotlin  Result kotlin  	Throwable kotlin  let kotlin  	onFailure kotlin  	onSuccess kotlin  to kotlin  not kotlin.Boolean  sp 
kotlin.Double  invoke kotlin.Function0  invoke kotlin.Function1  	compareTo 
kotlin.Int  inc 
kotlin.Int  toString 
kotlin.Int  	Companion 
kotlin.Result  failure 
kotlin.Result  	onFailure 
kotlin.Result  	onSuccess 
kotlin.Result  success 
kotlin.Result  failure kotlin.Result.Companion  success kotlin.Result.Companion  ifEmpty 
kotlin.String  
isNotBlank 
kotlin.String  length 
kotlin.String  let 
kotlin.String  substringBefore 
kotlin.String  take 
kotlin.String  to 
kotlin.String  trim 
kotlin.String  message kotlin.Throwable  Iterator kotlin.collections  List kotlin.collections  Map kotlin.collections  MutableIterator kotlin.collections  MutableList kotlin.collections  
component1 kotlin.collections  
component2 kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  ifEmpty kotlin.collections  
isNotEmpty kotlin.collections  listOf kotlin.collections  mapOf kotlin.collections  
mutableListOf kotlin.collections  sumOf kotlin.collections  sumOfInt kotlin.collections  take kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  isEmpty kotlin.collections.List  
isNotEmpty kotlin.collections.List  iterator kotlin.collections.List  size kotlin.collections.List  sumOf kotlin.collections.List  Entry kotlin.collections.Map  
component1 kotlin.collections.Map.Entry  
component2 kotlin.collections.Map.Entry  iterator "kotlin.collections.MutableIterable  hasNext "kotlin.collections.MutableIterator  next "kotlin.collections.MutableIterator  add kotlin.collections.MutableList  SuspendFunction1 kotlin.coroutines  java 
kotlin.jvm  KMutableProperty0 kotlin.reflect  
KProperty0 kotlin.reflect  java kotlin.reflect.KClass  Sequence kotlin.sequences  forEach kotlin.sequences  ifEmpty kotlin.sequences  sumOf kotlin.sequences  take kotlin.sequences  forEach kotlin.text  ifEmpty kotlin.text  
isNotBlank kotlin.text  
isNotEmpty kotlin.text  substringBefore kotlin.text  sumOf kotlin.text  take kotlin.text  trim kotlin.text  CoroutineScope kotlinx.coroutines  Job kotlinx.coroutines  launch kotlinx.coroutines  launch !kotlinx.coroutines.CoroutineScope  	onFailure !kotlinx.coroutines.CoroutineScope  	onSuccess !kotlinx.coroutines.CoroutineScope  trim !kotlinx.coroutines.CoroutineScope  
ChannelResult kotlinx.coroutines.channels  
ProducerScope kotlinx.coroutines.channels  
awaitClose kotlinx.coroutines.channels  FRIENDSHIPS_PATH )kotlinx.coroutines.channels.ProducerScope  FRIEND_REQUESTS_PATH )kotlinx.coroutines.channels.ProducerScope  
FriendRequest )kotlinx.coroutines.channels.ProducerScope  FriendRequestStatus )kotlinx.coroutines.channels.ProducerScope  
Friendship )kotlinx.coroutines.channels.ProducerScope  Log )kotlinx.coroutines.channels.ProducerScope  TAG )kotlinx.coroutines.channels.ProducerScope  auth )kotlinx.coroutines.channels.ProducerScope  
awaitClose )kotlinx.coroutines.channels.ProducerScope  close )kotlinx.coroutines.channels.ProducerScope  database )kotlinx.coroutines.channels.ProducerScope  	emptyList )kotlinx.coroutines.channels.ProducerScope  getFriendsDetails )kotlinx.coroutines.channels.ProducerScope  java )kotlinx.coroutines.channels.ProducerScope  
mutableListOf )kotlinx.coroutines.channels.ProducerScope  trySend )kotlinx.coroutines.channels.ProducerScope  Flow kotlinx.coroutines.flow  callbackFlow kotlinx.coroutines.flow  collectAsState kotlinx.coroutines.flow.Flow  await kotlinx.coroutines.tasks                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    