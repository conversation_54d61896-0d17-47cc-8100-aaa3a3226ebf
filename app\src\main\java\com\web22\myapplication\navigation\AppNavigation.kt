package com.web22.myapplication.navigation

import androidx.compose.runtime.*
import androidx.navigation.NavHostController
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.compose.rememberNavController
import com.google.firebase.auth.FirebaseAuth
import com.web22.myapplication.ui.screens.AddFriendScreen
import com.web22.myapplication.ui.screens.FriendsScreen
import com.web22.myapplication.ui.screens.HomeScreen
import com.web22.myapplication.ui.screens.LiveStreamScreen
import com.web22.myapplication.ui.screens.LoginScreen
import com.web22.myapplication.ui.screens.ProfileScreen
import com.web22.myapplication.ui.screens.RegisterScreen

// مسارات التنقل
object Routes {
    const val LOGIN = "login"
    const val REGISTER = "register"
    const val HOME = "home"
    const val FRIENDS = "friends"
    const val ADD_FRIEND = "add_friend"
    const val LIVE_STREAM = "live_stream"
    const val PROFILE = "profile"
}

@Composable
fun AppNavigation(
    navController: NavHostController = rememberNavController()
) {
    // التحقق من حالة تسجيل الدخول
    val currentUser = FirebaseAuth.getInstance().currentUser
    val startDestination = if (currentUser != null) Routes.HOME else Routes.LOGIN
    
    NavHost(
        navController = navController,
        startDestination = startDestination
    ) {
        // شاشة تسجيل الدخول
        composable(Routes.LOGIN) {
            LoginScreen(
                onLoginSuccess = {
                    // الانتقال إلى الصفحة الرئيسية وحذف تاريخ التنقل
                    navController.navigate(Routes.HOME) {
                        popUpTo(Routes.LOGIN) { inclusive = true }
                    }
                },
                onNavigateToRegister = {
                    navController.navigate(Routes.REGISTER)
                }
            )
        }
        
        // شاشة التسجيل
        composable(Routes.REGISTER) {
            RegisterScreen(
                onRegisterSuccess = {
                    // الانتقال إلى الصفحة الرئيسية وحذف تاريخ التنقل
                    navController.navigate(Routes.HOME) {
                        popUpTo(Routes.LOGIN) { inclusive = true }
                    }
                },
                onNavigateToLogin = {
                    navController.popBackStack()
                }
            )
        }
        
        // الصفحة الرئيسية
        composable(Routes.HOME) {
            HomeScreen(
                onSignOut = {
                    // الانتقال إلى شاشة تسجيل الدخول وحذف تاريخ التنقل
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.HOME) { inclusive = true }
                    }
                },
                onNavigateToFriends = {
                    navController.navigate(Routes.FRIENDS)
                },
                onNavigateToGroups = {
                    navController.navigate(Routes.LIVE_STREAM)
                },
                onNavigateToProfile = {
                    navController.navigate(Routes.PROFILE)
                }
            )
        }

        // شاشة قائمة الأصدقاء
        composable(Routes.FRIENDS) {
            FriendsScreen(
                onBackClick = {
                    navController.popBackStack()
                },
                onAddFriendClick = {
                    navController.navigate(Routes.ADD_FRIEND)
                }
            )
        }

        // شاشة إضافة صديق
        composable(Routes.ADD_FRIEND) {
            AddFriendScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }

        // شاشة البث المباشر
        composable(Routes.LIVE_STREAM) {
            LiveStreamScreen(
                onBackClick = {
                    navController.popBackStack()
                }
            )
        }

        // شاشة الملف الشخصي (الإعدادات)
        composable(Routes.PROFILE) {
            ProfileScreen(
                onBackClick = {
                    navController.popBackStack()
                },
                onSignOut = {
                    // الانتقال إلى شاشة تسجيل الدخول وحذف تاريخ التنقل
                    navController.navigate(Routes.LOGIN) {
                        popUpTo(Routes.HOME) { inclusive = true }
                    }
                }
            )
        }
    }
}
