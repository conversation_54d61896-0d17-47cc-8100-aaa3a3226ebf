{"rules": {"private-blocks": {".read": "auth !== null", ".write": "auth !== null", "$uid": {".read": "auth !== null && $uid === auth.uid", ".write": "auth !== null && $uid === auth.uid", "$blocked_uid": {".read": "auth !== null && ($uid === auth.uid || $blocked_uid === auth.uid)", ".write": "auth !== null && $uid === auth.uid"}}}, "banned-users": {".read": "auth !== null", ".write": "auth !== null && (root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || root.child('users').child(auth.uid).child('isAdmin').val() === true || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.uid === 'Q7KGiF5tRfTt7TUz2pPKpOUEa9S2')", "$uid": {".read": "auth !== null", ".write": "auth !== null && (root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || root.child('users').child(auth.uid).child('isAdmin').val() === true || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.uid === 'Q7KGiF5tRfTt7TUz2pPKpOUEa9S2')"}}, "admins": {".read": "auth !== null && (root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')", ".write": "auth !== null && (root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')"}, "users": {".read": "auth !== null", ".write": "auth !== null", "$uid": {".read": "auth !== null", ".write": "$uid === auth.uid || (root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')"}}, "friendships": {".read": "auth !== null", ".write": "auth !== null", ".indexOn": ["user1Id", "user2Id", "timestamp"], "$friendship_id": {".read": "auth !== null", ".write": "auth !== null && (data.child('user1Id').val() === auth.uid || data.child('user2Id').val() === auth.uid || !data.exists())", ".validate": "newData.hasChildren(['id', 'user1Id', 'user2Id', 'timestamp']) && newData.child('user1Id').val() !== newData.child('user2Id').val()"}}, "friend_requests": {".read": "auth !== null", ".write": "auth !== null", ".indexOn": ["fromUserId", "toUserId", "status", "timestamp"], "$request_id": {".read": "auth !== null && (data.child('fromUserId').val() === auth.uid || data.child('toUserId').val() === auth.uid)", ".write": "auth !== null && (data.child('fromUserId').val() === auth.uid || data.child('toUserId').val() === auth.uid || !data.exists())", ".validate": "newData.hasChildren(['id', 'fromUserId', 'toUserId', 'fromUserName', 'fromUserEmail', 'timestamp', 'status']) && newData.child('fromUserId').val() !== newData.child('toUserId').val()"}}, "rooms": {".read": "auth !== null", ".indexOn": ["lastMessage/author/uid"], "$room_id": {".read": "auth !== null", ".write": "!data.exists() || data.child('admins').child(auth.uid).val() == true || (auth !== null && (root.child('users').child(auth.uid).child('isAdmin').val() === true || root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || root.child('users').child(auth.uid).child('uid').val() === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')) || (data.child('isDirectMessage').val() === true && data.child('members').child(auth.uid).exists())", "admins": {".write": "auth !== null && (root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || root.child('users').child(auth.uid).child('uid').val() === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')"}, "lastMessage": {".write": "auth !== null"}, "typing": {".write": "auth !== null"}, "hasMessages": {".write": "auth !== null"}, "visibleTo": {".write": "auth !== null"}, "muted-users": {".write": "auth !== null && (root.child('users').child(auth.uid).child('isAdmin').val() === true || root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')"}}}, "messages": {".read": "auth !== null", ".write": "auth !== null", ".indexOn": ["roomId", "author/uid"], "$message_id": {".read": "auth !== null", ".write": "auth !== null"}}, "status": {".read": "auth !== null", "$user_id": {".read": "auth !== null", ".write": "$user_id === auth.uid"}}, "deleted-rooms": {".read": "auth !== null && root.child('users').child(auth.uid).child('isAdmin').val() === true", ".write": "auth !== null && (root.child('users').child(auth.uid).child('isAdmin').val() === true || root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || root.child('users').child(auth.uid).child('uid').val() === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')"}, "hidden-rooms": {".read": "auth !== null && (root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')", ".write": "auth !== null && (root.child('users').child(auth.uid).child('isAdmin').val() === true || root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1')"}, "force-logout": {".read": "auth !== null", ".write": "auth !== null && (root.child('users').child(auth.uid).child('isAdmin').val() === true || root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.uid === 'Q7KGiF5tRfTt7TUz2pPKpOUEa9S2')", "$uid": {".read": "auth !== null && $uid === auth.uid", ".write": "auth !== null && (root.child('users').child(auth.uid).child('isAdmin').val() === true || root.child('users').child(auth.uid).child('email').val() === '<EMAIL>' || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.uid === 'Q7KGiF5tRfTt7TUz2pPKpOUEa9S2')"}}, "profile": {".read": "auth !== null", "$uid": {".read": "auth !== null", ".write": "auth !== null && $uid === auth.uid"}}, "lucky-wheel": {".read": "auth !== null", ".write": "auth !== null && (auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.token.email === '<EMAIL>')", "config": {".read": "auth !== null", ".write": "auth !== null && (auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.token.email === '<EMAIL>')"}, "tasks": {".read": "auth !== null", ".write": "auth !== null && (auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.token.email === '<EMAIL>')"}, "users": {".read": "auth !== null && (auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.token.email === '<EMAIL>')", "$uid": {".read": "auth !== null && ($uid === auth.uid || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.token.email === '<EMAIL>')", ".write": "auth !== null && ($uid === auth.uid || auth.uid === 'W8Zzmzm2NYMOfnqga4DiF2Uw2Sr1' || auth.token.email === '<EMAIL>')"}}}, ".read": false, ".write": false}}