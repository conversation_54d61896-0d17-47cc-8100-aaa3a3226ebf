# دليل البدء السريع - نظام تسجيل الدخول

## 🚀 خطوات التشغيل السريع:

### 1. إعداد Firebase Console (مهم جداً!)

1. **اذهب إلى [Firebase Console](https://console.firebase.google.com/)**
2. **اختر مشروعك: `comweb22-98ea3`**
3. **فعل Authentication:**
   - اذهب إلى Authentication > Sign-in method
   - فعل "Email/Password"
   - فعل "Google" (اختياري)

### 2. تشغيل التطبيق

```bash
# في مجلد المشروع
./gradlew assembleDebug

# أو من Android Studio
# اضغط Run ▶️
```

### 3. اختبار التطبيق

1. **إنشاء حساب جديد:**
   - افتح التطبيق
   - اضغط "إنشاء حساب جديد"
   - أدخل إيميل وكلمة مرور (6 أحرف على الأقل)
   - اضغط "إنشاء الحساب"

2. **تسجيل الدخول:**
   - أدخل نفس الإيميل وكلمة المرور
   - اضغط "تسجيل الدخول"

3. **الصفحة الرئيسية:**
   - ستظهر رسالة "مرحباً [اسم المستخدم]"
   - يمكنك تسجيل الخروج من الزر في الأعلى

## 🔧 إذا واجهت مشاكل:

### مشكلة: لا يعمل تسجيل الدخول
**الحل:**
1. تأكد من تفعيل Email/Password في Firebase Console
2. تحقق من اتصال الإنترنت
3. راجع Logcat للأخطاء

### مشكلة: لا يعمل Google Sign-In
**الحل:**
1. تأكد من تفعيل Google provider في Firebase Console
2. أضف SHA-1 fingerprint في Firebase Console
3. قم بتحديث google-services.json

### مشكلة: التطبيق يتوقف
**الحل:**
1. تحقق من Logcat للأخطاء
2. تأكد من وجود ملف google-services.json
3. تأكد من تطبيق Google Services plugin

## 📋 ملفات مهمة:

- `app/google-services.json` - إعدادات Firebase
- `app/src/main/java/com/web22/myapplication/auth/AuthenticationManager.kt` - إدارة المصادقة
- `app/src/main/java/com/web22/myapplication/navigation/AppNavigation.kt` - التنقل
- `Authentication_Setup_README.md` - دليل شامل

## 🎯 الميزات الجاهزة:

✅ تسجيل دخول بالإيميل وكلمة المرور  
✅ إنشاء حساب جديد  
✅ تسجيل دخول بـ Google (يحتاج إعداد إضافي)  
✅ تسجيل خروج  
✅ صفحة رئيسية بترحيب شخصي  
✅ واجهات عربية جميلة  
✅ معالجة الأخطاء  

## 📞 للمساعدة:

إذا واجهت أي مشاكل، تحقق من:
1. Firebase Console settings
2. Logcat في Android Studio
3. ملف `Authentication_Setup_README.md` للتفاصيل الكاملة

**نظام تسجيل الدخول جاهز للاستخدام! 🎉**
