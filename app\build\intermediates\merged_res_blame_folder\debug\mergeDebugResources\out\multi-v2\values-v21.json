{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-63:/values-v21/values-v21.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,13", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,864,1036", "endLines": "2,3,4,5,6,7,8,9,12,17", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1031,1383"}, "to": {"startLines": "2,3,4,5,6,7,8,9,18,21", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,173,237,304,368,484,610,736,1357,1529", "endLines": "2,3,4,5,6,7,8,9,20,25", "endColumns": "117,63,66,63,115,125,125,127,12,12", "endOffsets": "168,232,299,363,479,605,731,859,1524,1876"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b56453fc745a8c0d03f83bec51002d5e\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values-v21\\values-v21.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "9", "endColumns": "12", "endOffsets": "543"}, "to": {"startLines": "10", "startColumns": "4", "startOffsets": "864", "endLines": "17", "endColumns": "12", "endOffsets": "1352"}}]}]}