package com.toika.netwok

import android.app.Application
import com.google.firebase.FirebaseApp
import com.google.firebase.analytics.FirebaseAnalytics
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.FirebaseDatabase
import com.google.firebase.storage.FirebaseStorage

class MyApplication : Application() {
    
    override fun onCreate() {
        super.onCreate()
        
        // Initialize Firebase
        FirebaseApp.initializeApp(this)

        // Enable Firebase Analytics
        FirebaseAnalytics.getInstance(this)

        // Initialize Firebase Auth
        FirebaseAuth.getInstance()

        // Enable Firebase Realtime Database offline persistence
        FirebaseDatabase.getInstance().setPersistenceEnabled(true)

        // Get Firebase Storage reference
        FirebaseStorage.getInstance()
    }
}
