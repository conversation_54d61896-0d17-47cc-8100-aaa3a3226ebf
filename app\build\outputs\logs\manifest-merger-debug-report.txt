-- Merging decision tree log ---
manifest
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml:2:1-50:12
INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml:2:1-50:12
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fecf2c807995b272b505dedc8b7a296b\transformed\navigation-common-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108a50cab81f5504821575488c5acb51\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9df939248a7f810960e9160603f848f1\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b00b399fc8fdde1243e34606bb1b3da\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ece1078cfd035bd4ae37a1ee405726b\transformed\navigation-compose-2.8.4\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34214429150def93f2b30cbf2c1470e7\transformed\camera-video-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8958ab760b9493832dc38f3c8c6661e\transformed\camera-view-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\691b52cf043e2389afff4eff0f1234fe\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b87011a461135d7f02139f54eccc309\transformed\camera-core-1.3.1\AndroidManifest.xml:17:1-36:12
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:17:1-75:12
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:15:1-39:12
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:15:1-38:12
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e71863d474cbeda869e988a1e7d78c0\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:17:1-44:12
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:17:1-40:12
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\549dfb7dd035cf2830ebdc27ba100054\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd9b1e9b5252b2b0a7803dbf76bd3503\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19a80697de001540b855c2b5931c366a\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d93ba6e8564e95d1b2792b0d0134d531\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:17:1-46:12
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:17:1-43:12
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5068a91375ef0c94f5b2855b82b5c4f2\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2c6695d6c1622be1b17400de9a7326d\transformed\integrity-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a1a27fe342d9a773c4fdd33326a329\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:2:1-10:12
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:15:1-34:12
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:2:1-24:12
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:2:1-18:12
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0221ad92c10ebef58eada0d645c11629\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:15:1-23:12
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:17:1-32:12
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43305a13caa93653995345664b1d8bf8\transformed\play-services-fido-20.1.0\AndroidManifest.xml:2:1-10:12
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8da29e7eccb1a8725377c96f852ddc\transformed\material3-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e099880564deb176147b2205fdc84816\transformed\foundation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1988aa7453e8f8ba6d4b7a0a9a91b75d\transformed\foundation-layout-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\316fe0558a53da49592b1654cfc19f74\transformed\animation-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b995510614e260fc5fe467c0606dc17\transformed\ui-tooling-data-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47e49ab63b3b04d25e306c183aeb8d7a\transformed\ui-unit-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b83557fec76eed87a49eee1ac5848235\transformed\ui-geometry-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab925b8a5a84693177f5bec13a038d8\transformed\ui-tooling-preview-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ed08fb5c1a3a9710644dd973e2fb06\transformed\ui-util-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70353f590e52128fc387aa72c6085aaa\transformed\ui-text-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f628f2f971a3d94a816342e8948e9fcc\transformed\ui-graphics-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd826d93e366177cbd4c832fa829f6e\transformed\material-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9980422a7baf6d57a5f8e57fb176b1db\transformed\material-ripple-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17c69e52f939d729b3a73437acc3b04c\transformed\animation-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da9eca42d33563843ca556c4766e0630\transformed\activity-ktx-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1544f1b139521f791780ab7f198bbeeb\transformed\activity-1.8.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d4bf3a0dad295a811bc21929e3663d4\transformed\activity-compose-1.8.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d20f92b46c91933d45419fe55d06a008\transformed\material-icons-core-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46ff11bf9275c6b1f59824f1dcf6a348\transformed\play-services-stats-17.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c78aae232403cc65a271ee7c90d26b1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\273a7df6dcde50251dc3ac7c0bfbddff\transformed\loader-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09ab614e376350ca0889dc2dbf11206f\transformed\browser-1.4.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\148dc8c4b5b1366d764685159800372a\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7494369eca28e761a3742b866990c6e1\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0829b58b9ebc59694a91988f12dd88f\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a025e9499ea55015726479be46ed1bd\transformed\autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d639670c7d6ba137857ab99511f0816\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1767a88c5501ffc3a8a0d1d559eac78f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b08c108d84fa4b84d4c9e75d2ee76286\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53449f75b7aaa86d9c0df9f11c5071f9\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a374800de6b93a3510af7d1e0db96c\transformed\graphics-path-1.0.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2cdf0205b6d494f5c454474d997a0c7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95ce289665a5e541eaeba8f16e8fc675\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\957ace9b52ef5d190a8c96df423c29f0\transformed\core-ktx-1.13.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9683d20a3da236e35b8cc02cdc74653a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c5d551a6b0c87ae8b7dd48bec154e96\transformed\savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8ccec4ea25acc3999a056cde6de79bc\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2647642c7a90f8fe9c27a90510e85791\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b5eeac298185bc52fcc214e38566d9\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e23b3a390110663bfe438c0600190436\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\520622b0edae59466318f8730d724c96\transformed\lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16b5f86ffb1f7128a3cf8be8486e8984\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60c06cff5f4e82e7034d2c60ca8d1a63\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee1f439662ff1e0d7721fce5a696f7f2\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32a9d52d969bf02783b498cc9d8e5a\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b4a44dc8f1c4481a868a6141be3e196\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2adb1a9a11a67aa25c3d67eeed28082c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f43be022de2035b39234c04c60821b4e\transformed\ui-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d28077fe58b764e3b557e822a3aef3e9\transformed\runtime-saveable-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1f20a8188c88b9c878853f4f4f26720\transformed\runtime-release\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3caadb5b0d7f25b6ea3644be56f4d5da\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:15:1-19:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2648c4397f32e943027c5fa27ab4507c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8d05de82339cece08a4ee87c0ad6cb9\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:17:1-30:12
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6076cf973aefb766239a71611c6b1a96\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f177f4e74cbad48f4c4f00cd4b4613f\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64ef9d114ab110ad0973e98d0ce3706c\transformed\fragment-1.5.7\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b1d0cf350f3b5e90e6f29c5486582ae\transformed\googleid-1.1.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd7cad93733d6b01d2d4f72b755cd421\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7805eaa8a00b4460e6c10c2437a05ab\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\719aa13de07833277cf8f8b07a0382be\transformed\exifinterface-1.3.2\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39581b196ec25730e467d570b8e42570\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92620289196ff5d6ec529210e038dddc\transformed\firebase-components-18.0.0\AndroidManifest.xml:15:1-20:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57323265802a85121977e94d4bbc71e8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ea7dec2636b03c39a982d205e613184\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6e5cc3ed245995ab554a4532e625c0\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88065eff8e2b76d3fd56677ebacf58db\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55ea7875380f1f377c90c73a3837f9e6\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e01804c4f250705dd3d5d0bfed3c5af9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e7281fad7fc40b756d6d3b31eab71a\transformed\tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:2:1-21:12
	package
		INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:5:5-67
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:25:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:24:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:22:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:7:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:23:5-67
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:6:5-79
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:23:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:8:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:7:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:24:5-79
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:6:22-76
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:9:5-71
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:9:22-68
uses-permission#android.permission.MODIFY_AUDIO_SETTINGS
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:10:5-80
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:10:22-77
uses-permission#android.permission.WAKE_LOCK
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:11:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:24:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:25:5-68
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:11:22-65
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:12:5-77
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:12:22-74
uses-permission#android.permission.FOREGROUND_SERVICE_MEDIA_PROJECTION
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:13:5-94
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:13:22-91
uses-permission#android.permission.SYSTEM_ALERT_WINDOW
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:14:5-78
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:14:22-75
uses-feature#android.hardware.microphone
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:17:5-88
	android:required
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:17:62-85
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:17:19-61
application
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:19:5-48:19
INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml:19:5-48:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b87011a461135d7f02139f54eccc309\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b87011a461135d7f02139f54eccc309\transformed\camera-core-1.3.1\AndroidManifest.xml:23:5-34:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:28:5-73:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:26:5-37:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:25:5-36:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:23:5-42:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:22:5-38:19
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d93ba6e8564e95d1b2792b0d0134d531\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d93ba6e8564e95d1b2792b0d0134d531\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:28:5-44:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:29:5-41:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5068a91375ef0c94f5b2855b82b5c4f2\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5068a91375ef0c94f5b2855b82b5c4f2\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2c6695d6c1622be1b17400de9a7326d\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2c6695d6c1622be1b17400de9a7326d\transformed\integrity-1.3.0\AndroidManifest.xml:5:5-6:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a1a27fe342d9a773c4fdd33326a329\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a1a27fe342d9a773c4fdd33326a329\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:21:5-32:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:11:5-22:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:8:5-16:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:22:5-39:19
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0221ad92c10ebef58eada0d645c11629\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0221ad92c10ebef58eada0d645c11629\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:21:5-20
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:29:5-30:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43305a13caa93653995345664b1d8bf8\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43305a13caa93653995345664b1d8bf8\transformed\play-services-fido-20.1.0\AndroidManifest.xml:7:5-8:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:22:5-26:19
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46ff11bf9275c6b1f59824f1dcf6a348\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46ff11bf9275c6b1f59824f1dcf6a348\transformed\play-services-stats-17.0.2\AndroidManifest.xml:7:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:23:5-33:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2648c4397f32e943027c5fa27ab4507c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2648c4397f32e943027c5fa27ab4507c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8d05de82339cece08a4ee87c0ad6cb9\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8d05de82339cece08a4ee87c0ad6cb9\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6076cf973aefb766239a71611c6b1a96\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6076cf973aefb766239a71611c6b1a96\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f177f4e74cbad48f4c4f00cd4b4613f\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f177f4e74cbad48f4c4f00cd4b4613f\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:22:5-23:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7805eaa8a00b4460e6c10c2437a05ab\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7805eaa8a00b4460e6c10c2437a05ab\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e01804c4f250705dd3d5d0bfed3c5af9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e01804c4f250705dd3d5d0bfed3c5af9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:11:5-19:19
	android:extractNativeLibs
		INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:27:9-35
	android:label
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:25:9-41
	android:fullBackupContent
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:23:9-54
	android:roundIcon
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:26:9-54
	tools:targetApi
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:29:9-29
	android:icon
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:24:9-43
	android:allowBackup
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:21:9-35
	android:theme
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:28:9-43
	android:dataExtractionRules
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:22:9-65
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:20:9-38
activity#com.toika.netwok.MainActivity
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:30:9-40:20
	android:label
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:33:13-45
	android:exported
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:34:13-47
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:31:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:35:13-39:29
action#android.intent.action.MAIN
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:36:17-69
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:36:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:38:17-77
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:38:27-74
service#com.toika.netwok.streaming.ScreenCaptureService
ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:43:9-47:63
	android:enabled
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:45:13-35
	android:exported
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:46:13-37
	android:foregroundServiceType
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:47:13-60
	android:name
		ADDED from D:\kotln project\app\src\main\AndroidManifest.xml:44:13-59
uses-sdk
INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml
INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fecf2c807995b272b505dedc8b7a296b\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fecf2c807995b272b505dedc8b7a296b\transformed\navigation-common-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108a50cab81f5504821575488c5acb51\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-runtime:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\108a50cab81f5504821575488c5acb51\transformed\navigation-runtime-2.8.4\AndroidManifest.xml:20:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9df939248a7f810960e9160603f848f1\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-common-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9df939248a7f810960e9160603f848f1\transformed\navigation-common-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b00b399fc8fdde1243e34606bb1b3da\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-runtime-ktx:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0b00b399fc8fdde1243e34606bb1b3da\transformed\navigation-runtime-ktx-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ece1078cfd035bd4ae37a1ee405726b\transformed\navigation-compose-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.navigation:navigation-compose:2.8.4] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1ece1078cfd035bd4ae37a1ee405726b\transformed\navigation-compose-2.8.4\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34214429150def93f2b30cbf2c1470e7\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-video:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\34214429150def93f2b30cbf2c1470e7\transformed\camera-video-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8958ab760b9493832dc38f3c8c6661e\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-view:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c8958ab760b9493832dc38f3c8c6661e\transformed\camera-view-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\691b52cf043e2389afff4eff0f1234fe\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-lifecycle:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\691b52cf043e2389afff4eff0f1234fe\transformed\camera-lifecycle-1.3.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b87011a461135d7f02139f54eccc309\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b87011a461135d7f02139f54eccc309\transformed\camera-core-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:21:5-23:151
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e71863d474cbeda869e988a1e7d78c0\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2e71863d474cbeda869e988a1e7d78c0\transformed\credentials-1.2.0-rc01\AndroidManifest.xml:5:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\549dfb7dd035cf2830ebdc27ba100054\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\549dfb7dd035cf2830ebdc27ba100054\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd9b1e9b5252b2b0a7803dbf76bd3503\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-api-phone:18.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\fd9b1e9b5252b2b0a7803dbf76bd3503\transformed\play-services-auth-api-phone-18.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19a80697de001540b855c2b5931c366a\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-auth-base:18.0.10] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\19a80697de001540b855c2b5931c366a\transformed\play-services-auth-base-18.0.10\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d93ba6e8564e95d1b2792b0d0134d531\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-analytics:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d93ba6e8564e95d1b2792b0d0134d531\transformed\firebase-analytics-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5068a91375ef0c94f5b2855b82b5c4f2\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5068a91375ef0c94f5b2855b82b5c4f2\transformed\play-services-measurement-sdk-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2c6695d6c1622be1b17400de9a7326d\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.android.play:integrity:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2c6695d6c1622be1b17400de9a7326d\transformed\integrity-1.3.0\AndroidManifest.xml:4:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a1a27fe342d9a773c4fdd33326a329\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-auth-interop:20.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\38a1a27fe342d9a773c4fdd33326a329\transformed\firebase-auth-interop-20.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:19:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0221ad92c10ebef58eada0d645c11629\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-appcheck-interop:17.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\0221ad92c10ebef58eada0d645c11629\transformed\firebase-appcheck-interop-17.1.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43305a13caa93653995345664b1d8bf8\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-fido:20.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\43305a13caa93653995345664b1d8bf8\transformed\play-services-fido-20.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8da29e7eccb1a8725377c96f852ddc\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3-android:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae8da29e7eccb1a8725377c96f852ddc\transformed\material3-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e099880564deb176147b2205fdc84816\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e099880564deb176147b2205fdc84816\transformed\foundation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1988aa7453e8f8ba6d4b7a0a9a91b75d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.foundation:foundation-layout-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1988aa7453e8f8ba6d4b7a0a9a91b75d\transformed\foundation-layout-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\316fe0558a53da49592b1654cfc19f74\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-core-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\316fe0558a53da49592b1654cfc19f74\transformed\animation-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b995510614e260fc5fe467c0606dc17\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-data-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6b995510614e260fc5fe467c0606dc17\transformed\ui-tooling-data-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47e49ab63b3b04d25e306c183aeb8d7a\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-unit-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\47e49ab63b3b04d25e306c183aeb8d7a\transformed\ui-unit-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b83557fec76eed87a49eee1ac5848235\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-geometry-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b83557fec76eed87a49eee1ac5848235\transformed\ui-geometry-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab925b8a5a84693177f5bec13a038d8\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\5ab925b8a5a84693177f5bec13a038d8\transformed\ui-tooling-preview-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ed08fb5c1a3a9710644dd973e2fb06\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-util-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a1ed08fb5c1a3a9710644dd973e2fb06\transformed\ui-util-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70353f590e52128fc387aa72c6085aaa\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-text-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\70353f590e52128fc387aa72c6085aaa\transformed\ui-text-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f628f2f971a3d94a816342e8948e9fcc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-graphics-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f628f2f971a3d94a816342e8948e9fcc\transformed\ui-graphics-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd826d93e366177cbd4c832fa829f6e\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2bd826d93e366177cbd4c832fa829f6e\transformed\material-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9980422a7baf6d57a5f8e57fb176b1db\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-ripple-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9980422a7baf6d57a5f8e57fb176b1db\transformed\material-ripple-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17c69e52f939d729b3a73437acc3b04c\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.animation:animation-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\17c69e52f939d729b3a73437acc3b04c\transformed\animation-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da9eca42d33563843ca556c4766e0630\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da9eca42d33563843ca556c4766e0630\transformed\activity-ktx-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1544f1b139521f791780ab7f198bbeeb\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1544f1b139521f791780ab7f198bbeeb\transformed\activity-1.8.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d4bf3a0dad295a811bc21929e3663d4\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-compose:1.8.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d4bf3a0dad295a811bc21929e3663d4\transformed\activity-compose-1.8.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d20f92b46c91933d45419fe55d06a008\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.material:material-icons-core-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d20f92b46c91933d45419fe55d06a008\transformed\material-icons-core-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46ff11bf9275c6b1f59824f1dcf6a348\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-stats:17.0.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\46ff11bf9275c6b1f59824f1dcf6a348\transformed\play-services-stats-17.0.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c78aae232403cc65a271ee7c90d26b1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c78aae232403cc65a271ee7c90d26b1\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\273a7df6dcde50251dc3ac7c0bfbddff\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.loader:loader:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\273a7df6dcde50251dc3ac7c0bfbddff\transformed\loader-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09ab614e376350ca0889dc2dbf11206f\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.browser:browser:1.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\09ab614e376350ca0889dc2dbf11206f\transformed\browser-1.4.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\148dc8c4b5b1366d764685159800372a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\148dc8c4b5b1366d764685159800372a\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7494369eca28e761a3742b866990c6e1\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7494369eca28e761a3742b866990c6e1\transformed\appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0829b58b9ebc59694a91988f12dd88f\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0829b58b9ebc59694a91988f12dd88f\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a025e9499ea55015726479be46ed1bd\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a025e9499ea55015726479be46ed1bd\transformed\autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d639670c7d6ba137857ab99511f0816\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3d639670c7d6ba137857ab99511f0816\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1767a88c5501ffc3a8a0d1d559eac78f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1767a88c5501ffc3a8a0d1d559eac78f\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b08c108d84fa4b84d4c9e75d2ee76286\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b08c108d84fa4b84d4c9e75d2ee76286\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53449f75b7aaa86d9c0df9f11c5071f9\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.firebase:firebase-database-collection:18.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53449f75b7aaa86d9c0df9f11c5071f9\transformed\firebase-database-collection-18.0.1\AndroidManifest.xml:5:5-7:41
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a374800de6b93a3510af7d1e0db96c\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.graphics:graphics-path:1.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44a374800de6b93a3510af7d1e0db96c\transformed\graphics-path-1.0.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2cdf0205b6d494f5c454474d997a0c7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e2cdf0205b6d494f5c454474d997a0c7\transformed\customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95ce289665a5e541eaeba8f16e8fc675\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\95ce289665a5e541eaeba8f16e8fc675\transformed\ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\957ace9b52ef5d190a8c96df423c29f0\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\957ace9b52ef5d190a8c96df423c29f0\transformed\core-ktx-1.13.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9683d20a3da236e35b8cc02cdc74653a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9683d20a3da236e35b8cc02cdc74653a\transformed\savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c5d551a6b0c87ae8b7dd48bec154e96\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9c5d551a6b0c87ae8b7dd48bec154e96\transformed\savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8ccec4ea25acc3999a056cde6de79bc\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e8ccec4ea25acc3999a056cde6de79bc\transformed\lifecycle-viewmodel-2.8.3\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2647642c7a90f8fe9c27a90510e85791\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2647642c7a90f8fe9c27a90510e85791\transformed\lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b5eeac298185bc52fcc214e38566d9\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\01b5eeac298185bc52fcc214e38566d9\transformed\lifecycle-livedata-core-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e23b3a390110663bfe438c0600190436\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e23b3a390110663bfe438c0600190436\transformed\lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\520622b0edae59466318f8730d724c96\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\520622b0edae59466318f8730d724c96\transformed\lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16b5f86ffb1f7128a3cf8be8486e8984\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\16b5f86ffb1f7128a3cf8be8486e8984\transformed\lifecycle-viewmodel-savedstate-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60c06cff5f4e82e7034d2c60ca8d1a63\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\60c06cff5f4e82e7034d2c60ca8d1a63\transformed\lifecycle-viewmodel-ktx-2.8.3\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee1f439662ff1e0d7721fce5a696f7f2\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ee1f439662ff1e0d7721fce5a696f7f2\transformed\lifecycle-livedata-core-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32a9d52d969bf02783b498cc9d8e5a\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ae32a9d52d969bf02783b498cc9d8e5a\transformed\lifecycle-livedata-2.8.3\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b4a44dc8f1c4481a868a6141be3e196\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9b4a44dc8f1c4481a868a6141be3e196\transformed\lifecycle-runtime-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2adb1a9a11a67aa25c3d67eeed28082c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-compose-android:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2adb1a9a11a67aa25c3d67eeed28082c\transformed\lifecycle-viewmodel-compose-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f43be022de2035b39234c04c60821b4e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.ui:ui-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f43be022de2035b39234c04c60821b4e\transformed\ui-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d28077fe58b764e3b557e822a3aef3e9\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-saveable-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d28077fe58b764e3b557e822a3aef3e9\transformed\runtime-saveable-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1f20a8188c88b9c878853f4f4f26720\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.compose.runtime:runtime-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b1f20a8188c88b9c878853f4f4f26720\transformed\runtime-release\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3caadb5b0d7f25b6ea3644be56f4d5da\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.firebase:firebase-installations-interop:17.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\3caadb5b0d7f25b6ea3644be56f4d5da\transformed\firebase-installations-interop-17.1.1\AndroidManifest.xml:17:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2648c4397f32e943027c5fa27ab4507c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2648c4397f32e943027c5fa27ab4507c\transformed\play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8d05de82339cece08a4ee87c0ad6cb9\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8d05de82339cece08a4ee87c0ad6cb9\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6076cf973aefb766239a71611c6b1a96\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6076cf973aefb766239a71611c6b1a96\transformed\play-services-measurement-base-22.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f177f4e74cbad48f4c4f00cd4b4613f\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-measurement-connector:19.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2f177f4e74cbad48f4c4f00cd4b4613f\transformed\firebase-measurement-connector-19.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64ef9d114ab110ad0973e98d0ce3706c\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.7] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\64ef9d114ab110ad0973e98d0ce3706c\transformed\fragment-1.5.7\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b1d0cf350f3b5e90e6f29c5486582ae\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.libraries.identity.googleid:googleid:1.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2b1d0cf350f3b5e90e6f29c5486582ae\transformed\googleid-1.1.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd7cad93733d6b01d2d4f72b755cd421\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\cd7cad93733d6b01d2d4f72b755cd421\transformed\annotation-experimental-1.4.1\AndroidManifest.xml:5:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7805eaa8a00b4460e6c10c2437a05ab\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f7805eaa8a00b4460e6c10c2437a05ab\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\719aa13de07833277cf8f8b07a0382be\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\719aa13de07833277cf8f8b07a0382be\transformed\exifinterface-1.3.2\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39581b196ec25730e467d570b8e42570\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39581b196ec25730e467d570b8e42570\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92620289196ff5d6ec529210e038dddc\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [com.google.firebase:firebase-components:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\92620289196ff5d6ec529210e038dddc\transformed\firebase-components-18.0.0\AndroidManifest.xml:18:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57323265802a85121977e94d4bbc71e8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\57323265802a85121977e94d4bbc71e8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ea7dec2636b03c39a982d205e613184\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\2ea7dec2636b03c39a982d205e613184\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6e5cc3ed245995ab554a4532e625c0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ac6e5cc3ed245995ab554a4532e625c0\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88065eff8e2b76d3fd56677ebacf58db\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\88065eff8e2b76d3fd56677ebacf58db\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55ea7875380f1f377c90c73a3837f9e6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\55ea7875380f1f377c90c73a3837f9e6\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e01804c4f250705dd3d5d0bfed3c5af9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e01804c4f250705dd3d5d0bfed3c5af9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e7281fad7fc40b756d6d3b31eab71a\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\82e7281fad7fc40b756d6d3b31eab71a\transformed\tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:23:9-148
	android:targetSdkVersion
		INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\kotln project\app\src\main\AndroidManifest.xml
service#androidx.camera.core.impl.MetadataHolderService
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b87011a461135d7f02139f54eccc309\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
MERGED from [androidx.camera:camera-core:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7b87011a461135d7f02139f54eccc309\transformed\camera-core-1.3.1\AndroidManifest.xml:29:9-33:78
	tools:node
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:29:13-31
	android:enabled
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
	android:exported
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:28:13-75
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
meta-data#androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER
ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
	android:value
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
	android:name
		ADDED from [androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4e550dee190dd40673153a977b091e27\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
activity#com.google.firebase.auth.internal.GenericIdpActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:29:9-46:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:31:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:33:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:32:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:34:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:30:13-80
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:genericidp
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:35:13-45:29
action#android.intent.action.VIEW
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:17-69
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:36:25-66
category#android.intent.category.DEFAULT
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:17-76
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:38:27-73
category#android.intent.category.BROWSABLE
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:17-78
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:39:27-75
data
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:41:17-44:51
	android:path
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:43:21-37
	android:host
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:42:21-49
	android:scheme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:44:21-48
activity#com.google.firebase.auth.internal.RecaptchaActivity
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:47:9-64:20
	android:excludeFromRecents
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:49:13-46
	android:launchMode
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:51:13-44
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:50:13-36
	android:theme
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:52:13-72
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:48:13-79
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:firebase.auth+data:path:/+data:scheme:recaptcha
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:53:13-63:29
service#com.google.firebase.components.ComponentDiscoveryService
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:66:9-72:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:27:9-36:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:26:9-35:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:34:9-40:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:22:9-31:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:12:9-21:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:9:9-15:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
MERGED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:30:9-38:19
	android:exported
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:68:13-37
	tools:targetApi
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:34:13-32
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:32:13-43
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:67:13-84
meta-data#com.google.firebase.components:com.google.firebase.auth.FirebaseAuthRegistrar
ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:69:13-71:85
	android:value
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:71:17-82
	android:name
		ADDED from [com.google.firebase:firebase-auth:23.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\44d444153606f9abb3d81bec63058bed\transformed\firebase-auth-23.1.0\AndroidManifest.xml:70:17-109
meta-data#com.google.firebase.components:com.google.firebase.storage.FirebaseStorageKtxRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:30:13-32:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:32:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:31:17-118
meta-data#com.google.firebase.components:com.google.firebase.storage.StorageRegistrar
ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:33:13-35:85
	android:value
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:35:17-82
	android:name
		ADDED from [com.google.firebase:firebase-storage:21.0.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36c32fd037decb811bf3d8c90ba17b1d\transformed\firebase-storage-21.0.1\AndroidManifest.xml:34:17-107
meta-data#com.google.firebase.components:com.google.firebase.database.FirebaseDatabaseKtxRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:29:13-31:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:31:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:30:17-120
meta-data#com.google.firebase.components:com.google.firebase.database.DatabaseRegistrar
ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:32:13-34:85
	android:value
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:34:17-82
	android:name
		ADDED from [com.google.firebase:firebase-database:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\72b3457979ef6f179764f308f9a3ce31\transformed\firebase-database-21.0.0\AndroidManifest.xml:33:17-109
service#androidx.credentials.playservices.CredentialProviderMetadataHolder
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:24:9-32:19
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:26:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:28:13-60
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:25:13-94
meta-data#androidx.credentials.CREDENTIAL_PROVIDER_KEY
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:29:13-31:104
	android:value
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:31:17-101
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:30:17-76
activity#androidx.credentials.playservices.HiddenActivity
ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:34:9-41:20
	android:fitsSystemWindows
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:39:13-45
	android:enabled
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:38:13-37
	android:configChanges
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:36:13-87
	android:theme
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:40:13-48
	android:name
		ADDED from [androidx.credentials:credentials-play-services-auth:1.2.0-rc01] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b56453fc745a8c0d03f83bec51002d5e\transformed\credentials-play-services-auth-1.2.0-rc01\AndroidManifest.xml:35:13-76
activity#com.google.android.gms.auth.api.signin.internal.SignInHubActivity
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:23:9-27:75
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:25:13-46
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:26:13-37
	android:theme
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:27:13-72
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:24:13-93
service#com.google.android.gms.auth.api.signin.RevocationBoundService
ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:33:9-37:51
	android:exported
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:35:13-36
	android:visibleToInstantApps
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:37:13-48
	android:permission
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:36:13-107
	android:name
		ADDED from [com.google.android.gms:play-services-auth:21.2.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4c900b5b67f1531143ff194f1784dd7d\transformed\play-services-auth-21.2.0\AndroidManifest.xml:34:13-89
uses-permission#com.google.android.finsky.permission.BIND_GET_INSTALL_REFERRER_SERVICE
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:26:5-110
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:26:22-107
receiver#com.google.android.gms.measurement.AppMeasurementReceiver
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:29:9-33:20
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:31:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:32:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:30:13-85
service#com.google.android.gms.measurement.AppMeasurementService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:38:13-37
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:36:13-84
service#com.google.android.gms.measurement.AppMeasurementJobService
ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:39:9-43:72
	android:enabled
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:41:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:42:13-37
	android:permission
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:43:13-69
	android:name
		ADDED from [com.google.android.gms:play-services-measurement:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\759f8fca5b0831853038ab7b4601df91\transformed\play-services-measurement-22.1.2\AndroidManifest.xml:40:13-87
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-measurement-impl:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d0ba99073fa7a4e1416e2fe7197cb9ce\transformed\play-services-measurement-impl-22.1.2\AndroidManifest.xml:27:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8d05de82339cece08a4ee87c0ad6cb9\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d8d05de82339cece08a4ee87c0ad6cb9\transformed\play-services-ads-identifier-18.0.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:25:22-76
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:27:5-88
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:26:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\1d9714ddf9be9ccef4d4c88d3747e117\transformed\play-services-measurement-sdk-api-22.1.2\AndroidManifest.xml:28:5-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:27:22-79
property#android.adservices.AD_SERVICES_CONFIG
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:30:9-32:61
	android:resource
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:32:13-58
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:31:13-65
meta-data#com.google.firebase.components:com.google.firebase.analytics.connector.internal.AnalyticsConnectorRegistrar
ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:37:13-39:85
	android:value
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:39:17-82
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-api:22.1.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4528b736ce971a2ad54d6a99a7fac471\transformed\play-services-measurement-api-22.1.2\AndroidManifest.xml:38:17-139
uses-permission#com.google.android.providers.gsf.permission.READ_GSERVICES
ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:5-98
	android:name
		ADDED from [com.google.android.recaptcha:recaptcha:18.5.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7415dc446bd5e1844f4a3451940f1ba3\transformed\recaptcha-18.5.1\AndroidManifest.xml:9:22-95
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckKtxRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:25:13-27:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:27:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:26:17-120
meta-data#com.google.firebase.components:com.google.firebase.appcheck.FirebaseAppCheckRegistrar
ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:28:13-30:85
	android:value
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:30:17-82
	android:name
		ADDED from [com.google.firebase:firebase-appcheck:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4da16e95c586b1b2e86670338a1866c4\transformed\firebase-appcheck-18.0.0\AndroidManifest.xml:29:17-117
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsKtxRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:15:13-17:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:17:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:16:17-130
meta-data#com.google.firebase.components:com.google.firebase.installations.FirebaseInstallationsRegistrar
ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:18:13-20:85
	android:value
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:20:17-82
	android:name
		ADDED from [com.google.firebase:firebase-installations:18.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bad78e792d2e8d5bf71e3ba55a6dae04\transformed\firebase-installations-18.0.0\AndroidManifest.xml:19:17-127
meta-data#com.google.firebase.components:com.google.firebase.ktx.FirebaseCommonLegacyRegistrar
ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:12:13-14:85
	android:value
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:14:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common-ktx:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a61ded5de719a0b3d5c0bbec6b14539b\transformed\firebase-common-ktx-21.0.0\AndroidManifest.xml:13:17-116
provider#com.google.firebase.provider.FirebaseInitProvider
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:23:9-28:39
	android:authorities
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:25:13-72
	android:exported
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:27:13-37
	android:directBootAware
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:26:13-43
	android:initOrder
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:28:13-36
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:24:13-77
meta-data#com.google.firebase.components:com.google.firebase.FirebaseCommonKtxRegistrar
ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:35:13-37:85
	android:value
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:37:17-82
	android:name
		ADDED from [com.google.firebase:firebase-common:21.0.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e0c880f9d656a863cb1f25599a8dadaf\transformed\firebase-common-21.0.0\AndroidManifest.xml:36:17-109
activity#androidx.activity.ComponentActivity
ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-test-manifest:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c70fcab49a1032cb5e91b64edd3a1db3\transformed\ui-test-manifest-1.7.2\AndroidManifest.xml:24:13-63
activity#androidx.compose.ui.tooling.PreviewActivity
ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
	android:exported
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
	android:name
		ADDED from [androidx.compose.ui:ui-tooling-android:1.7.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\90ed6ddba62f5b8d0576bb9e8c8c0674\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e01804c4f250705dd3d5d0bfed3c5af9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\e01804c4f250705dd3d5d0bfed3c5af9\transformed\startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\36dc41f43b9b320c8deb7b72260046be\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7661eb1439e484e98fea3778baf664e3\transformed\play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\6abe900a27f5698514cdb388da4cd4af\transformed\ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
permission#com.toika.netwok.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
uses-permission#com.toika.netwok.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4271afb7208b91aba51d66f2d79dd50a\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da131603abe9f3475c237a546883a13f\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
meta-data#com.google.android.gms.version
ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:66-119
	android:name
		ADDED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\4a7d2fcfebb99ca0b7d2351010b4b169\transformed\play-services-basement-18.4.0\AndroidManifest.xml:6:20-65
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3fbe72876876dd63fa63779581f4853\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
activity#com.google.android.play.core.common.PlayCoreDialogWrapperActivity
ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:14:9-18:65
	android:stateNotNeeded
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:17:13-42
	android:exported
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:16:13-37
	android:theme
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:18:13-62
	android:name
		ADDED from [com.google.android.play:core-common:2.0.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\da5e41b10fafadce7ba510b91b313ba3\transformed\core-common-2.0.3\AndroidManifest.xml:15:13-93
