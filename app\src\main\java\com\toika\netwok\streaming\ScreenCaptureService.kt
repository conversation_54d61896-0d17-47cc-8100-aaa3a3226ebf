package com.toika.netwok.streaming

import android.app.*
import android.content.Context
import android.content.Intent
import android.graphics.PixelFormat
import android.hardware.display.DisplayManager
import android.hardware.display.VirtualDisplay
import android.media.MediaRecorder
import android.media.projection.MediaProjection
import android.media.projection.MediaProjectionManager
import android.os.Build
import android.os.IBinder
import android.util.DisplayMetrics
import android.util.Log
import android.view.Surface
import android.view.WindowManager
import androidx.core.app.NotificationCompat
import com.toika.netwok.R
import java.io.IOException

class ScreenCaptureService : Service() {
    
    companion object {
        private const val TAG = "ScreenCaptureService"
        private const val NOTIFICATION_ID = 1001
        private const val CHANNEL_ID = "screen_capture_channel"

        const val ACTION_START_CAPTURE = "start_capture"
        const val ACTION_STOP_CAPTURE = "stop_capture"
        const val EXTRA_RESULT_CODE = "result_code"
        const val EXTRA_RESULT_DATA = "result_data"

        // Broadcast actions للإبلاغ عن الحالة
        const val ACTION_SCREEN_SHARE_STARTED = "com.toika.netwok.SCREEN_SHARE_STARTED"
        const val ACTION_SCREEN_SHARE_FAILED = "com.toika.netwok.SCREEN_SHARE_FAILED"
    }
    
    private var mediaProjection: MediaProjection? = null
    private var virtualDisplay: VirtualDisplay? = null
    private var mediaRecorder: MediaRecorder? = null
    private var isRecording = false
    private var hasStartedSuccessfully = false
    
    private lateinit var mediaProjectionManager: MediaProjectionManager
    private lateinit var windowManager: WindowManager
    private lateinit var displayMetrics: DisplayMetrics
    
    override fun onCreate() {
        super.onCreate()
        
        mediaProjectionManager = getSystemService(Context.MEDIA_PROJECTION_SERVICE) as MediaProjectionManager
        windowManager = getSystemService(Context.WINDOW_SERVICE) as WindowManager
        displayMetrics = DisplayMetrics()
        windowManager.defaultDisplay.getMetrics(displayMetrics)
        
        createNotificationChannel()
        Log.d(TAG, "ScreenCaptureService created")
    }
    
    override fun onStartCommand(intent: Intent?, flags: Int, startId: Int): Int {
        when (intent?.action) {
            ACTION_START_CAPTURE -> {
                val resultCode = intent.getIntExtra(EXTRA_RESULT_CODE, Activity.RESULT_CANCELED)
                val resultData = intent.getParcelableExtra<Intent>(EXTRA_RESULT_DATA)
                
                if (resultCode == Activity.RESULT_OK && resultData != null) {
                    startScreenCapture(resultCode, resultData)
                } else {
                    Log.e(TAG, "Invalid result code or data for screen capture")
                    stopSelf()
                }
            }
            ACTION_STOP_CAPTURE -> {
                stopScreenCapture()
                stopSelf()
            }
        }
        
        return START_NOT_STICKY
    }
    
    override fun onBind(intent: Intent?): IBinder? = null
    
    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                "البث المباشر",
                NotificationManager.IMPORTANCE_HIGH
            ).apply {
                description = "إشعارات البث المباشر ومشاركة الشاشة"
                setShowBadge(true)
                enableLights(true)
                lightColor = 0xFFFF0000.toInt() // أحمر
                enableVibration(false)
                setSound(null, null) // بدون صوت
            }

            val notificationManager = getSystemService(NotificationManager::class.java)
            notificationManager.createNotificationChannel(channel)
        }
    }
    
    private fun startScreenCapture(resultCode: Int, resultData: Intent) {
        try {
            Log.d(TAG, "Starting screen capture with resultCode: $resultCode")

            // التحقق من صحة البيانات
            if (resultCode != android.app.Activity.RESULT_OK) {
                Log.e(TAG, "Invalid result code: $resultCode")
                stopSelf()
                return
            }

            // إنشاء MediaProjection
            mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, resultData)

            if (mediaProjection == null) {
                Log.e(TAG, "Failed to create MediaProjection")
                stopSelf()
                return
            }

            // إظهار الإشعار أولاً
            startForeground(NOTIFICATION_ID, createNotification())

            // إعداد MediaRecorder
            setupMediaRecorder()

            // إنشاء VirtualDisplay
            createVirtualDisplay()

            // بدء التسجيل
            mediaRecorder?.start()
            isRecording = true
            hasStartedSuccessfully = true

            // إرسال broadcast للإبلاغ عن النجاح
            sendBroadcast(Intent(ACTION_SCREEN_SHARE_STARTED))

            Log.d(TAG, "Screen capture started successfully")

        } catch (e: Exception) {
            Log.e(TAG, "Failed to start screen capture: ${e.message}", e)

            // إظهار إشعار خطأ فقط إذا لم تنجح العملية
            if (!hasStartedSuccessfully) {
                // إرسال broadcast للإبلاغ عن الفشل
                val failureIntent = Intent(ACTION_SCREEN_SHARE_FAILED).apply {
                    putExtra("error_message", e.message)
                }
                sendBroadcast(failureIntent)

                try {
                    startForeground(NOTIFICATION_ID, createErrorNotification())
                } catch (ex: Exception) {
                    Log.e(TAG, "Failed to show error notification: ${ex.message}")
                }
            }

            stopScreenCapture()

            // إيقاف الخدمة إذا فشلت العملية
            if (!hasStartedSuccessfully) {
                stopSelf()
            }
        }
    }
    
    private fun setupMediaRecorder() {
        try {
            mediaRecorder = if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.S) {
                MediaRecorder(this)
            } else {
                @Suppress("DEPRECATION")
                MediaRecorder()
            }

            mediaRecorder?.apply {
                // إعداد مصادر الصوت والفيديو
                setAudioSource(MediaRecorder.AudioSource.MIC)
                setVideoSource(MediaRecorder.VideoSource.SURFACE)
                setOutputFormat(MediaRecorder.OutputFormat.MPEG_4)

                // إعداد ملف الإخراج
                val outputDir = externalCacheDir ?: cacheDir
                val outputFile = "${outputDir}/screen_capture_${System.currentTimeMillis()}.mp4"
                setOutputFile(outputFile)
                Log.d(TAG, "Output file: $outputFile")

                // إعداد الترميز
                setVideoEncoder(MediaRecorder.VideoEncoder.H264)
                setAudioEncoder(MediaRecorder.AudioEncoder.AAC)

                // إعداد جودة الفيديو
                val width = displayMetrics.widthPixels
                val height = displayMetrics.heightPixels
                setVideoSize(width, height)
                setVideoFrameRate(30)
                setVideoEncodingBitRate(2 * 1024 * 1024) // 2 Mbps (أقل لتجنب الأخطاء)

                Log.d(TAG, "MediaRecorder configured: ${width}x${height}")

                // تحضير MediaRecorder
                prepare()
                Log.d(TAG, "MediaRecorder prepared successfully")
            }
        } catch (e: Exception) {
            Log.e(TAG, "Failed to setup MediaRecorder: ${e.message}", e)
            throw e
        }
    }
    
    private fun createVirtualDisplay() {
        val surface: Surface = mediaRecorder?.surface ?: return
        
        virtualDisplay = mediaProjection?.createVirtualDisplay(
            "ScreenCapture",
            displayMetrics.widthPixels,
            displayMetrics.heightPixels,
            displayMetrics.densityDpi,
            DisplayManager.VIRTUAL_DISPLAY_FLAG_AUTO_MIRROR,
            surface,
            null,
            null
        )
    }
    
    private fun stopScreenCapture() {
        try {
            if (isRecording) {
                mediaRecorder?.stop()
                isRecording = false
            }
            
            mediaRecorder?.release()
            mediaRecorder = null
            
            virtualDisplay?.release()
            virtualDisplay = null
            
            mediaProjection?.stop()
            mediaProjection = null
            
            Log.d(TAG, "Screen capture stopped")
            
        } catch (e: Exception) {
            Log.e(TAG, "Error stopping screen capture: ${e.message}")
        }
    }
    
    private fun createNotification(): Notification {
        // Intent للعودة للتطبيق
        val appIntent = Intent(this, com.toika.netwok.MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val appPendingIntent = PendingIntent.getActivity(
            this, 0, appIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        // Intent لإيقاف المشاركة
        val stopIntent = Intent(this, ScreenCaptureService::class.java).apply {
            action = ACTION_STOP_CAPTURE
        }
        val stopPendingIntent = PendingIntent.getService(
            this, 1, stopIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("🔴 البث المباشر نشط")
            .setContentText("يتم مشاركة شاشتك مع المشاهدين • اضغط للعودة للتطبيق")
            .setSmallIcon(android.R.drawable.ic_media_play)
            .setColor(0xFFFF0000.toInt()) // أحمر
            .setOngoing(true)
            .setContentIntent(appPendingIntent)
            .addAction(
                android.R.drawable.ic_media_pause,
                "إيقاف البث",
                stopPendingIntent
            )
            .setStyle(NotificationCompat.BigTextStyle()
                .bigText("البث المباشر نشط الآن\nيتم مشاركة شاشتك مع المشاهدين\nاضغط هنا للعودة للتطبيق"))
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .setVisibility(NotificationCompat.VISIBILITY_PUBLIC)
            .build()
    }

    private fun createErrorNotification(): Notification {
        // Intent للعودة للتطبيق
        val appIntent = Intent(this, com.toika.netwok.MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TOP
        }
        val appPendingIntent = PendingIntent.getActivity(
            this, 0, appIntent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        return NotificationCompat.Builder(this, CHANNEL_ID)
            .setContentTitle("❌ فشل في بدء البث")
            .setContentText("حدث خطأ في مشاركة الشاشة • اضغط للعودة للتطبيق")
            .setSmallIcon(android.R.drawable.ic_dialog_alert)
            .setColor(0xFFFF0000.toInt()) // أحمر
            .setContentIntent(appPendingIntent)
            .setAutoCancel(true)
            .setPriority(NotificationCompat.PRIORITY_HIGH)
            .build()
    }

    override fun onDestroy() {
        super.onDestroy()
        stopScreenCapture()
        Log.d(TAG, "ScreenCaptureService destroyed")
    }
    
    fun isCapturing(): Boolean = isRecording
}
