{"rules": {".read": "auth != null", ".write": "auth != null", "users": {"$uid": {".read": "auth != null", ".write": "auth != null && auth.uid == $uid"}}, "friendships": {"$uid": {".read": "auth != null && auth.uid == $uid", ".write": "auth != null && auth.uid == $uid"}}, "friend_requests": {"$uid": {".read": "auth != null && auth.uid == $uid", ".write": "auth != null"}}, "live_streams": {".read": "auth != null", "$streamId": {".write": "auth != null && (auth.uid == data.child('hostId').val() || !data.exists())"}}, "stream_viewers": {"$streamId": {".read": "auth != null", "$viewerId": {".write": "auth != null && auth.uid == $viewerId"}}}, "stream_messages": {"$streamId": {".read": "auth != null", ".write": "auth != null"}}}}