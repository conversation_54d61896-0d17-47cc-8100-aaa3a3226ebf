package com.web22.myapplication.data

import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.web22.myapplication.data.models.*
import kotlinx.coroutines.channels.awaitClose
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.callbackFlow
import kotlinx.coroutines.tasks.await

class FriendsManager {
    
    private val database = FirebaseDatabase.getInstance()
    private val auth = FirebaseAuth.getInstance()
    
    companion object {
        private const val TAG = "FriendsManager"
        private const val USERS_PATH = "users"
        private const val FRIEND_REQUESTS_PATH = "friend_requests"
        private const val FRIENDSHIPS_PATH = "friendships"
    }
    
    // حفظ معلومات المستخدم في Firebase
    suspend fun saveUserToDatabase(user: User): Result<Unit> {
        return try {
            database.getReference(USERS_PATH)
                .child(user.uid)
                .setValue(user)
                .await()
            
            Log.d(TAG, "User saved successfully: ${user.uid}")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to save user: ${e.message}")
            Result.failure(e)
        }
    }
    
    // البحث عن مستخدم بـ UID
    suspend fun searchUserByUid(uid: String): Result<User?> {
        return try {
            val snapshot = database.getReference(USERS_PATH)
                .child(uid)
                .get()
                .await()
            
            val user = snapshot.getValue(User::class.java)
            Log.d(TAG, "User search result: $user")
            Result.success(user)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to search user: ${e.message}")
            Result.failure(e)
        }
    }
    
    // إرسال طلب صداقة
    suspend fun sendFriendRequest(toUserId: String, toUserName: String, toUserEmail: String): Result<Unit> {
        return try {
            val currentUser = auth.currentUser ?: return Result.failure(Exception("User not logged in"))
            
            // التحقق من عدم إرسال طلب لنفس المستخدم
            if (currentUser.uid == toUserId) {
                return Result.failure(Exception("لا يمكنك إرسال طلب صداقة لنفسك"))
            }
            
            // التحقق من وجود طلب سابق
            val existingRequest = checkExistingFriendRequest(currentUser.uid, toUserId)
            if (existingRequest) {
                return Result.failure(Exception("تم إرسال طلب صداقة مسبقاً"))
            }
            
            // التحقق من وجود صداقة
            val existingFriendship = checkExistingFriendship(currentUser.uid, toUserId)
            if (existingFriendship) {
                return Result.failure(Exception("أنتما أصدقاء بالفعل"))
            }
            
            val requestId = database.getReference(FRIEND_REQUESTS_PATH).push().key
                ?: return Result.failure(Exception("Failed to generate request ID"))
            
            val friendRequest = FriendRequest(
                id = requestId,
                fromUserId = currentUser.uid,
                toUserId = toUserId,
                fromUserName = currentUser.displayName ?: currentUser.email?.substringBefore("@") ?: "مستخدم",
                fromUserEmail = currentUser.email ?: "",
                timestamp = System.currentTimeMillis(),
                status = FriendRequestStatus.PENDING
            )
            
            database.getReference(FRIEND_REQUESTS_PATH)
                .child(requestId)
                .setValue(friendRequest)
                .await()
            
            Log.d(TAG, "Friend request sent successfully")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send friend request: ${e.message}")
            Result.failure(e)
        }
    }
    
    // التحقق من وجود طلب صداقة سابق
    private suspend fun checkExistingFriendRequest(fromUserId: String, toUserId: String): Boolean {
        return try {
            val snapshot = database.getReference(FRIEND_REQUESTS_PATH)
                .orderByChild("fromUserId")
                .equalTo(fromUserId)
                .get()
                .await()
            
            for (child in snapshot.children) {
                val request = child.getValue(FriendRequest::class.java)
                if (request?.toUserId == toUserId && request.status == FriendRequestStatus.PENDING) {
                    return true
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking existing friend request: ${e.message}")
            false
        }
    }
    
    // التحقق من وجود صداقة
    private suspend fun checkExistingFriendship(userId1: String, userId2: String): Boolean {
        return try {
            val snapshot = database.getReference(FRIENDSHIPS_PATH)
                .get()
                .await()
            
            for (child in snapshot.children) {
                val friendship = child.getValue(Friendship::class.java)
                if (friendship != null) {
                    if ((friendship.user1Id == userId1 && friendship.user2Id == userId2) ||
                        (friendship.user1Id == userId2 && friendship.user2Id == userId1)) {
                        return true
                    }
                }
            }
            false
        } catch (e: Exception) {
            Log.e(TAG, "Error checking existing friendship: ${e.message}")
            false
        }
    }
    
    // الحصول على طلبات الصداقة الواردة
    fun getIncomingFriendRequests(): Flow<List<FriendRequest>> = callbackFlow {
        try {
            val currentUser = auth.currentUser
            if (currentUser == null) {
                Log.w(TAG, "No current user, returning empty friend requests")
                trySend(emptyList())
                close()
                return@callbackFlow
            }

            val listener = database.getReference(FRIEND_REQUESTS_PATH)
                .orderByChild("toUserId")
                .equalTo(currentUser.uid)
                .addValueEventListener(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        try {
                            val requests = mutableListOf<FriendRequest>()
                            for (child in snapshot.children) {
                                try {
                                    val request = child.getValue(FriendRequest::class.java)
                                    if (request?.status == FriendRequestStatus.PENDING) {
                                        requests.add(request)
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "Error parsing friend request: ${e.message}")
                                }
                            }
                            trySend(requests)
                        } catch (e: Exception) {
                            Log.e(TAG, "Error processing friend requests: ${e.message}")
                            trySend(emptyList())
                        }
                    }

                    override fun onCancelled(error: DatabaseError) {
                        Log.e(TAG, "Failed to get friend requests: ${error.message}")
                        trySend(emptyList())
                        close()
                    }
                })

            awaitClose {
                try {
                    database.getReference(FRIEND_REQUESTS_PATH).removeEventListener(listener)
                } catch (e: Exception) {
                    Log.e(TAG, "Error removing listener: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up friend requests listener: ${e.message}")
            trySend(emptyList())
            close()
        }
    }
    
    // قبول طلب صداقة
    suspend fun acceptFriendRequest(requestId: String, fromUserId: String): Result<Unit> {
        return try {
            val currentUser = auth.currentUser ?: return Result.failure(Exception("User not logged in"))
            
            // تحديث حالة الطلب
            database.getReference(FRIEND_REQUESTS_PATH)
                .child(requestId)
                .child("status")
                .setValue(FriendRequestStatus.ACCEPTED.name)
                .await()
            
            // إنشاء صداقة
            val friendshipId = database.getReference(FRIENDSHIPS_PATH).push().key
                ?: return Result.failure(Exception("Failed to generate friendship ID"))
            
            val friendship = Friendship(
                id = friendshipId,
                user1Id = fromUserId,
                user2Id = currentUser.uid,
                timestamp = System.currentTimeMillis()
            )
            
            database.getReference(FRIENDSHIPS_PATH)
                .child(friendshipId)
                .setValue(friendship)
                .await()
            
            Log.d(TAG, "Friend request accepted successfully")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to accept friend request: ${e.message}")
            Result.failure(e)
        }
    }
    
    // رفض طلب صداقة
    suspend fun rejectFriendRequest(requestId: String): Result<Unit> {
        return try {
            database.getReference(FRIEND_REQUESTS_PATH)
                .child(requestId)
                .child("status")
                .setValue(FriendRequestStatus.REJECTED.name)
                .await()
            
            Log.d(TAG, "Friend request rejected successfully")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to reject friend request: ${e.message}")
            Result.failure(e)
        }
    }
    
    // الحصول على قائمة الأصدقاء
    fun getFriends(): Flow<List<User>> = callbackFlow {
        try {
            val currentUser = auth.currentUser
            if (currentUser == null) {
                Log.w(TAG, "No current user, returning empty friends list")
                trySend(emptyList())
                close()
                return@callbackFlow
            }

            val listener = database.getReference(FRIENDSHIPS_PATH)
                .addValueEventListener(object : ValueEventListener {
                    override fun onDataChange(snapshot: DataSnapshot) {
                        try {
                            val friendIds = mutableListOf<String>()

                            for (child in snapshot.children) {
                                try {
                                    val friendship = child.getValue(Friendship::class.java)
                                    if (friendship != null) {
                                        when {
                                            friendship.user1Id == currentUser.uid -> friendIds.add(friendship.user2Id)
                                            friendship.user2Id == currentUser.uid -> friendIds.add(friendship.user1Id)
                                        }
                                    }
                                } catch (e: Exception) {
                                    Log.e(TAG, "Error parsing friendship: ${e.message}")
                                }
                            }

                            // الحصول على معلومات الأصدقاء
                            getFriendsDetails(friendIds) { friends ->
                                trySend(friends)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error processing friendships: ${e.message}")
                            trySend(emptyList())
                        }
                    }

                    override fun onCancelled(error: DatabaseError) {
                        Log.e(TAG, "Failed to get friends: ${error.message}")
                        trySend(emptyList())
                        close()
                    }
                })

            awaitClose {
                try {
                    database.getReference(FRIENDSHIPS_PATH).removeEventListener(listener)
                } catch (e: Exception) {
                    Log.e(TAG, "Error removing friends listener: ${e.message}")
                }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error setting up friends listener: ${e.message}")
            trySend(emptyList())
            close()
        }
    }
    
    // الحصول على تفاصيل الأصدقاء
    private fun getFriendsDetails(friendIds: List<String>, callback: (List<User>) -> Unit) {
        try {
            if (friendIds.isEmpty()) {
                callback(emptyList())
                return
            }

            val friends = mutableListOf<User>()
            var completed = 0

            for (friendId in friendIds) {
                database.getReference(USERS_PATH)
                    .child(friendId)
                    .get()
                    .addOnSuccessListener { snapshot ->
                        try {
                            val user = snapshot.getValue(User::class.java)
                            if (user != null) {
                                friends.add(user)
                            }
                        } catch (e: Exception) {
                            Log.e(TAG, "Error parsing user data for $friendId: ${e.message}")
                        }
                        completed++
                        if (completed == friendIds.size) {
                            callback(friends)
                        }
                    }
                    .addOnFailureListener { exception ->
                        Log.e(TAG, "Failed to get user data for $friendId: ${exception.message}")
                        completed++
                        if (completed == friendIds.size) {
                            callback(friends)
                        }
                    }
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error in getFriendsDetails: ${e.message}")
            callback(emptyList())
        }
    }
    
    // حذف صديق
    suspend fun removeFriend(friendId: String): Result<Unit> {
        return try {
            val currentUser = auth.currentUser ?: return Result.failure(Exception("User not logged in"))
            
            // البحث عن الصداقة وحذفها
            val snapshot = database.getReference(FRIENDSHIPS_PATH)
                .get()
                .await()
            
            for (child in snapshot.children) {
                val friendship = child.getValue(Friendship::class.java)
                if (friendship != null) {
                    if ((friendship.user1Id == currentUser.uid && friendship.user2Id == friendId) ||
                        (friendship.user1Id == friendId && friendship.user2Id == currentUser.uid)) {
                        
                        child.ref.removeValue().await()
                        Log.d(TAG, "Friend removed successfully")
                        return Result.success(Unit)
                    }
                }
            }
            
            Result.failure(Exception("Friendship not found"))
        } catch (e: Exception) {
            Log.e(TAG, "Failed to remove friend: ${e.message}")
            Result.failure(e)
        }
    }
}
