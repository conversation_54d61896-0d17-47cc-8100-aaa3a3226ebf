{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-72:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7661eb1439e484e98fea3778baf664e3\\transformed\\play-services-base-18.5.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "49,50,51,52,53,54,55,56,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,2841,3113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3025,3115,3195,3285,3375,3455,3536,3616,22746,22851,23032,23157,23264,23444,23567,23683,23953,24141,24246,24427,24552,24727,24875,24938,25000,163363,172437", "endLines": "49,50,51,52,53,54,55,56,353,354,355,356,357,358,359,360,362,363,364,365,366,367,368,369,370,2853,3131", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "3110,3190,3280,3370,3450,3531,3611,3691,22846,23027,23152,23259,23439,23562,23678,23781,24136,24241,24422,24547,24722,24870,24933,24995,25074,163673,172849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b56453fc745a8c0d03f83bec51002d5e\\transformed\\credentials-play-services-auth-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "5", "endColumns": "12", "endOffsets": "273"}, "to": {"startLines": "1877", "startColumns": "4", "startOffsets": "125568", "endLines": "1880", "endColumns": "12", "endOffsets": "125786"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\da5e41b10fafadce7ba510b91b313ba3\\transformed\\core-common-2.0.3\\res\\values\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "166", "endLines": "11", "endColumns": "8", "endOffsets": "571"}, "to": {"startLines": "1881", "startColumns": "4", "startOffsets": "125791", "endLines": "1888", "endColumns": "8", "endOffsets": "126196"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e01804c4f250705dd3d5d0bfed3c5af9\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "342", "startColumns": "4", "startOffsets": "21990", "endColumns": "82", "endOffsets": "22068"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,2217,2276,2335,2395,2455,2515,2575,2635,2695,2755,2815,2875,2935,2994,3054,3114,3174,3234,3294,3354,3414,3474,3534,3594,3653,3713,3773,3832,3891,3950,4009,4068,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,2271,2330,2390,2450,2510,2570,2630,2690,2750,2810,2870,2930,2989,3049,3109,3169,3229,3289,3349,3409,3469,3529,3589,3648,3708,3768,3827,3886,3945,4004,4063,4122,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,28,29,47,48,79,80,187,188,189,190,191,192,193,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,274,275,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,312,344,345,346,347,348,349,350,458,1830,1831,1835,1836,1840,1996,1997,2641,2647,2703,2736,2766,2799", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1483,1555,2894,2959,5049,5118,12315,12385,12453,12525,12595,12656,12730,13587,13648,13709,13771,13835,13897,13958,14026,14126,14186,14252,14325,14394,14451,14503,15018,15090,15166,15231,15290,15349,15409,15469,15529,15589,15649,15709,15769,15829,15889,15949,16008,16068,16128,16188,16248,16308,16368,16428,16488,16548,16608,16667,16727,16787,16846,16905,16964,17023,17082,17850,17885,18289,18344,18407,18462,18520,18578,18639,18702,18759,18810,18860,18921,18978,19044,19078,19113,19888,22116,22183,22255,22324,22393,22467,22539,31426,122144,122261,122462,122572,122773,134860,134932,156137,156340,158570,160301,161301,161983", "endLines": "9,28,29,47,48,79,80,187,188,189,190,191,192,193,203,204,205,206,207,208,209,210,211,212,213,214,215,216,217,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,274,275,283,284,285,286,287,288,289,290,291,292,293,294,295,296,297,298,312,344,345,346,347,348,349,350,458,1830,1834,1835,1839,1840,1996,1997,2646,2656,2735,2756,2798,2804", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1550,1638,2954,3020,5113,5176,12380,12448,12520,12590,12651,12725,12798,13643,13704,13766,13830,13892,13953,14021,14121,14181,14247,14320,14389,14446,14498,14560,15085,15161,15226,15285,15344,15404,15464,15524,15584,15644,15704,15764,15824,15884,15944,16003,16063,16123,16183,16243,16303,16363,16423,16483,16543,16603,16662,16722,16782,16841,16900,16959,17018,17077,17136,17880,17915,18339,18402,18457,18515,18573,18634,18697,18754,18805,18855,18916,18973,19039,19073,19108,19143,19953,22178,22250,22319,22388,22462,22534,22622,31492,122256,122457,122567,122768,122897,134927,134994,156335,156636,160296,160977,161978,162145"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f43be022de2035b39234c04c60821b4e\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "265,266,267,270,272,305,351,352,372,373,375,385,386,448,449,451,452,454,455,456,457,459,460,461,1536,1552,1555", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "17346,17420,17478,17656,17741,19451,22627,22692,25147,25213,25458,26356,26408,30909,30971,31095,31145,31252,31298,31344,31386,31497,31544,31580,99939,100919,101030", "endLines": "265,266,267,270,272,305,351,352,372,373,375,385,386,448,449,451,452,454,455,456,457,459,460,461,1538,1554,1558", "endColumns": "73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "17415,17473,17528,17702,17791,19499,22687,22741,25208,25309,25511,26403,26463,30966,31020,31140,31194,31293,31339,31381,31421,31539,31575,31665,100046,101025,101220"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f628f2f971a3d94a816342e8948e9fcc\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "269", "startColumns": "4", "startOffsets": "17590", "endColumns": "65", "endOffsets": "17651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a7d2fcfebb99ca0b7d2351010b4b169\\transformed\\play-services-basement-18.4.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "310,361", "startColumns": "4,4", "startOffsets": "19747,23786", "endColumns": "67,166", "endOffsets": "19810,23948"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ae8da29e7eccb1a8725377c96f852ddc\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "311,387,388,389,390,391,392,393,394,395,396,399,400,401,402,403,404,405,406,407,408,409,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,1539,1549", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19815,26468,26556,26642,26723,26807,26876,26941,27024,27130,27216,27336,27390,27459,27520,27589,27678,27773,27847,27944,28037,28135,28284,28375,28463,28559,28657,28721,28789,28876,28970,29037,29109,29181,29282,29391,29467,29536,29584,29650,29714,29788,29845,29902,29974,30024,30078,30149,30220,30290,30359,30417,30493,30564,30638,30724,30774,30844,100051,100766", "endLines": "311,387,388,389,390,391,392,393,394,395,398,399,400,401,402,403,404,405,406,407,408,411,412,413,414,415,416,417,418,419,420,421,422,423,424,425,426,427,428,429,430,431,432,433,434,435,436,437,438,439,440,441,442,443,444,445,446,447,1548,1551", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19883,26551,26637,26718,26802,26871,26936,27019,27125,27211,27331,27385,27454,27515,27584,27673,27768,27842,27939,28032,28130,28279,28370,28458,28554,28652,28716,28784,28871,28965,29032,29104,29176,29277,29386,29462,29531,29579,29645,29709,29783,29840,29897,29969,30019,30073,30144,30215,30285,30354,30412,30488,30559,30633,30719,30769,30839,30904,100761,100914"}}, {"source": "D:\\kotln project\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "42", "endOffsets": "54"}, "to": {"startLines": "343", "startColumns": "4", "startOffsets": "22073", "endColumns": "42", "endOffsets": "22111"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fecf2c807995b272b505dedc8b7a296b\\transformed\\navigation-common-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,15,21,27,30", "startColumns": "4,4,4,4,4", "startOffsets": "55,694,938,1185,1318", "endLines": "14,20,26,29,34", "endColumns": "24,24,24,24,24", "endOffsets": "689,933,1180,1313,1495"}, "to": {"startLines": "3019,3032,3038,3044,3053", "startColumns": "4,4,4,4,4", "startOffsets": "168941,169580,169824,170071,170434", "endLines": "3031,3037,3043,3046,3057", "endColumns": "24,24,24,24,24", "endOffsets": "169575,169819,170066,170199,170611"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\549dfb7dd035cf2830ebdc27ba100054\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,45,46,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,82,83,84,85,86,87,88,92,93,94,95,96,97,98,99,100,101,104,105,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,194,195,196,197,198,199,200,201,202,218,219,220,221,222,223,224,225,261,262,263,264,271,278,279,282,299,306,307,308,309,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,453,464,465,466,467,468,469,477,478,482,486,490,495,501,508,512,516,521,525,529,533,537,541,545,551,555,561,565,571,575,580,584,587,591,597,601,607,611,617,620,624,628,632,636,640,641,642,643,646,649,652,655,659,660,661,662,663,666,668,670,672,677,678,682,688,692,693,695,706,707,711,717,721,722,723,727,754,758,759,763,791,961,987,1158,1184,1215,1223,1229,1243,1265,1270,1275,1285,1294,1303,1307,1314,1322,1329,1330,1339,1342,1345,1349,1353,1357,1360,1361,1366,1371,1381,1386,1393,1399,1400,1403,1407,1412,1414,1416,1419,1422,1424,1428,1431,1438,1441,1444,1448,1450,1454,1456,1458,1460,1464,1472,1480,1492,1498,1507,1510,1521,1524,1525,1530,1531,1559,1628,1698,1699,1709,1718,1719,1721,1725,1728,1731,1734,1737,1740,1743,1746,1750,1753,1756,1759,1763,1766,1770,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1794,1796,1798,1799,1800,1801,1802,1803,1804,1805,1807,1808,1810,1811,1813,1815,1816,1818,1819,1820,1821,1822,1823,1825,1826,1827,1828,1829,1841,1843,1845,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1860,1862,1863,1864,1865,1866,1867,1869,1873,1889,1890,1891,1892,1893,1894,1898,1899,1900,1901,1903,1905,1907,1909,1911,1912,1913,1914,1916,1918,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1931,1934,1935,1936,1937,1939,1941,1942,1944,1945,1947,1949,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1964,1965,1966,1967,1969,1970,1971,1972,1973,1975,1977,1979,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,1998,2073,2076,2079,2082,2096,2109,2151,2180,2207,2216,2278,2637,2657,2685,2805,2829,2835,2854,2875,2999,3058,3064,3072,3078,3132,3164,3230,3250,3305,3317,3343", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,973,1035,1116,1177,1252,1328,1405,1643,1728,1810,1886,2004,2081,2159,2265,2371,2450,2779,2836,3696,3770,3845,3910,3976,4036,4097,4169,4242,4309,4377,4436,4495,4554,4613,4672,4726,4780,4833,4887,4941,4995,5181,5255,5334,5407,5481,5552,5624,5696,5910,5967,6025,6098,6172,6246,6321,6393,6466,6536,6697,6757,6860,6929,6998,7068,7142,7218,7282,7359,7435,7512,7577,7646,7723,7798,7867,7935,8012,8078,8139,8236,8301,8370,8469,8540,8599,8657,8714,8773,8837,8908,8980,9052,9124,9196,9263,9331,9399,9458,9521,9585,9675,9766,9826,9892,9959,10025,10095,10159,10212,10279,10340,10407,10520,10578,10641,10706,10771,10846,10919,10991,11040,11101,11162,11223,11285,11349,11413,11477,11542,11605,11665,11726,11792,11851,11911,11973,12044,12104,12803,12889,12976,13066,13153,13241,13323,13406,13496,14565,14617,14675,14720,14786,14850,14907,14964,17141,17198,17246,17295,17707,18040,18087,18243,19148,19504,19568,19630,19690,19958,20032,20102,20180,20234,20304,20389,20437,20483,20544,20607,20673,20737,20808,20871,20936,21000,21061,21122,21174,21247,21321,21390,21465,21539,21613,21754,31199,31781,31859,31949,32037,32133,32223,32805,32894,33141,33422,33674,33959,34352,34829,35051,35273,35549,35776,36006,36236,36466,36696,36923,37342,37568,37993,38223,38651,38870,39153,39361,39492,39719,40145,40370,40797,41018,41443,41563,41839,42140,42464,42755,43069,43206,43337,43442,43684,43851,44055,44263,44534,44646,44758,44863,44980,45194,45340,45480,45566,45914,46002,46248,46666,46915,46997,47095,47687,47787,48039,48463,48718,48812,48901,49138,51162,51404,51506,51759,53915,64447,65963,76594,78122,79879,80505,80925,81986,83251,83507,83743,84290,84784,85389,85587,86167,86731,87106,87224,87762,87919,88115,88388,88644,88814,88955,89019,89384,89751,90427,90691,91029,91382,91476,91662,91968,92230,92355,92482,92721,92932,93051,93244,93421,93876,94057,94179,94438,94551,94738,94840,94947,95076,95351,95859,96355,97232,97526,98096,98245,98977,99149,99233,99569,99661,101225,106471,111860,111922,112500,113084,113175,113288,113517,113677,113829,114000,114166,114335,114502,114665,114908,115078,115251,115422,115696,115895,116100,116430,116514,116610,116706,116804,116904,117006,117108,117210,117312,117414,117514,117610,117722,117851,117974,118105,118236,118334,118448,118542,118682,118816,118912,119024,119124,119240,119336,119448,119548,119688,119824,119988,120118,120276,120426,120567,120711,120846,120958,121108,121236,121364,121500,121632,121762,121892,122004,122902,123048,123192,123412,123478,123568,123644,123748,123838,123940,124048,124156,124256,124336,124428,124526,124636,124714,124820,124912,125016,125126,125248,125411,126201,126281,126381,126471,126581,126671,126912,127006,127112,127204,127304,127416,127530,127646,127762,127856,127970,128082,128184,128304,128426,128508,128612,128732,128858,128956,129050,129138,129250,129366,129488,129600,129775,129891,129977,130069,130181,130305,130372,130498,130566,130694,130838,130966,131035,131130,131245,131358,131457,131566,131677,131788,131889,131994,132094,132224,132315,132438,132532,132644,132730,132834,132930,133018,133136,133240,133344,133470,133558,133666,133766,133856,133966,134050,134152,134236,134290,134354,134460,134546,134656,134740,134999,137615,137733,137848,137928,138289,138826,140230,141574,142935,143323,146098,156002,156641,157998,162150,162901,163163,163678,164057,168335,170616,170845,171139,171354,172854,173704,176730,177474,179605,179945,181256", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,45,46,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,81,82,83,84,85,86,87,88,92,93,94,95,96,97,98,99,100,101,104,105,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,194,195,196,197,198,199,200,201,202,218,219,220,221,222,223,224,225,261,262,263,264,271,278,279,282,299,306,307,308,309,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,335,336,337,338,339,453,464,465,466,467,468,476,477,481,485,489,494,500,507,511,515,520,524,528,532,536,540,544,550,554,560,564,570,574,579,583,586,590,596,600,606,610,616,619,623,627,631,635,639,640,641,642,645,648,651,654,658,659,660,661,662,665,667,669,671,676,677,681,687,691,692,694,705,706,710,716,720,721,722,726,753,757,758,762,790,960,986,1157,1183,1214,1222,1228,1242,1264,1269,1274,1284,1293,1302,1306,1313,1321,1328,1329,1338,1341,1344,1348,1352,1356,1359,1360,1365,1370,1380,1385,1392,1398,1399,1402,1406,1411,1413,1415,1418,1421,1423,1427,1430,1437,1440,1443,1447,1449,1453,1455,1457,1459,1463,1471,1479,1491,1497,1506,1509,1520,1523,1524,1529,1530,1535,1627,1697,1698,1708,1717,1718,1720,1724,1727,1730,1733,1736,1739,1742,1745,1749,1752,1755,1758,1762,1765,1769,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1786,1787,1788,1789,1790,1791,1792,1793,1795,1797,1798,1799,1800,1801,1802,1803,1804,1806,1807,1809,1810,1812,1814,1815,1817,1818,1819,1820,1821,1822,1824,1825,1826,1827,1828,1829,1842,1844,1846,1848,1849,1850,1851,1852,1853,1854,1855,1856,1857,1858,1859,1861,1862,1863,1864,1865,1866,1868,1872,1876,1889,1890,1891,1892,1893,1897,1898,1899,1900,1902,1904,1906,1908,1910,1911,1912,1913,1915,1917,1919,1920,1921,1922,1923,1924,1925,1926,1927,1928,1929,1930,1933,1934,1935,1936,1938,1940,1941,1943,1944,1946,1948,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1960,1961,1963,1964,1965,1966,1968,1969,1970,1971,1972,1974,1976,1978,1980,1981,1982,1983,1984,1985,1986,1987,1988,1989,1990,1991,1992,1993,1994,1995,2072,2075,2078,2081,2095,2101,2118,2179,2206,2215,2277,2636,2640,2684,2702,2828,2834,2840,2874,2998,3018,3063,3067,3077,3112,3143,3229,3249,3304,3316,3342,3349", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,968,1030,1111,1172,1247,1323,1400,1478,1723,1805,1881,1957,2076,2154,2260,2366,2445,2525,2831,2889,3765,3840,3905,3971,4031,4092,4164,4237,4304,4372,4431,4490,4549,4608,4667,4721,4775,4828,4882,4936,4990,5044,5250,5329,5402,5476,5547,5619,5691,5764,5962,6020,6093,6167,6241,6316,6388,6461,6531,6602,6752,6813,6924,6993,7063,7137,7213,7277,7354,7430,7507,7572,7641,7718,7793,7862,7930,8007,8073,8134,8231,8296,8365,8464,8535,8594,8652,8709,8768,8832,8903,8975,9047,9119,9191,9258,9326,9394,9453,9516,9580,9670,9761,9821,9887,9954,10020,10090,10154,10207,10274,10335,10402,10515,10573,10636,10701,10766,10841,10914,10986,11035,11096,11157,11218,11280,11344,11408,11472,11537,11600,11660,11721,11787,11846,11906,11968,12039,12099,12167,12884,12971,13061,13148,13236,13318,13401,13491,13582,14612,14670,14715,14781,14845,14902,14959,15013,17193,17241,17290,17341,17736,18082,18131,18284,19175,19563,19625,19685,19742,20027,20097,20175,20229,20299,20384,20432,20478,20539,20602,20668,20732,20803,20866,20931,20995,21056,21117,21169,21242,21316,21385,21460,21534,21608,21749,21819,31247,31854,31944,32032,32128,32218,32800,32889,33136,33417,33669,33954,34347,34824,35046,35268,35544,35771,36001,36231,36461,36691,36918,37337,37563,37988,38218,38646,38865,39148,39356,39487,39714,40140,40365,40792,41013,41438,41558,41834,42135,42459,42750,43064,43201,43332,43437,43679,43846,44050,44258,44529,44641,44753,44858,44975,45189,45335,45475,45561,45909,45997,46243,46661,46910,46992,47090,47682,47782,48034,48458,48713,48807,48896,49133,51157,51399,51501,51754,53910,64442,65958,76589,78117,79874,80500,80920,81981,83246,83502,83738,84285,84779,85384,85582,86162,86726,87101,87219,87757,87914,88110,88383,88639,88809,88950,89014,89379,89746,90422,90686,91024,91377,91471,91657,91963,92225,92350,92477,92716,92927,93046,93239,93416,93871,94052,94174,94433,94546,94733,94835,94942,95071,95346,95854,96350,97227,97521,98091,98240,98972,99144,99228,99564,99656,99934,106466,111855,111917,112495,113079,113170,113283,113512,113672,113824,113995,114161,114330,114497,114660,114903,115073,115246,115417,115691,115890,116095,116425,116509,116605,116701,116799,116899,117001,117103,117205,117307,117409,117509,117605,117717,117846,117969,118100,118231,118329,118443,118537,118677,118811,118907,119019,119119,119235,119331,119443,119543,119683,119819,119983,120113,120271,120421,120562,120706,120841,120953,121103,121231,121359,121495,121627,121757,121887,121999,122139,123043,123187,123325,123473,123563,123639,123743,123833,123935,124043,124151,124251,124331,124423,124521,124631,124709,124815,124907,125011,125121,125243,125406,125563,126276,126376,126466,126576,126666,126907,127001,127107,127199,127299,127411,127525,127641,127757,127851,127965,128077,128179,128299,128421,128503,128607,128727,128853,128951,129045,129133,129245,129361,129483,129595,129770,129886,129972,130064,130176,130300,130367,130493,130561,130689,130833,130961,131030,131125,131240,131353,131452,131561,131672,131783,131884,131989,132089,132219,132310,132433,132527,132639,132725,132829,132925,133013,133131,133235,133339,133465,133553,133661,133761,133851,133961,134045,134147,134231,134285,134349,134455,134541,134651,134735,134855,137610,137728,137843,137923,138284,138517,139338,141569,142930,143318,146093,155997,156132,157993,158565,162896,163158,163358,164052,168330,168936,170840,170991,171349,172432,173161,176725,177469,179600,179940,181251,181454"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\520622b0edae59466318f8730d724c96\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "300", "startColumns": "4", "startOffsets": "19180", "endColumns": "42", "endOffsets": "19218"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e71863d474cbeda869e988a1e7d78c0\\transformed\\credentials-1.2.0-rc01\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,137", "endColumns": "81,83", "endOffsets": "132,216"}, "to": {"startLines": "340,341", "startColumns": "4,4", "startOffsets": "21824,21906", "endColumns": "81,83", "endOffsets": "21901,21985"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1544f1b139521f791780ab7f198bbeeb\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "280,301", "startColumns": "4,4", "startOffsets": "18136,19223", "endColumns": "41,59", "endOffsets": "18173,19278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2647642c7a90f8fe9c27a90510e85791\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "303", "startColumns": "4", "startOffsets": "19337", "endColumns": "49", "endOffsets": "19382"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\c8958ab760b9493832dc38f3c8c6661e\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,3068", "startColumns": "4,4,4", "startOffsets": "250,511,170996", "endLines": "7,17,3071", "endColumns": "11,11,24", "endOffsets": "397,813,171134"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09ab614e376350ca0889dc2dbf11206f\\transformed\\browser-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,113,179,242,304,375,447,515,582,661", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "108,174,237,299,370,442,510,577,656,725"}, "to": {"startLines": "41,42,43,44,185,186,371,376,377,378", "startColumns": "4,4,4,4,4,4,4,4,4,4", "startOffsets": "2530,2588,2654,2717,12172,12243,25079,25516,25583,25662", "endColumns": "57,65,62,61,70,71,67,66,78,68", "endOffsets": "2583,2649,2712,2774,12238,12310,25142,25578,25657,25726"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\64ef9d114ab110ad0973e98d0ce3706c\\transformed\\fragment-1.5.7\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,10", "startColumns": "4,4,4,4,4", "startOffsets": "55,112,177,241,411", "endLines": "2,3,4,9,13", "endColumns": "56,64,63,24,24", "endOffsets": "107,172,236,406,555"}, "to": {"startLines": "268,281,304,2757,2762", "startColumns": "4,4,4,4,4", "startOffsets": "17533,18178,19387,160982,161152", "endLines": "268,281,304,2761,2765", "endColumns": "56,64,63,24,24", "endOffsets": "17585,18238,19446,161147,161296"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\108a50cab81f5504821575488c5acb51\\transformed\\navigation-runtime-2.8.4\\res\\values\\values.xml", "from": {"startLines": "2,3,10,13", "startColumns": "4,4,4,4", "startOffsets": "55,108,412,527", "endLines": "2,9,12,15", "endColumns": "52,24,24,24", "endOffsets": "103,407,522,637"}, "to": {"startLines": "276,2102,3047,3050", "startColumns": "4,4,4,4", "startOffsets": "17920,138522,170204,170319", "endLines": "276,2108,3049,3052", "endColumns": "52,24,24,24", "endOffsets": "17968,138821,170314,170429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e2cdf0205b6d494f5c454474d997a0c7\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "273,277", "startColumns": "4,4", "startOffsets": "17796,17973", "endColumns": "53,66", "endOffsets": "17845,18035"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7494369eca28e761a3742b866990c6e1\\transformed\\appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2119,2135,2141,3144,3160", "startColumns": "4,4,4,4,4", "startOffsets": "139343,139768,139946,173166,173577", "endLines": "2134,2140,2150,3159,3163", "endColumns": "24,24,24,24,24", "endOffsets": "139763,139941,140225,173572,173699"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e099880564deb176147b2205fdc84816\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "462,463", "startColumns": "4,4", "startOffsets": "31670,31726", "endColumns": "55,54", "endOffsets": "31721,31776"}}, {"source": "D:\\kotln project\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "34,89,90,91,102,103,106", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1962,5769,5816,5863,6607,6652,6818", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1999,5811,5858,5905,6647,6692,6855"}}, {"source": "D:\\kotln project\\app\\build\\generated\\res\\processDebugGoogleServices\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,199,316,398,502,611,731,824", "endColumns": "143,116,81,103,108,119,92,69", "endOffsets": "194,311,393,497,606,726,819,889"}, "to": {"startLines": "374,379,380,381,382,383,384,450", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "25314,25731,25848,25930,26034,26143,26263,31025", "endColumns": "143,116,81,103,108,119,92,69", "endOffsets": "25453,25843,25925,26029,26138,26258,26351,31090"}}, {"source": "D:\\kotln project\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "82", "endOffsets": "134"}, "to": {"startLines": "1847", "startColumns": "4", "startOffsets": "123330", "endColumns": "81", "endOffsets": "123407"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9c5d551a6b0c87ae8b7dd48bec154e96\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "302", "startColumns": "4", "startOffsets": "19283", "endColumns": "53", "endOffsets": "19332"}}]}]}