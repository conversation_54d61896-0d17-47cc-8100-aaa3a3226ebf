#Fri May 30 03:57:52 PDT 2025
path.4=2/classes.dex
path.3=15/classes.dex
path.2=10/classes.dex
path.1=0/classes.dex
path.7=classes3.dex
path.6=classes2.dex
path.5=7/classes.dex
path.0=classes.dex
base.4=D\:\\kotln project\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\2\\classes.dex
base.3=D\:\\kotln project\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\15\\classes.dex
base.2=D\:\\kotln project\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\10\\classes.dex
base.1=D\:\\kotln project\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\0\\classes.dex
base.0=D\:\\kotln project\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes.dex
renamed.3=classes4.dex
renamed.2=classes3.dex
renamed.1=classes2.dex
renamed.0=classes.dex
renamed.7=classes8.dex
renamed.6=classes7.dex
base.7=D\:\\kotln project\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes3.dex
renamed.5=classes6.dex
base.6=D\:\\kotln project\\app\\build\\intermediates\\dex\\debug\\mergeExtDexDebug\\classes2.dex
renamed.4=classes5.dex
base.5=D\:\\kotln project\\app\\build\\intermediates\\dex\\debug\\mergeProjectDexDebug\\7\\classes.dex
