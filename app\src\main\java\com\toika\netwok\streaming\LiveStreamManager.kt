package com.toika.netwok.streaming

import android.content.Context
import android.util.Log
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.database.*
import com.toika.netwok.data.models.LiveStream
import com.toika.netwok.data.models.StreamViewer
import com.toika.netwok.data.models.StreamMessage
import com.toika.netwok.data.models.MessageType
import com.toika.netwok.webrtc.WebRTCManager
import kotlinx.coroutines.tasks.await
import java.util.UUID

class LiveStreamManager(private val context: Context) {
    
    companion object {
        private const val TAG = "LiveStreamManager"
        private const val STREAMS_PATH = "live_streams"
        private const val VIEWERS_PATH = "stream_viewers"
        private const val MESSAGES_PATH = "stream_messages"
        private const val USER_STREAMS_PATH = "user_streams"
    }
    
    private val database = FirebaseDatabase.getInstance()
    private val auth = FirebaseAuth.getInstance()
    private val webRTCManager = WebRTCManager(context)
    
    private var currentStream: LiveStream? = null
    private var isStreaming = false
    private var isViewing = false
    
    // Callbacks
    var onStreamStarted: ((LiveStream) -> Unit)? = null
    var onStreamEnded: (() -> Unit)? = null
    var onViewerCountChanged: ((Int) -> Unit)? = null
    var onNewMessage: ((StreamMessage) -> Unit)? = null
    var onStreamListUpdated: ((List<LiveStream>) -> Unit)? = null
    
    // بدء البث المباشر
    suspend fun startLiveStream(
        title: String,
        description: String = "",
        isPrivate: Boolean = false,
        allowedViewers: List<String> = emptyList()
    ): Result<LiveStream> {
        return try {
            val currentUser = auth.currentUser ?: return Result.failure(Exception("User not authenticated"))
            
            // إنشاء معرف البث
            val streamId = UUID.randomUUID().toString()
            
            // إنشاء بيانات البث
            val stream = LiveStream(
                streamId = streamId,
                hostId = currentUser.uid,
                hostName = currentUser.displayName ?: "مستخدم",
                hostProfileImage = currentUser.photoUrl?.toString() ?: "",
                title = title,
                description = description,
                isActive = true,
                startTime = System.currentTimeMillis(),
                isPrivate = isPrivate,
                allowedViewers = allowedViewers,
                streamUrl = "webrtc://$streamId", // URL مؤقت
                chatEnabled = true,
                recordingEnabled = false
            )
            
            // حفظ البث في Firebase
            database.getReference(STREAMS_PATH)
                .child(streamId)
                .setValue(stream)
                .await()
            
            // إضافة البث لقائمة بثوث المستخدم
            database.getReference(USER_STREAMS_PATH)
                .child(currentUser.uid)
                .child(streamId)
                .setValue(true)
                .await()
            
            // بدء WebRTC
            webRTCManager.startLocalStream()
            
            currentStream = stream
            isStreaming = true
            
            // مراقبة عدد المشاهدين
            startViewerCountMonitoring(streamId)
            
            onStreamStarted?.invoke(stream)
            Log.d(TAG, "Live stream started: $streamId")
            
            Result.success(stream)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start live stream: ${e.message}")
            Result.failure(e)
        }
    }
    
    // إنهاء البث المباشر
    suspend fun endLiveStream(): Result<Unit> {
        return try {
            val stream = currentStream ?: return Result.failure(Exception("No active stream"))
            
            // تحديث حالة البث
            val updatedStream = stream.copy(
                isActive = false,
                endTime = System.currentTimeMillis()
            )
            
            database.getReference(STREAMS_PATH)
                .child(stream.streamId)
                .setValue(updatedStream)
                .await()
            
            // إزالة جميع المشاهدين
            database.getReference(VIEWERS_PATH)
                .child(stream.streamId)
                .removeValue()
                .await()
            
            // إيقاف WebRTC
            webRTCManager.stopLocalStream()
            
            currentStream = null
            isStreaming = false
            
            onStreamEnded?.invoke()
            Log.d(TAG, "Live stream ended: ${stream.streamId}")
            
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to end live stream: ${e.message}")
            Result.failure(e)
        }
    }
    
    // الانضمام لمشاهدة البث
    suspend fun joinStream(streamId: String): Result<LiveStream> {
        return try {
            val currentUser = auth.currentUser ?: return Result.failure(Exception("User not authenticated"))
            
            // الحصول على بيانات البث
            val streamSnapshot = database.getReference(STREAMS_PATH)
                .child(streamId)
                .get()
                .await()
            
            val stream = streamSnapshot.getValue(LiveStream::class.java)
                ?: return Result.failure(Exception("Stream not found"))
            
            if (!stream.isActive) {
                return Result.failure(Exception("Stream is not active"))
            }
            
            // التحقق من الصلاحيات للبث الخاص
            if (stream.isPrivate && !stream.allowedViewers.contains(currentUser.uid)) {
                return Result.failure(Exception("Access denied to private stream"))
            }
            
            // إضافة المشاهد
            val viewer = StreamViewer(
                viewerId = currentUser.uid,
                viewerName = currentUser.displayName ?: "مستخدم",
                viewerProfileImage = currentUser.photoUrl?.toString() ?: "",
                joinedAt = System.currentTimeMillis(),
                isActive = true
            )
            
            database.getReference(VIEWERS_PATH)
                .child(streamId)
                .child(currentUser.uid)
                .setValue(viewer)
                .await()
            
            // إرسال رسالة انضمام
            sendSystemMessage(streamId, "${viewer.viewerName} انضم للبث", MessageType.JOIN)
            
            isViewing = true
            
            // مراقبة الرسائل
            startMessageMonitoring(streamId)
            
            Log.d(TAG, "Joined stream: $streamId")
            Result.success(stream)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to join stream: ${e.message}")
            Result.failure(e)
        }
    }
    
    // مغادرة البث
    suspend fun leaveStream(streamId: String): Result<Unit> {
        return try {
            val currentUser = auth.currentUser ?: return Result.failure(Exception("User not authenticated"))
            
            // إزالة المشاهد
            database.getReference(VIEWERS_PATH)
                .child(streamId)
                .child(currentUser.uid)
                .removeValue()
                .await()
            
            // إرسال رسالة مغادرة
            val userName = currentUser.displayName ?: "مستخدم"
            sendSystemMessage(streamId, "$userName غادر البث", MessageType.LEAVE)
            
            isViewing = false
            
            Log.d(TAG, "Left stream: $streamId")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to leave stream: ${e.message}")
            Result.failure(e)
        }
    }
    
    // إرسال رسالة في الدردشة
    suspend fun sendMessage(streamId: String, message: String): Result<Unit> {
        return try {
            val currentUser = auth.currentUser ?: return Result.failure(Exception("User not authenticated"))
            
            val streamMessage = StreamMessage(
                messageId = UUID.randomUUID().toString(),
                streamId = streamId,
                senderId = currentUser.uid,
                senderName = currentUser.displayName ?: "مستخدم",
                senderProfileImage = currentUser.photoUrl?.toString() ?: "",
                message = message,
                timestamp = System.currentTimeMillis(),
                messageType = MessageType.TEXT
            )
            
            database.getReference(MESSAGES_PATH)
                .child(streamId)
                .child(streamMessage.messageId)
                .setValue(streamMessage)
                .await()
            
            Log.d(TAG, "Message sent to stream: $streamId")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send message: ${e.message}")
            Result.failure(e)
        }
    }
    
    // إرسال رسالة نظام
    private suspend fun sendSystemMessage(streamId: String, message: String, type: MessageType) {
        try {
            val systemMessage = StreamMessage(
                messageId = UUID.randomUUID().toString(),
                streamId = streamId,
                senderId = "system",
                senderName = "النظام",
                message = message,
                timestamp = System.currentTimeMillis(),
                messageType = type
            )
            
            database.getReference(MESSAGES_PATH)
                .child(streamId)
                .child(systemMessage.messageId)
                .setValue(systemMessage)
                .await()
        } catch (e: Exception) {
            Log.e(TAG, "Failed to send system message: ${e.message}")
        }
    }
    
    // الحصول على قائمة البثوث النشطة
    fun getActiveStreams(callback: (List<LiveStream>) -> Unit) {
        database.getReference(STREAMS_PATH)
            .orderByChild("isActive")
            .equalTo(true)
            .addValueEventListener(object : ValueEventListener {
                override fun onDataChange(snapshot: DataSnapshot) {
                    val streams = mutableListOf<LiveStream>()
                    for (child in snapshot.children) {
                        child.getValue(LiveStream::class.java)?.let { stream ->
                            streams.add(stream)
                        }
                    }
                    callback(streams.sortedByDescending { it.startTime })
                }
                
                override fun onCancelled(error: DatabaseError) {
                    Log.e(TAG, "Failed to get active streams: ${error.message}")
                    callback(emptyList())
                }
            })
    }
    
    // مراقبة عدد المشاهدين
    private fun startViewerCountMonitoring(streamId: String) {
        database.getReference(VIEWERS_PATH)
            .child(streamId)
            .addValueEventListener(object : ValueEventListener {
                override fun onDataChange(snapshot: DataSnapshot) {
                    val viewerCount = snapshot.childrenCount.toInt()
                    onViewerCountChanged?.invoke(viewerCount)
                    
                    // تحديث عدد المشاهدين في البث
                    database.getReference(STREAMS_PATH)
                        .child(streamId)
                        .child("viewerCount")
                        .setValue(viewerCount)
                }
                
                override fun onCancelled(error: DatabaseError) {
                    Log.e(TAG, "Failed to monitor viewer count: ${error.message}")
                }
            })
    }
    
    // مراقبة الرسائل
    private fun startMessageMonitoring(streamId: String) {
        database.getReference(MESSAGES_PATH)
            .child(streamId)
            .orderByChild("timestamp")
            .limitToLast(50)
            .addChildEventListener(object : ChildEventListener {
                override fun onChildAdded(snapshot: DataSnapshot, previousChildName: String?) {
                    snapshot.getValue(StreamMessage::class.java)?.let { message ->
                        onNewMessage?.invoke(message)
                    }
                }
                
                override fun onChildChanged(snapshot: DataSnapshot, previousChildName: String?) {}
                override fun onChildRemoved(snapshot: DataSnapshot) {}
                override fun onChildMoved(snapshot: DataSnapshot, previousChildName: String?) {}
                override fun onCancelled(error: DatabaseError) {
                    Log.e(TAG, "Failed to monitor messages: ${error.message}")
                }
            })
    }
    
    // الحصول على قائمة المشاهدين
    fun getViewers(streamId: String, callback: (List<StreamViewer>) -> Unit) {
        database.getReference(VIEWERS_PATH)
            .child(streamId)
            .addValueEventListener(object : ValueEventListener {
                override fun onDataChange(snapshot: DataSnapshot) {
                    val viewers = mutableListOf<StreamViewer>()
                    for (child in snapshot.children) {
                        child.getValue(StreamViewer::class.java)?.let { viewer ->
                            if (viewer.isActive) {
                                viewers.add(viewer)
                            }
                        }
                    }
                    callback(viewers.sortedBy { it.joinedAt })
                }
                
                override fun onCancelled(error: DatabaseError) {
                    Log.e(TAG, "Failed to get viewers: ${error.message}")
                    callback(emptyList())
                }
            })
    }
    
    // تبديل الكاميرا
    fun switchCamera() {
        webRTCManager.switchCamera()
    }
    
    // تفعيل/إيقاف الصوت
    fun toggleAudio(enabled: Boolean) {
        webRTCManager.toggleAudio(enabled)
    }
    
    // تفعيل/إيقاف الفيديو
    fun toggleVideo(enabled: Boolean) {
        webRTCManager.toggleVideo(enabled)
    }

    // دوال مشاركة الشاشة
    suspend fun startScreenShare(data: android.content.Intent?): Result<Unit> {
        return try {
            if (data == null) {
                return Result.failure(Exception("بيانات مشاركة الشاشة غير صحيحة"))
            }

            // بدء مشاركة الشاشة مع WebRTC
            webRTCManager.startScreenShare(data)

            // تحديث حالة البث في Firebase
            currentStream?.let { stream ->
                val updates = mapOf(
                    "screenSharingActive" to true,
                    "updatedAt" to System.currentTimeMillis()
                )
                database.getReference(STREAMS_PATH).child(stream.streamId).updateChildren(updates)
            }

            Log.d(TAG, "Screen sharing started successfully")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to start screen sharing: ${e.message}")
            Result.failure(e)
        }
    }

    suspend fun stopScreenShare(): Result<Unit> {
        return try {
            // إيقاف مشاركة الشاشة
            webRTCManager.stopScreenShare()

            // تحديث حالة البث في Firebase
            currentStream?.let { stream ->
                val updates = mapOf(
                    "screenSharingActive" to false,
                    "updatedAt" to System.currentTimeMillis()
                )
                database.getReference(STREAMS_PATH).child(stream.streamId).updateChildren(updates)
            }

            Log.d(TAG, "Screen sharing stopped successfully")
            Result.success(Unit)
        } catch (e: Exception) {
            Log.e(TAG, "Failed to stop screen sharing: ${e.message}")
            Result.failure(e)
        }
    }

    // تنظيف الموارد
    fun dispose() {
        webRTCManager.dispose()
    }
}
