# 🧪 دليل اختبار التطبيق - المحاكي والجوال الحقيقي

## 📱 **APK الجاهز:**
```
📍 المكان: D:\kotln project\toika-app.apk
📊 الحجم: ~16 MB
🔧 الميزات: مجموعة فيديو، بحث الغرف، Firebase
```

## 🚀 **الاختبار في المحاكي:**

### **✅ تم تثبيت التطبيق في المحاكي:**
- Device: emulator-5554
- Status: ✅ مثبت ومفتوح

### **📺 خطوات إنشاء مجموعة:**
```
1. افتح التطبيق في المحاكي
2. سجل دخول أو أنشئ حساب
3. اضغط "📺 إنشاء مجموعة فيديو"
4. ستظهر شاشة البث مع كود الغرفة
5. اكتب كود الغرفة (مثال: ABC12345)
6. اضغط زر الرجوع (←) - البث يبقى نشط
```

## 📲 **الاختبار في الجوال الحقيقي:**

### **📥 تثبيت التطبيق:**

#### **الطريقة 1 - USB Debugging:**
```
1. في الجوال:
   - Settings → About Phone
   - اضغط "Build Number" 7 مرات
   - Settings → Developer Options
   - فعّل "USB Debugging"

2. وصل الجوال بالكمبيوتر
3. شغل: adb devices
4. إذا ظهر جوالك: adb -s [DEVICE_ID] install toika-app.apk
```

#### **الطريقة 2 - تثبيت يدوي:**
```
1. انسخ ملف "toika-app.apk" للجوال
2. في الجوال: Settings → Security
3. فعّل "Install from Unknown Sources"
4. افتح ملف APK وثبته
```

### **🔍 خطوات الانضمام للمجموعة:**
```
1. افتح التطبيق في الجوال
2. سجل دخول بحساب مختلف عن المحاكي
3. أدخل كود الغرفة من المحاكي
4. اضغط "انضمام للغرفة"
5. ✅ يجب أن يجد الغرفة وينضم!
```

## 🎯 **سيناريوهات الاختبار:**

### **🧪 السيناريو الأساسي:**
```
المحاكي (المضيف):
├── إنشاء مجموعة فيديو
├── كود الغرفة: ABC12345
├── الرجوع للصفحة الرئيسية
└── البث يبقى نشط ✅

الجوال (الصديق):
├── إدخال كود: ABC12345
├── انضمام للغرفة
├── العثور على الغرفة ✅
└── الانتقال لشاشة المشاهدة ✅
```

### **🔄 السيناريو المتقدم:**
```
1. المضيف ينشئ مجموعة
2. الصديق ينضم
3. المضيف يعود لشاشة البث
4. يرى الصديق في قائمة المشاهدين ✅
5. يمكن مشاركة الشاشة ✅
6. يمكن إنهاء البث ✅
```

## 📊 **مراقبة النتائج:**

### **🔍 Logs المتوقعة (نجاح):**
```
🔍 Searching for room with code: ABC12345
📊 Found X total streams
🔍 Checking stream: ABC12345XXXXXXXX
📋 Stream details: isActive=true, title=مجموعة فيديو
🔑 Comparing codes: 'ABC12345' vs 'ABC12345'
✅ Room found! Stream: ABC12345XXXXXXXX
✅ Successfully joined room: ABC12345XXXXXXXX
```

### **❌ Logs المتوقعة (فشل):**
```
🔍 Searching for room with code: WRONG123
📊 Found X total streams
❌ Room not found for code: WRONG123
🔄 Trying alternative search method...
❌ Direct search also failed
```

## 🛠️ **استكشاف الأخطاء:**

### **❌ "الكود خطأ" في الجوال:**
```
الأسباب المحتملة:
1. الكود مكتوب خطأ
2. المضيف أنهى البث (زر 📞)
3. مشكلة في الاتصال بالإنترنت
4. المضيف والصديق في حسابات مختلفة (طبيعي)

الحلول:
1. تأكد من الكود الصحيح
2. تأكد أن المضيف لم ينهِ البث
3. تأكد من الاتصال بالإنترنت
4. راقب logs للتفاصيل
```

### **📱 التطبيق لا يثبت في الجوال:**
```
الأسباب:
1. "Unknown Sources" غير مفعل
2. مساحة غير كافية
3. إصدار Android قديم

الحلول:
1. فعّل "Install from Unknown Sources"
2. احذف تطبيقات غير مهمة
3. استخدم Android 7.0+ على الأقل
```

## 🎯 **النتائج المتوقعة:**

### **✅ نجاح الاختبار:**
```
1. المحاكي: إنشاء مجموعة بنجاح
2. الجوال: العثور على الغرفة
3. الجوال: الانضمام بنجاح
4. المحاكي: رؤية الصديق في المشاهدين
5. كلاهما: تجربة مستخدم سلسة
```

### **🎉 علامات النجاح:**
- لا توجد رسائل "الكود خطأ"
- الانتقال السلس بين الشاشات
- ظهور المشاهدين في القائمة
- إمكانية مشاركة الشاشة
- استقرار التطبيق

## 📞 **للمساعدة:**
إذا واجهت أي مشاكل:
1. راقب logs في المحاكي
2. تأكد من الاتصال بالإنترنت
3. جرب إعادة إنشاء المجموعة
4. تأكد من تسجيل الدخول في كلا الجهازين

**🎯 الهدف: اختبار ناجح للبحث عن الغرف والانضمام إليها!**
