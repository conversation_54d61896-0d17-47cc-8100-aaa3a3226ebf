{"logs": [{"outputFile": "com.toika.netwok.app-mergeDebugResources-72:/values-ro/values-ro.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4a7d2fcfebb99ca0b7d2351010b4b169\\transformed\\play-services-basement-18.4.0\\res\\values-ro\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "139", "endOffsets": "334"}, "to": {"startLines": "48", "startColumns": "4", "startOffsets": "5011", "endColumns": "143", "endOffsets": "5150"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e71863d474cbeda869e988a1e7d78c0\\transformed\\credentials-1.2.0-rc01\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,165", "endColumns": "109,120", "endOffsets": "160,281"}, "to": {"startLines": "29,30", "startColumns": "4,4", "startOffsets": "2851,2961", "endColumns": "109,120", "endOffsets": "2956,3077"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\549dfb7dd035cf2830ebdc27ba100054\\transformed\\appcompat-1.1.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,918,1009,1101,1196,1290,1391,1484,1579,1673,1764,1856,1938,2050,2158,2258,2372,2478,2584,2748,2851", "endColumns": "120,103,112,83,103,120,84,80,90,91,94,93,100,92,94,93,90,91,81,111,107,99,113,105,105,163,102,82", "endOffsets": "221,325,438,522,626,747,832,913,1004,1096,1191,1285,1386,1479,1574,1668,1759,1851,1933,2045,2153,2253,2367,2473,2579,2743,2846,2929"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,128", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,330,443,527,631,752,837,918,1009,1101,1196,1290,1391,1484,1579,1673,1764,1856,1938,2050,2158,2258,2372,2478,2584,2748,13846", "endColumns": "120,103,112,83,103,120,84,80,90,91,94,93,100,92,94,93,90,91,81,111,107,99,113,105,105,163,102,82", "endOffsets": "221,325,438,522,626,747,832,913,1004,1096,1191,1285,1386,1479,1574,1668,1759,1851,1933,2045,2153,2253,2367,2473,2579,2743,2846,13924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\ae8da29e7eccb1a8725377c96f852ddc\\transformed\\material3-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,185,312,429,555,665,762,876,1013,1133,1276,1360,1462,1557,1655,1775,1902,2009,2147,2283,2424,2600,2737,2856,2979,3105,3201,3297,3424,3565,3665,3770,3881,4021,4167,4279,4383,4459,4554,4646,4753,4839,4926,5027,5109,5192,5291,5395,5490,5591,5678,5789,5889,5995,6116,6198,6313", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "180,307,424,550,660,757,871,1008,1128,1271,1355,1457,1552,1650,1770,1897,2004,2142,2278,2419,2595,2732,2851,2974,3100,3196,3292,3419,3560,3660,3765,3876,4016,4162,4274,4378,4454,4549,4641,4748,4834,4921,5022,5104,5187,5286,5390,5485,5586,5673,5784,5884,5990,6111,6193,6308,6412"}, "to": {"startLines": "67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "7133,7263,7390,7507,7633,7743,7840,7954,8091,8211,8354,8438,8540,8635,8733,8853,8980,9087,9225,9361,9502,9678,9815,9934,10057,10183,10279,10375,10502,10643,10743,10848,10959,11099,11245,11357,11461,11537,11632,11724,11831,11917,12004,12105,12187,12270,12369,12473,12568,12669,12756,12867,12967,13073,13194,13276,13391", "endColumns": "129,126,116,125,109,96,113,136,119,142,83,101,94,97,119,126,106,137,135,140,175,136,118,122,125,95,95,126,140,99,104,110,139,145,111,103,75,94,91,106,85,86,100,81,82,98,103,94,100,86,110,99,105,120,81,114,103", "endOffsets": "7258,7385,7502,7628,7738,7835,7949,8086,8206,8349,8433,8535,8630,8728,8848,8975,9082,9220,9356,9497,9673,9810,9929,10052,10178,10274,10370,10497,10638,10738,10843,10954,11094,11240,11352,11456,11532,11627,11719,11826,11912,11999,12100,12182,12265,12364,12468,12563,12664,12751,12862,12962,13068,13189,13271,13386,13490"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7661eb1439e484e98fea3778baf664e3\\transformed\\play-services-base-18.5.0\\res\\values-ro\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,297,453,579,685,832,958,1077,1182,1340,1447,1602,1731,1873,2035,2100,2164", "endColumns": "103,155,125,105,146,125,118,104,157,106,154,128,141,161,64,63,78", "endOffsets": "296,452,578,684,831,957,1076,1181,1339,1446,1601,1730,1872,2034,2099,2163,2242"}, "to": {"startLines": "40,41,42,43,44,45,46,47,49,50,51,52,53,54,55,56,57", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3990,4098,4258,4388,4498,4649,4779,4902,5155,5317,5428,5587,5720,5866,6032,6101,6169", "endColumns": "107,159,129,109,150,129,122,108,161,110,158,132,145,165,68,67,82", "endOffsets": "4093,4253,4383,4493,4644,4774,4897,5006,5312,5423,5582,5715,5861,6027,6096,6164,6247"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\e099880564deb176147b2205fdc84816\\transformed\\foundation-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,150", "endColumns": "94,99", "endOffsets": "145,245"}, "to": {"startLines": "137,138", "startColumns": "4,4", "startOffsets": "14590,14685", "endColumns": "94,99", "endOffsets": "14680,14780"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\4271afb7208b91aba51d66f2d79dd50a\\transformed\\core-1.13.1\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,355,454,556,665,782", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "148,250,350,449,551,660,777,878"}, "to": {"startLines": "31,32,33,34,35,36,37,133", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3082,3180,3282,3382,3481,3583,3692,14230", "endColumns": "97,101,99,98,101,108,116,100", "endOffsets": "3175,3277,3377,3476,3578,3687,3804,14326"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\09ab614e376350ca0889dc2dbf11206f\\transformed\\browser-1.4.0\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,162,264,379", "endColumns": "106,101,114,104", "endOffsets": "157,259,374,479"}, "to": {"startLines": "58,62,63,64", "startColumns": "4,4,4,4", "startOffsets": "6252,6646,6748,6863", "endColumns": "106,101,114,104", "endOffsets": "6354,6743,6858,6963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f43be022de2035b39234c04c60821b4e\\transformed\\ui-release\\res\\values-ro\\values-ro.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,202,286,383,485,573,651,738,829,911,999,1089,1162,1236,1315,1390,1467,1534", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "197,281,378,480,568,646,733,824,906,994,1084,1157,1231,1310,1385,1462,1529,1644"}, "to": {"startLines": "38,39,59,60,61,65,66,124,125,126,127,129,130,131,132,134,135,136", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3809,3906,6359,6456,6558,6968,7046,13495,13586,13668,13756,13929,14002,14076,14155,14331,14408,14475", "endColumns": "96,83,96,101,87,77,86,90,81,87,89,72,73,78,74,76,66,114", "endOffsets": "3901,3985,6451,6553,6641,7041,7128,13581,13663,13751,13841,13997,14071,14150,14225,14403,14470,14585"}}]}]}